package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;

/**
 * 门店客户地址表
 *
 * <AUTHOR>
 * @date 2025-07-11 10:34:35
 */
@Data
@TenantTable
@TableName("tms_store_customer_address")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "门店客户地址表")
public class TmsStoreCustomerAddressEntity extends Model<TmsStoreCustomerAddressEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 联系人
	*/
    @Schema(description="联系人")
    private String contactName;

	/**
	* 联系电话
	*/
    @Schema(description="联系电话")
    private String contactPhone;

	/**
	* 详细地址
	*/
    @Schema(description="详细地址")
    private String detailAddress;

	/**
	* 国家
	*/
    @Schema(description="国家")
    private String country;

	/**
	* 省份
	*/
    @Schema(description="省份")
    private String province;

	/**
	* 城市
	*/
    @Schema(description="城市")
    private String city;

	/**
	* 邮政编码
	*/
    @Schema(description="邮政编码")
    private String postalCode;

	/**
	* 状态
	*/
    @Schema(description="状态")
    private Integer status;

	/**
	* 备注
	*/
    @Schema(description="备注")
    private String remark;

	/**
	* 乐观锁
	*/
    @Schema(description="乐观锁")
    private Long revision;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	/**
	* 删除标志
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标志")
    private String delFlag;

	/**
	* 租户号
	*/
    @Schema(description="租户号")
    private Long tenantId;
}