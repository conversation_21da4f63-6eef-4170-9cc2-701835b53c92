package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 包裹基础运费表-模版明细
 *
 * <AUTHOR>
 * @date 2025-03-07 14:50:41
 */
@Data
@TenantTable
@TableName("tms_basic_freight_pkg_detail")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "包裹基础运费表-模版明细")
public class TmsBasicFreightPkgDetailEntity extends BaseLogicEntity<TmsBasicFreightPkgDetailEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 区域
	*/
    @Schema(description="区域")
    private String region;

	/**
	* 开始重量
	*/
    @Schema(description="开始重量")
    private BigDecimal startWeight;

	/**
	* 结束重量
	*/
    @Schema(description="结束重量")
    private BigDecimal endWeight;

	/**
	* 包裹费用
	*/
    @Schema(description="包裹费用")
    private BigDecimal pkgCost;

	/**
	 * 利润率
	 */
	@Schema(description="利润率")
	private BigDecimal profitMargin;

	/**
	* 费用模版id
	*/
    @Schema(description="费用模版id")
    private Long pkgFreId;

}