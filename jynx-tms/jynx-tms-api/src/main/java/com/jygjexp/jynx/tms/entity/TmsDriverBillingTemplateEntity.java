package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;

/**
 * 司机计费模板主表
 *
 * <AUTHOR>
 * @date 2025-07-21 18:58:15
 */
@Data
@TenantTable
@TableName("tms_driver_billing_template")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "司机计费模板主表")
public class TmsDriverBillingTemplateEntity extends Model<TmsDriverBillingTemplateEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 模版ID（编码）
	*/
    @Schema(description="模版ID（编码）")
    private String templateCode;

	/**
	* 模版名称
	*/
    @Schema(description="模版名称")
    private String templateName;

	/**
	* 模版类型
	*/
    @Schema(description="模版类型")
    private Integer templateType;

	/**
	* 工作方式
	*/
    @Schema(description="工作方式")
    private Integer workMode;

	/**
	* 计费模式（1=时薪 2=计件）
	*/
    @Schema(description="计费模式（1=时薪 2=计件）")
    private Integer billingMode;

	/**
	 * 车辆类型（1=小型货车 2=中型货车）
	 */
	@Schema(description="车辆类型（1=小型货车 2=中型货车）")
	private Integer vehicleType;


	/**
	 * 模版绑定司机名单
	 */
	@Schema(description="模版绑定司机名单")
	private String driverId;

	/**
	* 启用状态：0：禁用、1：启用
	*/
    @Schema(description="启用状态：0：禁用、1：启用")
    private Integer isValid;

	/**
	* 乐观锁
	*/
    @Schema(description="乐观锁")
    private Long revision;

	/**
	* 备注
	*/
    @Schema(description="备注")
    private String remark;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	/**
	* 删除标志
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标志")
    private String delFlag;

	/**
	* 租户号
	*/
    @Schema(description="租户号")
    private Long tenantId;
}