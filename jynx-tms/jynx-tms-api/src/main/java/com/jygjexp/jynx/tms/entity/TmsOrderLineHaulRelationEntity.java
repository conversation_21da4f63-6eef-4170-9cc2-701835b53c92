package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jygjexp.jynx.common.core.util.TenantTable;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: chenchang
 * @Description: 客户与干线单任务关联表
 * @Date: 2025/4/13 21:12
 */
@Data
@TenantTable
@TableName("tms_order_line_haul_relation")
@EqualsAndHashCode(callSuper = true)
public class TmsOrderLineHaulRelationEntity extends BaseLogicEntity<TmsOrderLineHaulRelationEntity> {

    @TableId(type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "客户订单ID")
    private Long customerOrderId;

    @Schema(description = "客户单号")
    private String customerOrderNumber;

    @Schema(description = "跟踪单号")
    private String entrustedOrderNumber;

    @Schema(description = "干线任务单号")
    private String lineHaulNo;

    @Schema(description = "关联类型(1:主任务单,2:补充任务单)")
    private Integer relationType;


}
