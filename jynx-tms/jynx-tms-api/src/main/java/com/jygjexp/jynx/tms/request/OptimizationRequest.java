package com.jygjexp.jynx.tms.request;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OptimizationRequest {
        private Model model;
        private boolean populatePolylines;
        private boolean populateTransitionPolylines;

        // 嵌套模型类
        @Data
        public static class Model {
            private List<Shipment> shipments = new ArrayList<>();
            private List<Vehicle> vehicles = new ArrayList<>();
            private String globalStartTime;
            private String globalEndTime;
        }
        @Data
        public static class Shipment {
            private List<Pickup> pickups= new ArrayList<>();
            private List<Delivery> deliveries = new ArrayList<>();
            private LoadDemand loadDemands;
            private String label;
        }
        @Data
        public static class Delivery {
            private Location arrivalLocation;
            private String duration;
            private List<TimeWindow> timeWindows = new ArrayList<>();
        }
        @Data
        public static class Pickup {
            private Location arrivalLocation;
            private String duration;
            private List<TimeWindow> timeWindows = new ArrayList<>();
        }
        @Data
        public static class Vehicle {
            private Location startLocation;
            private Location endLocation;
            private LoadLimit loadLimits;
            private List<StartTimeWindow> startTimeWindows = new ArrayList<>();
            private List<EndTimeWindow> endTimeWindows = new ArrayList<>();
            private String label;
            private int costPerKilometer;
        }
        @Data
        // 辅助结构类
        public static class Location {
            private BigDecimal latitude;
            private BigDecimal longitude;
        }
        @Data
        public static class StartTimeWindow {
            private String startTime;
        }

        @Data
        public static class EndTimeWindow {
            private String endTime;
        }
        @Data
        public static class TimeWindow {
            private String startTime;
            private String endTime;
        }
        @Data
        public static class LoadDemand {
            private WeightToLoadDemand weight = new WeightToLoadDemand();
        }
        @Data
        public static class LoadLimit {
            private WeightToLoadLimit weight = new WeightToLoadLimit();
        }
        @Data
        public static class WeightToLoadDemand {
            private Integer amount;
        }
        @Data
        public static class WeightToLoadLimit {
            private Integer maxLoad;
        }
    }

