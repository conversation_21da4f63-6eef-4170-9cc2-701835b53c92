package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 车辆路线过渡段信息
 *
 * <AUTHOR>
 * @date 2025-03-17 15:40:58
 */
@Data
@TableName("tms_vehicle_route_transition")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "车辆路线过渡段信息")
public class TmsVehicleRouteTransitionEntity extends Model<TmsVehicleRouteTransitionEntity> {


	/**
	* 过渡段信息id
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="过渡段信息id")
    private Long vehicleRouteTransitionId;

	/**
	* 车辆路线规划id
	*/
    @Schema(description="车辆路线规划id")
    private Long vehicleRouteId;

	/**
	 * 访问顺序
	 */
	@Schema(description="访问顺序")
	private Integer orderNum;

	/**
	* 派送路程中持续时间，单位秒（s）
	*/
    @Schema(description="派送路程中持续时间，单位秒（s）")
    private String travelDuration;

	/**
	* 线路总长，单位（米）
	*/
    @Schema(description="线路总长，单位（米）")
    private Long travelDistanceMeters;

	/**
	* 等待持续时间，单位秒（s）
	*/
    @Schema(description="等待持续时间，单位秒（s）")
    private String waitDuration;

	/**
	* 线路总共持续时间
	*/
    @Schema(description="线路总共持续时间")
    private String totalDuration;

	/**
	* 开始时间
	*/
    @Schema(description="开始时间")
    private LocalDateTime startTime;

	/**
	* 车辆过渡段重量
	*/
    @Schema(description="车辆过渡段重量")
    private BigDecimal vehicleLoadsWeight;

	/**
	 * 重新规划标志点（默认值0（只进行了一次路径规划：中途没有进行重新进行路径规划），重新规划加一，即表示进行了多少次重新规划，用于将此时最新的visit线路查询出来）
	 */
	@Schema(description="重新规划标志点")
	private Integer replanningSign;


	/**
	 * 状态
	 */
	@Schema(description="状态")
	private Integer status;

	/**
	 * 站点id
	 */
	@Schema(description="站点id")
	private Integer siteId;

	/**
	 * 乐观锁
	 */
	@Schema(description="乐观锁")
	private Integer revision;

	/**
	 * 备注
	 */
	@Schema(description="备注")
	private String remark;

	/**
	 * 创建人
	 */
	@TableField(fill = FieldFill.INSERT)
	@Schema(description="创建人")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	@Schema(description="创建时间")
	private LocalDateTime createTime;

	/**
	 * 更新人
	 */
	@TableField(fill = FieldFill.UPDATE)
	@Schema(description="更新人")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.UPDATE)
	@Schema(description="更新时间")
	private LocalDateTime updateTime;

	/**
	 * 逻辑删除
	 */
	@TableLogic
	@TableField(fill = FieldFill.INSERT)
	@Schema(description="逻辑删除")
	private String delFlag;

	/**
	 * 租户号
	 */
	@Schema(description="租户号")
	private Integer tenantId;

	/**
	 * 两点间路线字符串（两点间的路线加密串,如果不为空则存储起来-两点间太近、访问点、最后一个是没有的）
	 */
	@Schema(description="两点间路线字符串（两点间的路线加密串,如果不为空则存储起来-两点间太近、访问点、最后一个是没有的）")
	private String transitionPolylinePoints;
	/**
	 * 路线令牌token
	 */
	@Schema(description="路线令牌token")
	private String routeToken;
}