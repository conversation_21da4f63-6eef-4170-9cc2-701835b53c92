package com.jygjexp.jynx.tms.request;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.tms.vo.TmsCustomerOrderPageVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ProjectName: NXWL_Backend_Api
 * @Author: chenchang
 * @Description:
 * @Date: 2025/6/11 0:51
 * @Version: 1.0
 */
@Data
@Schema(description = "揽收指派客户订单分页请求")
public class PickupCustomerOrderRequest {

    private long total;
    private long size;
    private long current;

    @Schema(description = "查询条件")
    private TmsCustomerOrderPageVo query;
}
