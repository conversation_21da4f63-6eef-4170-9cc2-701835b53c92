package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jygjexp.jynx.common.core.util.TenantTable;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 卡派订单申报信息
 *
 * <AUTHOR>
 * @date 2025-01-15 18:36:07
 */
@Data
@TenantTable
@TableName("tms_delivery_order_declare")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "卡派订单申报信息")
public class TmsDeliveryOrderDeclareEntity extends BaseLogicEntity<TmsDeliveryOrderDeclareEntity> {


    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

    /**
     * 订单id
     */
    @Schema(description="订单id")
    private Long orderId;

    /**
     * 箱id
     */
    @Schema(description="箱id")
    private Long itemId;

    /**
     * 名称
     */
    @Schema(description="名称")
    private String forecastTitle;

    /**
     * 名称 中文
     */
    @Schema(description="名称 中文")
    private String forecastTitleCn;

    /**
     * 数量
     */
    @Schema(description="数量")
    private Integer forecastQuantity;

    /**
     * 申报价格(单价),单位 USD,必填
     */
    @Schema(description="申报价格(单价),单位 USD,必填")
    private BigDecimal forecastUnitPrice;

    /**
     * 申报重量(单重)，单位 kg
     */
    @Schema(description="申报重量(单重)，单位 kg")
    private BigDecimal forecastUnitWeight;

    /**
     * 预报海关编码
     */
    @Schema(description="预报海关编码")
    private String forecastCustomsCode;

    /**
     * 实际名称
     */
    @Schema(description="实际名称")
    private String actualTitle;

    /**
     * 实际名称 中文
     */
    @Schema(description="实际名称 中文")
    private String actualTitleCn;

    /**
     * 实际数量
     */
    @Schema(description="实际数量")
    private Integer actualQuantity;

    /**
     * 申报价格(单价),单位 USD,必填
     */
    @Schema(description="申报价格(单价),单位 USD,必填")
    private BigDecimal actualUnitPrice;

    /**
     * 申报重量(单重)，单位 kg
     */
    @Schema(description="申报重量(单重)，单位 kg")
    private BigDecimal actualUnitWeight;

    /**
     * 实际海关编码
     */
    @Schema(description="实际海关编码")
    private String actualCustomsCode;

    /**
     * 申报单位
     */
    @Schema(description="申报单位")
    private String unitCode;

    /**
     * 产品url
     */
    @Schema(description="产品url")
    private String productUrl;

    /**
     * 图片url
     */
    @Schema(description="图片url")
    private String imageUrl;

    /**
     * 品牌
     */
    @Schema(description="品牌")
    private String brand;

    /**
     * 型号
     */
    @Schema(description="型号")
    private String model;

    /**
     * 材质
     */
    @Schema(description="材质")
    private String material;

    /**
     * 用途
     */
    @Schema(description="用途")
    private String purpose;

    /**
     * 申报币种，默认:USD
     */
    @Schema(description="申报币种，默认:USD")
    private String currencyCode;

    /**
     * SKU
     */
    @Schema(description="SKU")
    private String sku;

}