package com.jygjexp.jynx.tms.constants;

public class TmsMessageTypeConstants {

    /**
     * 异常订单
     */
    public static final int EXCEPTION_ORDER = 1;

    /**
     * 揽收订单
     */
    public static final int PICKUP_ORDER = 2;

    /**
     * 派送订单
     */
    public static final int DELIVERY_ORDER = 3;

    /**
     * 干线订单
     */
    public static final int TRUNK_ORDER = 4;

    public static String getDescription(int type) {
        switch (type) {
            case EXCEPTION_ORDER:
                return "异常订单";
            case PICKUP_ORDER:
                return "揽收订单";
            case DELIVERY_ORDER:
                return "派送订单";
            case TRUNK_ORDER:
                return "干线订单";
            default:
                return "未知类型";
        }
    }
}