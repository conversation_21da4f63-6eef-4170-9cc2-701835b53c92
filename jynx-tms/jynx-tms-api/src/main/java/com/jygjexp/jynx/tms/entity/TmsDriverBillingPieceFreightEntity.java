package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 司机计件模板-运费配置子表
 *
 * <AUTHOR>
 * @date 2025-07-21 18:59:30
 */
@Data
@TableName("tms_driver_billing_piece_freight")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "司机计件模板-运费配置子表")
public class TmsDriverBillingPieceFreightEntity extends Model<TmsDriverBillingPieceFreightEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long freightId;

	/**
	* 计件配置ID，关联 driver_billing_piece.id
	*/
    @Schema(description="计件配置ID，关联 driver_billing_piece.id")
    private Long pieceId;

	/**
	* 起始重量（kg）
	*/
    @Schema(description="起始重量（kg）")
    private BigDecimal startWeight;

	/**
	* 结束重量（kg）
	*/
    @Schema(description="结束重量（kg）")
    private BigDecimal endWeight;

	/**
	* 首件单价(CAD)
	*/
    @Schema(description="首件单价(CAD)")
    private BigDecimal firstItemPrice;

	/**
	* 续件单价(CAD)
	*/
    @Schema(description="续件单价(CAD)")
    private BigDecimal additionalItemPrice;
 
	/**
	* createTime
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="createTime")
    private LocalDateTime createTime;
 
	/**
	* updateTime
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="updateTime")
    private LocalDateTime updateTime;
}