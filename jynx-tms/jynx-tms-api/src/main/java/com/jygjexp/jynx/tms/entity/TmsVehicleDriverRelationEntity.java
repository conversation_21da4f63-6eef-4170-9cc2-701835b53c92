package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 车辆司机关联中间表
 *
 * <AUTHOR>
 * @date 2025-05-13 11:31:35
 */
@Data
@TableName("tms_vehicle_driver_relation")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "车辆司机关联中间表")
public class TmsVehicleDriverRelationEntity extends Model<TmsVehicleDriverRelationEntity> {

 
	/**
	* id
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="id")
    private Long id;
 
	/**
	* vehicleId
	*/
    @Schema(description="vehicleId")
    private Long vehicleId;
 
	/**
	* driverId
	*/
    @Schema(description="driverId")
    private Long driverId;
 
	/**
	* createTime
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="createTime")
    private LocalDateTime createTime;
}