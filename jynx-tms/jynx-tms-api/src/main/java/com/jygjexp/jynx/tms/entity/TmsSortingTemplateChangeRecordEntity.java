package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 分拣模板变更记录
 *
 * <AUTHOR>
 * @date 2025-07-23 10:41:37
 */
@Data
@TableName("tms_sorting_template_change_record")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "分拣模板变更记录")
public class TmsSortingTemplateChangeRecordEntity extends Model<TmsSortingTemplateChangeRecordEntity> {


    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="id")
    private Long id;

    /**
     * 模板id
     */
    @Schema(description="模板id")
    private Long templateId;

    /**
     * 模板名称
     */
    @Schema(description="模板名称")
    private String templateName;

    /**
     * 模板代码
     */
    @Schema(description="模板代码")
    private String templateCode;

    /**
     * 模板业务类型:1:揽收取件2:正向派送5:半托管分拣
     */
    @Schema(description="模板业务类型:1:揽收取件2:正向派送5:半托管分拣")
    private Integer templateBusinessType;

    /**
     * 操作类型:-1:删除;0:停用;1:启用;2:修改
     */
    @Schema(description="操作类型:-1:删除;0:停用;1:启用;2:修改")
    private Integer operationType;

    /**
     * 模板变更前内容
     */
    @Schema(description="模板变更前内容")
    private String beforeContent;

    /**
     * 模板变更后内容
     */
    @Schema(description="模板变更后内容")
    private String afterContent;

    /**
     * 操作时间
     */
    @Schema(description="操作时间")
    private LocalDateTime operateTime;

    /**
     * 操作人
     */
    @Schema(description = "操作人")
    private String operateName;

    /**
     * 备注
     */
    @Schema(description="备注")
    private String remark;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

    /**
     * 删除标记：0未删除，1已删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标记：0未删除，1已删除")
    private String delFlag;
}
