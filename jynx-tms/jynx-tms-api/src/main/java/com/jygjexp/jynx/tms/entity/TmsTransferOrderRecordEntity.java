package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;

/**
 * 转单记录表
 *
 * <AUTHOR>
 * @date 2025-05-22 14:08:30
 */
@Data
@TenantTable
@TableName("tms_transfer_order_record")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "转单记录表")
public class TmsTransferOrderRecordEntity extends Model<TmsTransferOrderRecordEntity> {


	/**
	* 转单记录表id
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="转单记录表id")
    private Long id;

	/**
	* 订单号（订单的跟踪单号或者干线的任务单号）
	*/
    @Schema(description="订单号（订单的跟踪单号或者干线的任务单号）")
    private String orderNo;

	/**
	* 原司机id
	*/
    @Schema(description="原司机id")
    private Long oldDriverId;

	/**
	* 原司机名称
	*/
    @Schema(description="原司机名称")
    private String oldDriverName;

	/**
	* 当前司机id
	*/
    @Schema(description="当前司机id")
    private Long currentDriverId;

	/**
	* 当前司机名称
	*/
    @Schema(description="当前司机名称")
    private String currentDriverName;

	/**
	* 转单类型（1：揽收、2：派送、3：干线）
	*/
    @Schema(description="转单类型（1：揽收、2：派送、3：干线）")
    private Integer type;

	/**
	* 转单原因
	*/
    @Schema(description="转单原因")
    private String reason;
	/**
	 * 转单描述
	 */
	@Schema(description="转单描述")
	private String description ;
	/**
	* 状态
	*/
    @Schema(description="状态")
    private Integer status;

	/**
	* 站点id
	*/
    @Schema(description="站点id")
    private Integer siteId;

	/**
	* 乐观锁
	*/
    @Schema(description="乐观锁")
    private Integer revision;

	/**
	* 备注
	*/
    @Schema(description="备注")
    private String remark;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	/**
	* 逻辑删除
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="逻辑删除")
    private String delFlag;

	/**
	* 租户号
	*/
    @Schema(description="租户号")
    private Integer tenantId;
}