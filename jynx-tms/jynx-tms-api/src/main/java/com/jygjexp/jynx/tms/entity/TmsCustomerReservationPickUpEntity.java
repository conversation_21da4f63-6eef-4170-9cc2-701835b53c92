package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.tms.enums.CustomerPickUpOrderStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 客户预约取件
 *
 * <AUTHOR>
 * @date 2025-07-16 10:47:32
 */
@Data
@TenantTable
@TableName("tms_customer_reservation_pick_up")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "客户预约取件")
public class TmsCustomerReservationPickUpEntity extends Model<TmsCustomerReservationPickUpEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	 * 预约单号
	 */
	@Schema(description="预约单号")
	private String reservationNumber;

	@Schema(description = "客户id")
	private Long customerId;


	/**
	* 客户名称
	*/
    @Schema(description="客户名称")
    private String customerName;

	/**
	* 国家/省/城市
	*/
    @Schema(description="国家/省/城市")
    private String city;

	/**
	* 联系名称
	*/
    @Schema(description="联系名称")
    private String contactName;

	/**
	* 邮政编码
	*/
    @Schema(description="邮政编码")
    private String postalCode;

	/**
	* 手机号
	*/
    @Schema(description="手机号")
    private String telephone;

	/**
	* 详细地址
	*/
    @Schema(description="详细地址")
    private String addressLine;

	/**
	* 包裹数
	*/
    @Schema(description="包裹数")
    private Integer packageNum;

	/**
	* 重量(kg)
	*/
    @Schema(description="重量(kg)")
    private BigDecimal weight;

	/**
	* 是否超过32KG
	*/
    @Schema(description="是否超过32KG")
    private Boolean weightStatus;

	/**
	* 取件日期
	*/
    @Schema(description="取件日期")
    private LocalDate pickupDate;

	/**
	* 提前取件时间
	*/
    @Schema(description="提前取件时间")
    private LocalDateTime startPickupTime;

	/**
	* 最迟取件时间
	*/
    @Schema(description="最迟取件时间")
    private LocalDateTime endPickupTime;

	/**
	* 取件参考
	*/
    @Schema(description="取件参考")
    private String pickupReference;

	/**
	* 特别说明
	*/
    @Schema(description="特别说明")
    private String specialInstructions;

	/**
	 * 司机id
	 */
	@Schema(description = "司机id")
	private String driverId;

	/**
	 * 附件图片
	 */
	@Schema(description = "附件图片")
	private String fileUrl;

	/**
	* 状态
 	* @see CustomerPickUpOrderStatusEnum
	*/
    @Schema(description="状态")
    private Integer status;

	@Schema(description="经纬度")
	private String location;

	/**
	* 乐观锁
	*/
    @Schema(description="乐观锁")
    private Long revision;

	/**
	* 备注
	*/
    @Schema(description="备注")
    private String remark;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	/**
	* 删除标志
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标志")
    private String delFlag;

	/**
	* 租户号
	*/
    @Schema(description="租户号")
    private Long tenantId;
}