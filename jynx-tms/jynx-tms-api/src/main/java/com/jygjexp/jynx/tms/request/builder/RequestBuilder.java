package com.jygjexp.jynx.tms.request.builder;

import com.jygjexp.jynx.tms.request.OptimizationRequest;
import lombok.Data;

import java.math.BigDecimal;
import java.util.function.Consumer;

import static java.awt.SystemColor.window;

/**
 * <AUTHOR>
 */
@Data
public class RequestBuilder {
    private final OptimizationRequest request = new OptimizationRequest();

    public RequestBuilder(OptimizationRequest.Model model, Boolean populatePolylines,Boolean populateTransitionPolylines){
        request.setModel(model);
        request.setPopulatePolylines(populatePolylines);
        request.setPopulateTransitionPolylines(populateTransitionPolylines);
    }
    public RequestBuilder withGlobalTime(String start, String end) {
        request.getModel().setGlobalStartTime(start);
        request.getModel().setGlobalEndTime(end);
        return this;
    }

    public RequestBuilder addShipment(Consumer<ShipmentBuilder> consumer) {
        ShipmentBuilder builder = new ShipmentBuilder();
        consumer.accept(builder);
        request.getModel().getShipments().add(builder.build());
        return this;
    }

    public RequestBuilder addVehicle(Consumer<VehicleBuilder> consumer) {
        VehicleBuilder builder = new VehicleBuilder();
        consumer.accept(builder);
        request.getModel().getVehicles().add(builder.build());
        return this;
    }
    public OptimizationRequest build() {
        return request;
    }

    // 内部建造者类
    public class ShipmentBuilder {
        private final OptimizationRequest.Shipment shipment = new OptimizationRequest.Shipment();

        public ShipmentBuilder withLabel(String label) {
            shipment.setLabel(label);
            return this;
        }

        public ShipmentBuilder withPickup(Consumer<PickupBuilder> consumer) {
            PickupBuilder builder = new PickupBuilder();
            consumer.accept(builder);
            shipment.getPickups().add(builder.build());
            return this;
        }
        public ShipmentBuilder withDelivery(Consumer<DeliveryBuilder> consumer) {
            DeliveryBuilder builder = new DeliveryBuilder();
            consumer.accept(builder);
            shipment.getDeliveries().add(builder.build());
            return this;
        }

        public ShipmentBuilder withLoadDemand(Integer amount) {
            OptimizationRequest.LoadDemand demand = new OptimizationRequest.LoadDemand();
            demand.getWeight().setAmount(amount);
            shipment.setLoadDemands(demand);
            return this;
        }

        public OptimizationRequest.Shipment build() {
            return shipment;
        }
    }

    public static class VehicleBuilder {
        private final OptimizationRequest.Vehicle vehicle = new OptimizationRequest.Vehicle();

        public VehicleBuilder withStartLocation(BigDecimal lat, BigDecimal lng) {
            OptimizationRequest.Location loc = new OptimizationRequest.Location();
            loc.setLatitude(lat);
            loc.setLongitude(lng);
            vehicle.setStartLocation(loc);
            return this;
        }

        public VehicleBuilder withEndLocation(BigDecimal lat, BigDecimal lng) {
            OptimizationRequest.Location loc = new OptimizationRequest.Location();
            loc.setLatitude(lat);
            loc.setLongitude(lng);
            vehicle.setEndLocation(loc);
            return this;
        }

        public VehicleBuilder withTimeWindows(String start, String end) {
            OptimizationRequest.StartTimeWindow startTimeWindow = new OptimizationRequest.StartTimeWindow();
            startTimeWindow.setStartTime(start);
            OptimizationRequest.EndTimeWindow endTimeWindow = new OptimizationRequest.EndTimeWindow();
            endTimeWindow.setEndTime(end);
            vehicle.getStartTimeWindows().add(startTimeWindow);
            vehicle.getEndTimeWindows().add(endTimeWindow);
            return this;
        }

        public VehicleBuilder withLoadLimit(int max) {
            OptimizationRequest.LoadLimit limit = new OptimizationRequest.LoadLimit();
            limit.getWeight().setMaxLoad(max);
            vehicle.setLoadLimits(limit);
            return this;
        }
        public VehicleBuilder withLabel(String label) {
            vehicle.setLabel(label);
            return this;
        }
        public VehicleBuilder withCostPerKilometer(int costPerKilometer) {
            vehicle.setCostPerKilometer(costPerKilometer);
            return this;
        }

        public OptimizationRequest.Vehicle build() {
            return vehicle;
        }
    }

    public static class DeliveryBuilder {
        private final OptimizationRequest.Delivery delivery = new OptimizationRequest.Delivery();

        public DeliveryBuilder withLocation(BigDecimal lat, BigDecimal lng) {
            OptimizationRequest.Location loc = new OptimizationRequest.Location();
            loc.setLatitude(lat);
            loc.setLongitude(lng);
            delivery.setArrivalLocation(loc);
            return this;
        }

        public DeliveryBuilder withDuration(String duration) {
            delivery.setDuration(duration);
            return this;
        }

        public DeliveryBuilder addTimeWindow(String start, String end) {
            OptimizationRequest.TimeWindow window = new OptimizationRequest.TimeWindow();
            window.setStartTime(start);
            window.setEndTime(end);
            delivery.getTimeWindows().add(window);
            return this;
        }

        public OptimizationRequest.Delivery build() {
            return delivery;
        }
    }
    public static class PickupBuilder {
        private final OptimizationRequest.Pickup pickup = new OptimizationRequest.Pickup();

        public PickupBuilder withLocation(BigDecimal lat, BigDecimal lng) {
            OptimizationRequest.Location loc = new OptimizationRequest.Location();
            loc.setLatitude(lat);
            loc.setLongitude(lng);
            pickup.setArrivalLocation(loc);
            return this;
        }

        public PickupBuilder withDuration(String duration) {
            pickup.setDuration(duration);
            return this;
        }

        public PickupBuilder addTimeWindow(String start, String end) {
            OptimizationRequest.TimeWindow window = new OptimizationRequest.TimeWindow();
            window.setStartTime(start);
            window.setEndTime(end);
            pickup.getTimeWindows().add(window);
            return this;
        }

        public OptimizationRequest.Pickup build() {
            return pickup;
        }
    }
}
