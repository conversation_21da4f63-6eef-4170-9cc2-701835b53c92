package com.jygjexp.jynx.tms.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 字典类型
 */
@Getter
@AllArgsConstructor
public class DictConvertConstants {

    //KEY
    public static final String BASE_KEY = "1:dict_details::";
    //仓库
    public static final String WAREHOUSE = "ReturnWareHouse";
    //返仓订单类型
    public static final String RETURN_ORDER_TYPE = "ReturnOrderTYPE";
    //来源渠道
    public static final String RETURN_AUTH = "ReturnAuth";
    //订单状态
    public static final String RETURN_STATUS = "ReturnStatus";
    //驿站人员类型
    public static final String STATION_EMPLOYEE_TYPE = "StationEmployeeType";
    //驿站管理方式
    public static final String STATION_MANAGE_TYPE = "StationManageType";
    //驿站转账方式
    public static final String STATION_TRANSFER_TYPE = "StationTransferType";
    //地区
    public static final String CITY = "City";
    //打印机状态
    public static final String PRINT_STATE = "PrintStatus";
    //附加服务类型
    public static final String ADDITIONAL_SERVICE_TYPE = "tms_additional_service_type";
    //中大件订单状态
    public static final String ORDER_STATUS_ZDJ = "tms_zjd_order_status";
    //中大件收货类型
    public static final String RECEIVE_TYPE_ZDJ = "tms_receiving_type";
    //中大件订单类型
    public static final String ORDER_TYPE_ZDJ = "tms_order_type";
    //中大件运输类型
    public static final String TRANSPORT_TYPE_ZDJ = "tms_transport_type";
    //中大件货物类型
    public static final String CARGO_TYPE_ZDJ = "cargo_type";
    //中大件地址类型
    public static final String ADDRESS_TYPE_ZDJ = "tms_address_type";
    //中大件业务模式
    public static final String BUSINESS_MODEL_ZDJ = "tms_business_model";
    //中大件推送渠道
    public static final String CHANNEL_TYPE_ZDJ = "tms_customer_push";
    //中大件运输任务状态
    public static final String TASK_STATUS = "tms_task_status";
    //中大件司机类型
    public static final String DRIVER_TYPE_ZDJ = "tms_lmdDriver_driverType";
    //中大件司机工作方式
    public static final String DRIVER_WORK_TYPE_ZDJ = "tms_lmdDriver_workType";
    //中大件司机驾照类型
    public static final String LICENSE_TYPE_ZDJ = "tms_license_type";
    //中大件司机计费模式
    public static final String BILLING_MODEL_ZDJ = "tms_lmdDriver_billingModel";
    //中大件启用停用
    public static final String TMS_IS_VALID = "tms_isValid";
    //中大件司机审核状态
    public static final String DRIVER_AUDIT_STATUS = "tms_lmddriver_audit";
    //中大件司机审核状态
    public static final String LEVEL_MEMBERSHIP = "tms_service_quote_member_level";
    //附加费费用类型
    public static final String FEE_TYPE = "tms_fee_type";
    // 门店类型
    public static final String TMS_STORE_TYPE = "TMS_STORE_TYPE";
    // 结算方式
    public static final String TMS_STORE_SETTLEMENT_TYPE = "TMS_STORE_SETTLEMENT_TYPE";
    // 支付方式
    public static final String TMS_STORE_PAY_TYPE = "TMS_STORE_PAY_TYPE";
    //门店客户类型
    public static final String TMS_STORE_CUSTOMER_TYPE = "TMS_STORE_CUSTOMER_TYPE";
    //门店客户等级
    public static final String TMS_STORE_CUSTOMER_RANK = "TMS_STORE_CUSTOMER_RANK";
    //盲盒比价规则
    public static final String TMS_BOX_RULE = "tms_box_rule";
    // 快递订单状态
    public static final String STORE_ORDER_STATUS = "store_order_status";
    // 推广人结算状态
    public static final String TMS_STORE_PROMOTION_SETTLE_STATUS = "TMS_STORE_PROMOTION_SETTLE_STATUS";

}
