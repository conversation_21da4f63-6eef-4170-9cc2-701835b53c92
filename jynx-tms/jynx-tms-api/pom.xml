<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
            http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.jygjexp</groupId>
		<artifactId>jynx-tms</artifactId>
		<version>5.6.0</version>
	</parent>

	<artifactId>jynx-tms-api</artifactId>

	<dependencies>
		<!-- 连表查询注解 -->
		<dependency>
			<groupId>com.github.yulichang</groupId>
			<artifactId>mybatis-plus-join-annotation</artifactId>
		</dependency>
		<!--core 工具类-->
		<dependency>
			<groupId>com.jygjexp</groupId>
			<artifactId>jynx-common-core</artifactId>
		</dependency>
		<!--mybatis plus extension,包含了mybatis plus core-->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-extension</artifactId>
		</dependency>
		<!--feign 工具类-->
		<dependency>
			<groupId>com.jygjexp</groupId>
			<artifactId>jynx-common-feign</artifactId>
		</dependency>
		<!-- excel 导入导出 -->
		<dependency>
			<groupId>com.jygjexp</groupId>
			<artifactId>jynx-common-excel</artifactId>
		</dependency>
		<!--必备: 操作数据源相关-->
		<dependency>
			<groupId>com.jygjexp</groupId>
			<artifactId>jynx-common-data</artifactId>
		</dependency>
		<dependency>
			<groupId>com.jygjexp</groupId>
			<artifactId>jynx-app-server-api</artifactId>
		</dependency>

		<!-- pdf相关		-->
		<dependency>
			<groupId>org.apache.pdfbox</groupId>
			<artifactId>pdfbox</artifactId>
		</dependency>
		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>core</artifactId>
		</dependency>
		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>javase</artifactId>
		</dependency>
	</dependencies>
</project>
