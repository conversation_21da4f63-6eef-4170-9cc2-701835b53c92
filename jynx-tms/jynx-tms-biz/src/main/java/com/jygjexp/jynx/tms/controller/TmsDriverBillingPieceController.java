package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsDriverBillingPieceEntity;
import com.jygjexp.jynx.tms.service.TmsDriverBillingPieceService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 司机计费模板-计件配置表
 *
 * <AUTHOR>
 * @date 2025-07-21 18:59:09
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsDriverBillingPiece" )
@Tag(description = "tmsDriverBillingPiece" , name = "司机计费模板-计件配置表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsDriverBillingPieceController {

    private final  TmsDriverBillingPieceService tmsDriverBillingPieceService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsDriverBillingPiece 司机计费模板-计件配置表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverBillingPiece_view')" )
    public R getTmsDriverBillingPiecePage(@ParameterObject Page page, @ParameterObject TmsDriverBillingPieceEntity tmsDriverBillingPiece) {
        LambdaQueryWrapper<TmsDriverBillingPieceEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsDriverBillingPieceService.page(page, wrapper));
    }


    /**
     * 通过id查询司机计费模板-计件配置表
     * @param pieceId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{pieceId}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverBillingPiece_view')" )
    public R getById(@PathVariable("pieceId" ) Long pieceId) {
        return R.ok(tmsDriverBillingPieceService.getById(pieceId));
    }

    /**
     * 新增司机计费模板-计件配置表
     * @param tmsDriverBillingPiece 司机计费模板-计件配置表
     * @return R
     */
    @Operation(summary = "新增司机计费模板-计件配置表" , description = "新增司机计费模板-计件配置表" )
    @SysLog("新增司机计费模板-计件配置表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverBillingPiece_add')" )
    public R save(@RequestBody TmsDriverBillingPieceEntity tmsDriverBillingPiece) {
        return R.ok(tmsDriverBillingPieceService.save(tmsDriverBillingPiece));
    }

    /**
     * 修改司机计费模板-计件配置表
     * @param tmsDriverBillingPiece 司机计费模板-计件配置表
     * @return R
     */
    @Operation(summary = "修改司机计费模板-计件配置表" , description = "修改司机计费模板-计件配置表" )
    @SysLog("修改司机计费模板-计件配置表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverBillingPiece_edit')" )
    public R updateById(@RequestBody TmsDriverBillingPieceEntity tmsDriverBillingPiece) {
        return R.ok(tmsDriverBillingPieceService.updateById(tmsDriverBillingPiece));
    }

    /**
     * 通过id删除司机计费模板-计件配置表
     * @param ids pieceId列表
     * @return R
     */
    @Operation(summary = "通过id删除司机计费模板-计件配置表" , description = "通过id删除司机计费模板-计件配置表" )
    @SysLog("通过id删除司机计费模板-计件配置表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverBillingPiece_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsDriverBillingPieceService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsDriverBillingPiece 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverBillingPiece_export')" )
    public List<TmsDriverBillingPieceEntity> export(TmsDriverBillingPieceEntity tmsDriverBillingPiece,Long[] ids) {
        return tmsDriverBillingPieceService.list(Wrappers.lambdaQuery(tmsDriverBillingPiece).in(ArrayUtil.isNotEmpty(ids), TmsDriverBillingPieceEntity::getPieceId, ids));
    }
}