package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsDeliveryOrderDeclareEntity;
import com.jygjexp.jynx.tms.service.TmsDeliveryOrderDeclareService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 卡派订单申报信息
 *
 * <AUTHOR>
 * @date 2025-01-15 18:36:07
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsDeliveryOrderDeclare" )
@Tag(description = "tmsDeliveryOrderDeclare" , name = "卡派订单申报信息管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsDeliveryOrderDeclareController {

    private final  TmsDeliveryOrderDeclareService tmsDeliveryOrderDeclareService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsDeliveryOrderDeclare 卡派订单申报信息
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsDeliveryOrderDeclare_view')" )
    public R getTmsDeliveryOrderDeclarePage(@ParameterObject Page page, @ParameterObject TmsDeliveryOrderDeclareEntity tmsDeliveryOrderDeclare) {
        LambdaQueryWrapper<TmsDeliveryOrderDeclareEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsDeliveryOrderDeclareService.page(page, wrapper));
    }


    /**
     * 通过id查询卡派订单申报信息
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsDeliveryOrderDeclare_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsDeliveryOrderDeclareService.getById(id));
    }

    /**
     * 新增卡派订单申报信息
     * @param tmsDeliveryOrderDeclare 卡派订单申报信息
     * @return R
     */
    @Operation(summary = "新增卡派订单申报信息" , description = "新增卡派订单申报信息" )
    @SysLog("新增卡派订单申报信息" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsDeliveryOrderDeclare_add')" )
    public R save(@RequestBody TmsDeliveryOrderDeclareEntity tmsDeliveryOrderDeclare) {
        return R.ok(tmsDeliveryOrderDeclareService.save(tmsDeliveryOrderDeclare));
    }

    /**
     * 修改卡派订单申报信息
     * @param tmsDeliveryOrderDeclare 卡派订单申报信息
     * @return R
     */
    @Operation(summary = "修改卡派订单申报信息" , description = "修改卡派订单申报信息" )
    @SysLog("修改卡派订单申报信息" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsDeliveryOrderDeclare_edit')" )
    public R updateById(@RequestBody TmsDeliveryOrderDeclareEntity tmsDeliveryOrderDeclare) {
        return R.ok(tmsDeliveryOrderDeclareService.updateById(tmsDeliveryOrderDeclare));
    }

    /**
     * 通过id删除卡派订单申报信息
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除卡派订单申报信息" , description = "通过id删除卡派订单申报信息" )
    @SysLog("通过id删除卡派订单申报信息" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsDeliveryOrderDeclare_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsDeliveryOrderDeclareService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsDeliveryOrderDeclare 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsDeliveryOrderDeclare_export')" )
    public List<TmsDeliveryOrderDeclareEntity> export(TmsDeliveryOrderDeclareEntity tmsDeliveryOrderDeclare,Long[] ids) {
        return tmsDeliveryOrderDeclareService.list(Wrappers.lambdaQuery(tmsDeliveryOrderDeclare).in(ArrayUtil.isNotEmpty(ids), TmsDeliveryOrderDeclareEntity::getId, ids));
    }
}