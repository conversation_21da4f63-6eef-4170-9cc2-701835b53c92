package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.security.util.SecurityUtils;
import com.jygjexp.jynx.tms.constants.AuditStatusConstant;
import com.jygjexp.jynx.tms.constants.OrderWayConstants;
import com.jygjexp.jynx.tms.constants.TrackTypeConstant;
import com.jygjexp.jynx.tms.dto.TmsApiOrderTaskDto;
import com.jygjexp.jynx.tms.dto.TmsReginDTO;
import com.jygjexp.jynx.tms.dto.TrackDto;
import com.jygjexp.jynx.tms.entity.*;
import com.jygjexp.jynx.tms.enums.*;
import com.jygjexp.jynx.tms.exception.CustomBusinessException;
import com.jygjexp.jynx.tms.mapper.*;
import com.jygjexp.jynx.tms.model.bo.ApiOrder;
//import com.jygjexp.jynx.tms.mq.producer.TmsBestOfficeOrderSyncProducer;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.*;
import com.jygjexp.jynx.tms.utils.*;
import com.jygjexp.jynx.tms.vo.*;
import com.jygjexp.jynx.tms.vo.excel.TmsCustomerOrderExcelVo;
import com.jygjexp.jynx.tms.vo.excel.TmsCustomerZdjOrderExcelVo;
import com.jygjexp.jynx.tms.vo.excel.TmsZdjCustomerOrderVo;
import com.jygjexp.jynx.zxoms.entity.CitiesEntity;
import com.jygjexp.jynx.zxoms.entity.StatesEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 卡派客户订单
 *
 * <AUTHOR>
 * @date 2025-03-13 15:09:24
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TmsCustomerOrderServiceImpl extends ServiceImpl<TmsCustomerOrderMapper, TmsCustomerOrderEntity> implements TmsCustomerOrderService {
    private static final Logger logger = LoggerFactory.getLogger(TmsCustomerOrderServiceImpl.class);
    private final TmsCustomerOrderMapper customerOrderMapper;
    private final TmsEntrustedOrderMapper entrustedOrderMapper;
    private final TmsRegionMapper regionMapper;
    private final TmsCarrierMapper carrierMapper;
    private final TmsBasicFreightMapper basicFreightMapper;
    private final TmsBasicFreightPkgMapper basicFreightPkgMapper;
    private final TmsBasicFreightPkgDetailMapper basicFreightPkgDetailMapper;
    private final TmsCarrierAuditRecordsService carrierAuditRecordsService;
    private final TmsCargoInfoService cargoInfoService;
    private final TmsAdditionalServicesService additionalServicesService;
    private final TmsPostalCodeService postalCodeService;
    private final TmsOrderTrackMapper orderTrackMapper;
    private final TmsOrderTrackService orderTrackService;
    private final TmsRoutePlanService routePlanService;
    private final TmsCustomerMapper customerMapper;
    private final TmsCargoInfoService tmsCargoInfoService;
    private final TmsAdditionalServicesService tmsAdditionalServicesService;
    private final TmsCustomerService tmsCustomerService;
    private final TmsTransportTaskOrderService transportTaskOrderService;
    private final TmsOverAreaService tmsOverAreaService;
    private final TmsSiteService tmsSiteService;
    private final StatesService statesService;
    private final CitiesService citiesService;
    private final TmsLineHaulOrderService tmsLineHaulOrderService;
    private final TmsThirdPartPostService tmsThirdPartPostService;
    private final TmsOrderLogService tmsOrderLogService;
    private final StringRedisTemplate redisTemplate;
    private final TmsLmdSortingService tmsLmdSortingService;
    private final TmsReceivableService tmsReceivableService;
//    private final TmsBestOfficeOrderSyncProducer bestOfficeOrderSyncProducer;

    // 自定义雪花 ID 生成器
    private final SnowflakeIdGenerator snowflakeIdGenerator;


    private static final String TRACK_CODE_PREFIX = "track:code:";

    /**
     * 客户订单分页查询
     *
     * @param page
     * @param orderPageVo
     * @return
     */
    @Override
    public Page<TmsCustomerOrderPageVo> search(Page page, TmsCustomerOrderPageVo orderPageVo, Boolean kpFlag) {
        //System.out.println(orderPageVo.getStrOrderStatus());
        MPJLambdaWrapper wrapper = getWrapper(orderPageVo, null, kpFlag);
        return customerOrderMapper.selectJoinPage(page, TmsCustomerOrderPageVo.class, wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R create(TmsCustomerOrderEntity customerOrderEntity) {
        try {
            // 数据校验
            String validationError = validateOrder(customerOrderEntity);
            if (validationError != null) {
                return R.failed(validationError); // 返回校验失败信息
            }

            String customerOrderNumber = customerOrderEntity.getCustomerOrderNumber();
            // 如何页面没有填写单号，则后台自动生成客户单号
            if (StrUtil.isBlank(customerOrderNumber)) {
                customerOrderNumber = generateCustomerOrderNumber();
                customerOrderEntity.setCustomerOrderNumber(customerOrderNumber);
            }

            //客户订单生成委托单号
            String entrustedOrderNumber = generalNewOrderNo(customerOrderEntity.getCustomerId(), customerOrderEntity.getBusinessModel());
            customerOrderEntity.setEntrustedOrderNumber(entrustedOrderNumber);

            // 操作人
            if (customerOrderEntity.getCustomerId() != null) {
                TmsCustomerEntity customer = tmsCustomerService.getById(customerOrderEntity.getCustomerId());
                customerOrderEntity.setOperator(customer == null ? null : customer.getCustomerName());
            } else {
                customerOrderEntity.setOperator(SecurityUtils.getUser().getUsername());
            }
            // 是否尾板提取货
            Integer addressType = customerOrderEntity.getAddressType() == null ? 1 : customerOrderEntity.getAddressType();
            if (addressType == 2 || addressType == 3) {
                customerOrderEntity.setIsTailgatePickup(1);
            } else if (addressType == 1 || addressType == 4) {
                customerOrderEntity.setIsTailgatePickup(0);
            }
            //中大件、揽收订单
            if (ObjectUtil.isNotNull(customerOrderEntity.getReceiveType())) {
/*                R zdjOrder = createZDJOrder(customerOrderEntity, false);
                if (zdjOrder.isOk()&&zdjOrder.getCode()==0){
                    // 订单创建成功后，检查是否为 Best Office 客户，如果是则发送同步消息
                    try {
                        sendBestOfficeOrderSyncMessage(customerOrderEntity, "CREATE");
                    } catch (Exception e) {
                        log.error("发送 Best Office 订单同步消息失败 - 订单号: {}, 错误: {}",
                                customerOrderEntity.getEntrustedOrderNumber(), e.getMessage(), e);
                        // 消息发送失败不影响订单创建，只记录日志
                    }
                }
                return zdjOrder;*/
                return createZDJOrder(customerOrderEntity, false);
            }

            log.info("开始保存主表数据");

            //创建完成订单，默认状态为待指派
            customerOrderEntity.setOrderStatus(CustomerOrderStatus.UNASSIGNED.getCode());
            int insert = customerOrderMapper.insert(customerOrderEntity);
            boolean isSaved = insert > 0;

            // 保存轨迹
            orderTrackService.saveTrack(entrustedOrderNumber, customerOrderEntity.getCustomerOrderNumber(), CustomerOrderStatus.UNASSIGNED.getValue(), "", "客户询价下单", "", 1);

            if (!isSaved) {
                return LocalizedR.failed("tms.failed.to.save.customer.order", "");
            }

            // 保存货物信息
            if (customerOrderEntity.getCargoInfoEntityList() != null && !customerOrderEntity.getCargoInfoEntityList().isEmpty()) {
                log.info("开始保存货物信息");
                int i = 0; // 初始化计数器
                for (TmsCargoInfoEntity cargoInfo : customerOrderEntity.getCargoInfoEntityList()) {
                    // 箱号-系统自动生成-由001依次递增，第一箱货就是001
                    cargoInfo.setBagNum(String.format("%03d", i + 1));
                    cargoInfo.setCustomerOrderNumber(customerOrderNumber);
                    cargoInfo.setEntrustedOrderNumber(entrustedOrderNumber);

                    // 业务模式是2-中大件时，每个包裹或每个箱子的重量不超过68kg
                    if (customerOrderEntity.getBusinessModel() == 2) {
                        if (customerOrderEntity.getOrderType() == 1) {
                            if (cargoInfo.getBoxMaxWeight().compareTo(BigDecimal.valueOf(68)) > 0) {
                                return LocalizedR.failed("tms.box.weight.exceeded", "");
                            }
                            if (cargoInfo.getCargoQuantity() == 1) {
                                // 箱 将单箱最大重量乘以数量 <= weight
                                if (cargoInfo.getBoxMaxWeight().multiply(BigDecimal.valueOf(cargoInfo.getCargoQuantity())).compareTo(cargoInfo.getWeight()) > 0) {
                                    return LocalizedR.failed("tms.total.weight.exceeded", "");
                                }
                            }
                        }
                        if (customerOrderEntity.getOrderType() == 2) {
                            if (cargoInfo.getPkgMaxWeight().compareTo(BigDecimal.valueOf(68)) > 0) {
                                return LocalizedR.failed("tms.package.weight.exceeded", "");
                            }
                            // 包裹 将单箱最大重量乘以数量 <= weight
                            if (cargoInfo.getPkgMaxWeight().multiply(BigDecimal.valueOf(cargoInfo.getCargoQuantity())).compareTo(cargoInfo.getWeight()) > 0) {
                                return LocalizedR.failed("tms.total.weight.exceeded", "");
                            }
                        }
                    }
                    i++;
                }
                cargoInfoService.saveBatch(customerOrderEntity.getCargoInfoEntityList());
            }

            // 保存订单总重量、总体积、总数量
            SummaryResultVo result = cargoInfoService.calculateSummary(customerOrderEntity.getCargoInfoEntityList());
            customerOrderEntity.setTotalWeight(result.getTotalWeight());
            customerOrderEntity.setTotalVolume(result.getTotalVolume());
            customerOrderEntity.setCargoQuantity(result.getTotalQuantity());
            customerOrderMapper.updateById(customerOrderEntity);

            // 保存附加服务信息
            if (customerOrderEntity.getAdditionalServicesEntityList() != null && !customerOrderEntity.getAdditionalServicesEntityList().isEmpty()) {
                log.info("开始保存附加服务信息");
                List<TmsAdditionalServicesEntity> allServices = new ArrayList<>(customerOrderEntity.getAdditionalServicesEntityList());

                if (customerOrderEntity.getAddressType() == 2 || customerOrderEntity.getAddressType() == 3) {
                    // 添加尾板提货服务（类型8）
                    TmsAdditionalServicesEntity tailgatePickup = new TmsAdditionalServicesEntity();
                    BeanUtils.copyProperties(customerOrderEntity.getAdditionalServicesEntityList().get(0), tailgatePickup);
                    tailgatePickup.setId(null);
                    tailgatePickup.setAdditionalServiceType("8");   // 尾板提货服务
                    allServices.add(tailgatePickup);

                    // 添加尾板卸货服务（类型9）
                    TmsAdditionalServicesEntity tailgateDelivery = new TmsAdditionalServicesEntity();
                    BeanUtils.copyProperties(customerOrderEntity.getAdditionalServicesEntityList().get(0), tailgateDelivery);
                    tailgateDelivery.setId(null);
                    tailgateDelivery.setAdditionalServiceType("9"); // 尾板卸货服务
                    allServices.add(tailgateDelivery);
                }

                additionalServicesService.saveBatch(allServices.stream()
                        .peek(service -> service.setCustomerOrderNumber(customerOrderEntity.getCustomerOrderNumber()))
                        .collect(Collectors.toList()));
            }


            // 保存承运商审核记录
            if (customerOrderEntity.getCarrierAuditRecordsEntityList() != null && !customerOrderEntity.getCarrierAuditRecordsEntityList().isEmpty()) {
                log.info("开始保存承运商审核记录");
                for (TmsCarrierAuditRecordsEntity carrierAuditRecords : customerOrderEntity.getCarrierAuditRecordsEntityList()) {
                    carrierAuditRecords.setCustomerOrderNumber(customerOrderNumber);
                    carrierAuditRecords.setEntrustedOrderNumber(entrustedOrderNumber);
                }
            }

        } catch (Exception e) {
            log.error("创建客户订单失败: {}", e.getMessage(), e);
            return LocalizedR.failed(e.getMessage(), "tms.failed.to.create.customer.order", "");
        }
        return LocalizedR.ok("tms.successfully.created.customer.order", "");
    }




    @Override
    @Transactional(rollbackFor = Exception.class)
    public R createZDJOrder(TmsCustomerOrderEntity customerOrderEntity, Boolean isApi) {
        //获取相关信息
        try {
            //查询当前客户是否支持一票多件
//            TmsCustomerEntity customer = tmsCustomerService.getById(customerOrderEntity.getCustomerId());
//            if (customer!=null && "1".equals(customer.getIsPush())){
//                if (customerOrderEntity.getCargoInfoEntityList().size()>1){
//                    return LocalizedR.failed("tms.one.ticket.multiple.pieces.not.supported", "");
//                }
//
//            }
            //判断是否需要使用客户单号作为面单
            customerOrderEntity.setIsCustomerLabel(OrderTools.isCustomerLabel(customerOrderEntity.getCustomerOrderNumber()));
            //取收货地三字邮编获取目的地派送仓库id
            String destPostalCode = customerOrderEntity.getDestPostalCode().trim();
            Long deliveryWarehouseId = tmsOverAreaService.getWarehouseId(destPostalCode.substring(0, 3));
            if (ObjectUtil.isNull(deliveryWarehouseId)) {
                return LocalizedR.failed("tms.failed.to.get.delivery.warehouse.id", "");
            }
            customerOrderEntity.setDeliveryWarehouseId(deliveryWarehouseId);
            //获取发货地三字邮编获取揽收仓库（揽收任务回的仓库，如果收货方式是送货到仓且仓库是一级的情况，该单没有揽收,标记为1，代表是一级仓）
            // 获取三字邮编前缀
            String shipperPostalCode = customerOrderEntity.getShipperPostalCode();
            String postalPrefix = shipperPostalCode.trim().substring(0, 3);
            // 查询揽收仓库 ID
            Long collectWarehouseId = tmsOverAreaService.getWarehouseId(postalPrefix);
            if (ObjectUtil.isNull(collectWarehouseId)) {
                return LocalizedR.failed("tms.failed.to.get.delivery.warehouse.id", "");
            }

            // 设置揽收任务仓库 ID
            customerOrderEntity.setCollectWarehouseId(collectWarehouseId);

            // 判断收货方式
            if (ReceiveType.DELIVERY_TO_WAREHOUSE.getCode().equals(customerOrderEntity.getReceiveType())) {
                // 送货到仓，需要判断是否为一级仓
                TmsSiteEntity collectWarehouse = tmsSiteService.getById(collectWarehouseId);
                int grade = (collectWarehouse != null &&
                        WarehouseType.ONE_LEVEL.getCode().equals(collectWarehouse.getSiteType())) ? 1 : 0;
                customerOrderEntity.setWarehouseGrade(grade);
            } else {
                // 上门揽收默认为0级
                customerOrderEntity.setWarehouseGrade(0);
            }

        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("获取位置信息错误");
        }
        //查询客户单号是否存在
        TmsCustomerOrderEntity customerOrder = getByCustomerOrderNumber(customerOrderEntity.getCustomerOrderNumber(), true);
        if (customerOrder.getId() != null) {
            return LocalizedR.failed("tms.customer.order.already.exists", "");
        }
        String entrustedOrderNumber = customerOrderEntity.getEntrustedOrderNumber();
        if(StrUtil.isBlank(entrustedOrderNumber)){
            entrustedOrderNumber = throttleLock.call(() -> generalNewOrderNo(customerOrderEntity.getCustomerId(), customerOrderEntity.getBusinessModel()));
            customerOrderEntity.setEntrustedOrderNumber(entrustedOrderNumber);
        }
        String customerOrderNumber = customerOrderEntity.getCustomerOrderNumber();
        Integer receiveType = customerOrderEntity.getReceiveType();
        //设置订单状态
        customerOrderEntity.setOrderStatus(receiveType == 1 ? NewOrderStatus.AWAITING_PICKUP.getCode() : NewOrderStatus.AWAITING_SHIPMENT.getCode());
        customerOrderEntity.setSubFlag(false);

        // 保存订单总重量、总体积、总数量
        SummaryResultVo result = cargoInfoService.calculateSummary(customerOrderEntity.getCargoInfoEntityList());
        customerOrderEntity.setTotalWeight(result.getTotalWeight());
        customerOrderEntity.setTotalVolume(result.getTotalVolume());
        customerOrderEntity.setCargoQuantity(result.getTotalQuantity());
        customerOrderEntity.setOrderWay(isApi ? OrderWayConstants.API : OrderWayConstants.CUSTOMER_CLIENT);
        if (customerOrderEntity.getForecastedPrice()==null){
            BigDecimal price = calculatePrice(customerOrderEntity.getCustomerId(), customerOrderEntity.getShipperPostalCode(), customerOrderEntity.getDestPostalCode(), result.getTotalWeight(), result.getTotalVolume());
            customerOrderEntity.setForecastedPrice(price);
        }
        int insert = customerOrderMapper.insert(customerOrderEntity);
        boolean isSaved = insert > 0;
        if (!isSaved) {
            return LocalizedR.failed("tms.failed.to.save.customer.order", "");
        }

        // 保存货物信息
        if (customerOrderEntity.getCargoInfoEntityList() != null && !customerOrderEntity.getCargoInfoEntityList().isEmpty()) {
            log.info("开始保存货物信息");
            int i = 0; // 初始化计数器
            for (TmsCargoInfoEntity cargoInfo : customerOrderEntity.getCargoInfoEntityList()) {
                cargoInfo.setBagNum(String.format("%03d", i + 1));
                cargoInfo.setCustomerOrderNumber(customerOrderNumber);
                cargoInfo.setEntrustedOrderNumber(entrustedOrderNumber);
                if (cargoInfo.getBoxNum() == null) {
                    //箱号默认为序号
                    cargoInfo.setBoxNum(cargoInfo.getBagNum());
                }
                i++;
            }
            cargoInfoService.saveBatch(customerOrderEntity.getCargoInfoEntityList());
        }

        //生成子单号
        HashMap<String, String> subOrderNumbers = createSubOrderNumber(customerOrderEntity);
//        // 生成运输任务单，区分揽收还是派送任务
//        transportTaskOrderService.createTransportTaskOrder(customerOrderEntity);
        return isApi ? R.ok(new ZdjApiVo(customerOrderEntity.getCustomerOrderNumber(), OrderTools.safeSubstringOrder(customerOrderEntity.getEntrustedOrderNumber()), subOrderNumbers))
                : LocalizedR.ok("tms.successfully.created.customer.order", "");

    }

    //价格计算
    private BigDecimal calculatePrice(Long customerId,String shipperPostalCode,String destPostalCode,BigDecimal totalWeight,BigDecimal totalVolume) {
        try {
            TmsOrderPriceCalculationVo order = new TmsOrderPriceCalculationVo();
            order.setCustomerId(customerId==null?0:customerId);
            order.setShipperPostalCode(shipperPostalCode);
            order.setDestPostalCode(destPostalCode);
            order.setTotalWeight(totalWeight);
            order.setTotalVolume(totalVolume.multiply(new BigDecimal("1000000")));
            PriceCalculationRequestVo para = new PriceCalculationRequestVo();
            para.setOrders(Collections.singletonList(order));
            PriceCalculationResultVo resultVo = tmsReceivableService.calculatePrice(para);
            return resultVo.getSuccessCount() > 0
                    ? resultVo.getDetails().get(0).getFinalPrice()
                    : null;
        }catch (Exception e){
            return null;
        }
    }

    // 推送商家客户端导入订单至小包
    @Override
    public R syncCreateOrder(List<TmsCustomerOrderEntity> customerOrder) {
        // String url = "https://api.otms.ltianexp.com/v1/api/orderNew/createOrder";
        String url = "https://api.jygjexp.com/v1/api/orderNew/createOrder";

        for (TmsCustomerOrderEntity order : customerOrder) {
            // 查询当前主单下的所有子单（箱号）
            LambdaQueryWrapper<TmsCargoInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.likeRight(TmsCargoInfoEntity::getEntrustedOrderNumber, order.getEntrustedOrderNumber());
            List<TmsCargoInfoEntity> cargoInfos = cargoInfoService.list(queryWrapper);
            // 校验：是否存在重量超过 30kg 的子单
            boolean overweightExists = cargoInfos.stream().anyMatch(subOrder -> subOrder.getWeight() != null && subOrder.getWeight().compareTo(BigDecimal.valueOf(30)) > 0);
            if (overweightExists) {
                // 记录日志并跳过该主单
                tmsOrderLogService.saveLog(order.getEntrustedOrderNumber(), 400, "存在子单重量超过30kg，跳过推送", "");
                continue;
            }

            for (TmsCargoInfoEntity subOrder : cargoInfos) {
                //构建数据
                HashMap<String, Object> dateMap = new HashMap<>();
                dateMap.put("channelCode", "CA226");
                // 使用子单号作为 referenceNo 和 trackingNo，保持和子单唯一
                dateMap.put("referenceNo", subOrder.getEntrustedOrderNumber());
                dateMap.put("trackingNo", subOrder.getEntrustedOrderNumber());
                dateMap.put("productType", "1");
                dateMap.put("pweight", formatDecimal(subOrder.getWeight()));
                dateMap.put("pieces", subOrder.getCargoQuantity());
                dateMap.put("insured", "0");
                dateMap.put("consigneeName", order.getReceiverName());
                dateMap.put("consigneeCountryCode", "CA");

                // 获取收货地省份与城市
                String[] split = order.getDestination().split("/");
                String province = null;
                String city = null;
                if (split.length >= 3) {
                    province = split[1];
                    city = split[2];
                }
                dateMap.put("consigneeProvince", province);
                dateMap.put("consigneeCity", city);
                dateMap.put("consigneeAddress", order.getDestAddress());
                dateMap.put("consigneePostcode", order.getDestPostalCode());
                dateMap.put("consigneeMobile", order.getReceiverPhone());
                dateMap.put("consigneePhone", order.getReceiverPhone());
                dateMap.put("consigneeEmail", "");
                dateMap.put("shipperName", order.getShipperName());
                dateMap.put("shipperCountryCode", "CA");

                // 发货地省份城市
                String[] splitTwo = order.getOrigin().split("/");
                String provinceTwo = null;
                String cityTwo = null;
                if (splitTwo.length >= 3) {
                    provinceTwo = splitTwo[1];
                    cityTwo = splitTwo[2];
                }
                dateMap.put("shipperProvince", provinceTwo);
                dateMap.put("shipperCity", cityTwo);
                dateMap.put("shipperAddress", order.getShipperAddress());
                dateMap.put("shipperPostcode", order.getShipperPostalCode());
                dateMap.put("shipperPhone", order.getShipperPhone());
                dateMap.put("currencyCode", "USD");
                dateMap.put("returnLabel", "1");

                // 商品信息
                HashMap<String, Object> apiList = new HashMap<>();
                apiList.put("cname", "日用品");
                apiList.put("ename", "Daily Necessities");
                apiList.put("price", "1");
                apiList.put("quantity", subOrder.getCargoQuantity());
                apiList.put("sku", "");
                apiList.put("unitCode", "PCE");
                apiList.put("weight", formatDecimal(subOrder.getWeight()));
                dateMap.put("apiOrderItemList", apiList);

                // 每个请求只传单个箱子
                List<Map<String, Object>> boxList = new ArrayList<>();
                Map<String, Object> box = new HashMap<>();
                box.put("length", formatDecimal(subOrder.getLength()));
                box.put("width", formatDecimal(subOrder.getWidth()));
                box.put("height", formatDecimal(subOrder.getHeight()));
                box.put("rweight", formatDecimal(subOrder.getWeight()));
                box.put("quantity", subOrder.getCargoQuantity());
                box.put("bagNum", subOrder.getEntrustedOrderNumber());
                boxList.add(box);
                dateMap.put("apiOrderVolumeList", boxList);

                org.json.JSONObject jsonObject = new org.json.JSONObject(dateMap);
                HashMap<String, String> headerMap = new HashMap<>();
                headerMap.put("code", "220999");
                String apiKey = "198e900a5a66403b86f6c916d05b43ae";
//                String apiKey = "1958538970ce46b79081437d8d3d35b4";  // 测试key
                headerMap.put("apiKey", apiKey);
                headerMap.put("Content-Type", "application/json");

                LocalDateTime now = LocalDateTime.now();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                String osName = System.getProperty("os.name").toLowerCase();
                if (!osName.contains("win")) {
                    now = now.plusHours(12);
                }
                headerMap.put("timestamp", now.format(formatter));
                headerMap.put("sign", OrderTools.getMD5("220999" + apiKey));

                try {
                    String result = OkHttpUtil.doPostJson(url, jsonObject.toString(), headerMap);
                    ObjectMapper mapper = new ObjectMapper();
                    JsonNode node = mapper.readTree(result);
                    if (node.has("code") && node.get("code").asInt() == 1 && node.has("data")) {
                        JsonNode dataNode = node.get("data");
                        String trackingNo = dataNode.get("trackingNo").asText();
                        String labelPath = dataNode.get("labelPath").asText();

                        TmsThirdPartPostEntity postEntity = new TmsThirdPartPostEntity();
                        postEntity.setChannel("UNI");
                        postEntity.setNbOrderNo(subOrder.getEntrustedOrderNumber());
                        postEntity.setChannelOrderNo(trackingNo);
                        postEntity.setCustomerOrderNo(subOrder.getBoxNum());
                        postEntity.setLabelUrl(labelPath);
                        postEntity.setTime(LocalDateTime.now());
                        tmsThirdPartPostService.save(postEntity);

                        // 将信息记录订单表中的推送字段
                        customerOrderMapper.update(null, new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                                .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, subOrder.getEntrustedOrderNumber())
                                .eq(TmsCustomerOrderEntity::getSubFlag, Boolean.TRUE)
                                .set(TmsCustomerOrderEntity::getPushOrder, trackingNo)
                                .set(TmsCustomerOrderEntity::getPushLabel, labelPath)
                        );

                        // 记录成功日志
                        tmsOrderLogService.saveLog(subOrder.getEntrustedOrderNumber(), 200, "推送成功, 订单号: " + trackingNo + "返回信息： " + result, "");
/*                        order.setPushOrder(trackingNo);
                        order.setPushLabel(labelPath);
                        customerOrderMapper.updateById(order);*/
                    } else {
                        tmsOrderLogService.saveLog(subOrder.getEntrustedOrderNumber(), 500, "推送失败, 返回信息： " + result, "");
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                    // 记录异常日志
                    tmsOrderLogService.saveLog(subOrder.getEntrustedOrderNumber(), 500, "异常信息: " + e.getMessage(), "");
                }
            }
        }
        return R.ok("推送完成");
    }

    // 异常订单推送小包
    @Override
    public R exOrderPushXb(List<String> subOrderNoList) {
        String url = "https://api.jygjexp.com/v1/api/orderNew/createOrder";

        // 查询当前单号的主单信息
        TmsCustomerOrderEntity order = customerOrderMapper.selectOne(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, subOrderNoList.get(0))
                .eq(TmsCustomerOrderEntity::getSubFlag, true)
                .last("limit 1")
        );

        // 查询当前（箱号）的货物信息
        LambdaQueryWrapper<TmsCargoInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TmsCargoInfoEntity::getEntrustedOrderNumber, subOrderNoList);
        List<TmsCargoInfoEntity> cargoInfos = cargoInfoService.list(queryWrapper);

        for (TmsCargoInfoEntity subOrder : cargoInfos) {
            //构建数据
            HashMap<String, Object> dateMap = new HashMap<>();
            dateMap.put("channelCode", "CA226");
            // 使用子单号作为 referenceNo 和 trackingNo，保持和子单唯一
            dateMap.put("referenceNo", subOrder.getEntrustedOrderNumber());
            dateMap.put("trackingNo", subOrder.getEntrustedOrderNumber());
            dateMap.put("productType", "1");
            dateMap.put("pweight", formatDecimal(subOrder.getWeight()));
            dateMap.put("pieces", subOrder.getCargoQuantity());
            dateMap.put("insured", "0");
            dateMap.put("consigneeName", order.getReceiverName());
            dateMap.put("consigneeCountryCode", "CA");

            // 获取收货地省份与城市
            String[] split = order.getDestination().split("/");
            String province = null;
            String city = null;
            if (split.length >= 3) {
                province = split[1];
                city = split[2];
            }
            dateMap.put("consigneeProvince", province);
            dateMap.put("consigneeCity", city);
            dateMap.put("consigneeAddress", order.getDestAddress());
            dateMap.put("consigneePostcode", order.getDestPostalCode());
            dateMap.put("consigneeMobile", order.getReceiverPhone());
            dateMap.put("consigneePhone", order.getReceiverPhone());
            dateMap.put("consigneeEmail", "");
            dateMap.put("shipperName", order.getShipperName());
            dateMap.put("shipperCountryCode", "CA");

            // 发货地省份城市
            String[] splitTwo = order.getOrigin().split("/");
            String provinceTwo = null;
            String cityTwo = null;
            if (splitTwo.length >= 3) {
                provinceTwo = splitTwo[1];
                cityTwo = splitTwo[2];
            }
            dateMap.put("shipperProvince", provinceTwo);
            dateMap.put("shipperCity", cityTwo);
            dateMap.put("shipperAddress", order.getShipperAddress());
            dateMap.put("shipperPostcode", order.getShipperPostalCode());
            dateMap.put("shipperPhone", order.getShipperPhone());
            dateMap.put("currencyCode", "USD");
            dateMap.put("returnLabel", "1");

            // 商品信息
            HashMap<String, Object> apiList = new HashMap<>();
            apiList.put("cname", "日用品");
            apiList.put("ename", "Daily Necessities");
            apiList.put("price", "1");
            apiList.put("quantity", subOrder.getCargoQuantity());
            apiList.put("sku", "");
            apiList.put("weight", formatDecimal(subOrder.getWeight()));
            dateMap.put("apiOrderItemList", apiList);

            // 每个请求只传单个箱子
            List<Map<String, Object>> boxList = new ArrayList<>();
            Map<String, Object> box = new HashMap<>();
            box.put("length", formatDecimal(subOrder.getLength()));
            box.put("width", formatDecimal(subOrder.getWidth()));
            box.put("height", formatDecimal(subOrder.getHeight()));
            box.put("rweight", formatDecimal(subOrder.getWeight()));
            box.put("quantity", subOrder.getCargoQuantity());
            box.put("bagNum", subOrder.getEntrustedOrderNumber());
            boxList.add(box);
            dateMap.put("apiOrderVolumeList", boxList);

            org.json.JSONObject jsonObject = new org.json.JSONObject(dateMap);
            HashMap<String, String> headerMap = new HashMap<>();
            headerMap.put("code", "220999");
            String apiKey = "198e900a5a66403b86f6c916d05b43ae";
            headerMap.put("apiKey", apiKey);
            headerMap.put("Content-Type", "application/json");

            LocalDateTime now = LocalDateTime.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String osName = System.getProperty("os.name").toLowerCase();
            if (!osName.contains("win")) {
                now = now.plusHours(12);
            }
            headerMap.put("timestamp", now.format(formatter));
            headerMap.put("sign", OrderTools.getMD5("220999" + apiKey));

            try {
                String result = OkHttpUtil.doPostJson(url, jsonObject.toString(), headerMap);
                ObjectMapper mapper = new ObjectMapper();
                JsonNode node = mapper.readTree(result);
                if (node.has("code") && node.get("code").asInt() == 1 && node.has("data")) {
                    JsonNode dataNode = node.get("data");
                    String trackingNo = dataNode.get("trackingNo").asText();
                    String labelPath = dataNode.get("labelPath").asText();

                    TmsThirdPartPostEntity postEntity = new TmsThirdPartPostEntity();
                    postEntity.setChannel("UNI");
                    postEntity.setNbOrderNo(subOrder.getEntrustedOrderNumber());
                    postEntity.setChannelOrderNo(trackingNo);
                    postEntity.setCustomerOrderNo(subOrder.getBoxNum());
                    postEntity.setLabelUrl(labelPath);
                    postEntity.setTime(LocalDateTime.now());
                    tmsThirdPartPostService.save(postEntity);

                    // 将信息记录订单表中的推送字段
                    customerOrderMapper.update(null, new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                            .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, subOrder.getEntrustedOrderNumber())
                            .eq(TmsCustomerOrderEntity::getSubFlag, Boolean.TRUE)
                            .set(TmsCustomerOrderEntity::getPushOrder, trackingNo)
                            .set(TmsCustomerOrderEntity::getPushLabel, labelPath)
                    );

                    // 记录成功日志
                    tmsOrderLogService.saveLog(subOrder.getEntrustedOrderNumber(), 200, "重推成功, 订单号: " + trackingNo + "返回信息： " + result, "");
                } else {
                    tmsOrderLogService.saveLog(subOrder.getEntrustedOrderNumber(), 500, "重推失败, 返回信息： " + result, "");
                }
            } catch (IOException e) {
                e.printStackTrace();
                // 记录异常日志
                tmsOrderLogService.saveLog(subOrder.getEntrustedOrderNumber(), 500, "异常信息: " + e.getMessage(), "");
            }
        }
        return R.ok("重推完成");
    }



/*    @Override
    public R syncCreateOrder(List<TmsCustomerOrderEntity> customerOrder) {
        String url = "https://api.otms.ltianexp.com/v1/api/orderNew/createOrder";

        // 循环推送订单（一票多件）
        for (TmsCustomerOrderEntity order : customerOrder) {
            //构建数据
            HashMap<String, Object> dateMap = new HashMap<>();
            dateMap.put("channelCode", "CA226");
            dateMap.put("referenceNo", order.getEntrustedOrderNumber());
            dateMap.put("trackingNo", order.getEntrustedOrderNumber());
            dateMap.put("productType", "1");
            dateMap.put("pweight", formatDecimal(order.getTotalWeight()));
//            dateMap.put("pweight", "29");
            dateMap.put("pieces", order.getCargoQuantity());
            dateMap.put("insured", "0");
            dateMap.put("consigneeName", order.getReceiverName());
            dateMap.put("consigneeCountryCode", "CA");

            // 获取收货地省份与城市
            String[] split = order.getDestination().split("/");
            String province = null;
            String city = null;
            if (split.length >= 3) {
                province = split[1]; // "ON"
                city = split[2];      // "ToronTo"
            }
            dateMap.put("consigneeProvince", province);
            dateMap.put("consigneeCity", city);
            dateMap.put("consigneeAddress", order.getDestAddress());
            dateMap.put("consigneePostcode", order.getDestPostalCode());
            dateMap.put("consigneeMobile", order.getReceiverPhone());
            dateMap.put("consigneePhone", order.getReceiverPhone());
            dateMap.put("consigneeEmail", "");
            dateMap.put("shipperName", order.getShipperName());
            dateMap.put("shipperCountryCode", "CA");

            // 获取发货地省份与城市
            String[] splitTwo = order.getOrigin().split("/");
            String provinceTwo = null;
            String cityTwo = null;
            if (splitTwo.length >= 3) {
                provinceTwo = splitTwo[1]; // "ON"
                cityTwo = splitTwo[2];      // "ToronTo"
            }
            dateMap.put("shipperProvince", provinceTwo);
            dateMap.put("shipperCity", cityTwo);
            dateMap.put("shipperAddress", order.getShipperAddress());
            dateMap.put("shipperPostcode", order.getShipperPostalCode());
            dateMap.put("shipperPhone", order.getShipperPhone());
            dateMap.put("currencyCode", "USD");
            dateMap.put("returnLabel", "1");

            // 设置商品信息
            HashMap<String, Object> apiList = new HashMap<>();
            apiList.put("cname", "日用品");
            apiList.put("ename", "Daily Necessities");
            apiList.put("price", "1");
            apiList.put("quantity", order.getCargoQuantity());
            apiList.put("sku", "");
            apiList.put("weight", formatDecimal(order.getTotalWeight()));
            // 将商品信息加入外部集合
            dateMap.put("apiOrderItemList", apiList);

            // 设置一票多件箱信息
            List<Map<String, Object>> boxList = new ArrayList<>();

            LambdaQueryWrapper<TmsCargoInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.likeRight(TmsCargoInfoEntity::getEntrustedOrderNumber, order.getEntrustedOrderNumber());

            List<TmsCargoInfoEntity> cargoInfos = cargoInfoService.list(queryWrapper);

            // 遍历货物信息，构建箱信息列表，保留两位小数
            for (TmsCargoInfoEntity subOrder : cargoInfos) {
                Map<String, Object> box = new HashMap<>();
                box.put("length", formatDecimal(subOrder.getLength()));       // 长度
                box.put("width", formatDecimal(subOrder.getWidth()));         // 宽度
                box.put("height", formatDecimal(subOrder.getHeight()));       // 高度
                box.put("rweight", formatDecimal(subOrder.getWeight()));      // 重量
                box.put("quantity", subOrder.getCargoQuantity());             // 件数
                box.put("bagNum", subOrder.getEntrustedOrderNumber());                      // 箱号
                boxList.add(box);
            }
            // 加入主参数集合
            dateMap.put("apiOrderVolumeList", boxList);

            org.json.JSONObject jsonObject = new org.json.JSONObject(dateMap);
            HashMap<String, String> headerMap = new HashMap<>();
            headerMap.put("code", "220999");
            String apiKey = "198e900a5a66403b86f6c916d05b43ae";    // 生产key
            //String apiKey = "1958538970ce46b79081437d8d3d35b4";  // 测试key
            headerMap.put("apiKey", apiKey);
            headerMap.put("Content-Type", "application/json");
            // 获取当前时间
            LocalDateTime now = LocalDateTime.now();
            // 格式化输出
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String osName = System.getProperty("os.name").toLowerCase();
            if (!osName.contains("win")) {
                now = now.plusHours(12);
            }
            headerMap.put("timestamp", now.format(formatter));
            headerMap.put("sign", OrderTools.getMD5("220999" + apiKey));

            try {
                String result = OkHttpUtil.doPostJson(url, jsonObject.toString(), headerMap);
                ObjectMapper mapper = new ObjectMapper();
                JsonNode node = mapper.readTree(result);
                // 解析返回结果，将返回单号与返回面单地址存入订单表
                if (node.has("code") && node.get("code").asInt() == 1 && node.has("data")) {
                    // 记录换单列表
                    JsonNode dataNode = node.get("data");
                    String trackingNo = dataNode.get("trackingNo").asText();
                    String labelPath = dataNode.get("labelPath").asText();
                    TmsThirdPartPostEntity postEntity = new TmsThirdPartPostEntity();
                    postEntity.setChannel("UNI");  // 渠道为UNI
                    postEntity.setNbOrderNo(order.getEntrustedOrderNumber());   // NB单号
                    postEntity.setChannelOrderNo(trackingNo);     // 渠道单号
                    postEntity.setLabelUrl(labelPath);    // 渠道面单地址
                    postEntity.setTime(LocalDateTime.now());
                    tmsThirdPartPostService.save(postEntity);


                    // 存储一下订单推送信息
                    order.setPushOrder(dataNode.get("trackingNo").asText());
                    order.setPushLabel(dataNode.get("labelPath").asText());
                    customerOrderMapper.updateById(order);
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return R.ok("推送完成");
    }*/


    // 校验货物信息，小数点位数
    private BigDecimal formatDecimal(BigDecimal value) {
        if (value == null) {
            return BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
        }
        return value.setScale(2, RoundingMode.HALF_UP);
    }

    //生成子单号
    private HashMap<String, String> createSubOrderNumber(TmsCustomerOrderEntity customerOrder) {
        HashMap<String, String> subOrderNos = new HashMap<>();
        String baseOrder = customerOrder.getEntrustedOrderNumber();
        List<TmsCargoInfoEntity> cargoInfoEntityList = customerOrder.getCargoInfoEntityList();
        for (int i = 0; i < cargoInfoEntityList.size(); i++) {
            TmsCargoInfoEntity tmsCargoInfoEntity = cargoInfoEntityList.get(i);
            String subOrderNumber = baseOrder + String.format("%03d", i + 1);
            customerOrder.setTotalWeight(tmsCargoInfoEntity.getWeight());

            // 直接累加货物的体积（单位：cm³ -> m³）
            BigDecimal volume = tmsCargoInfoEntity.getLength()
                    .multiply(tmsCargoInfoEntity.getWidth())
                    .multiply(tmsCargoInfoEntity.getHeight())
                    .divide(new BigDecimal("1000000"), 6, RoundingMode.HALF_UP); // 转换为 m³
            // 设置最小体积为0.001m³ (1升)
            BigDecimal minVolume = new BigDecimal("0.001");
            volume = volume.max(minVolume);
            customerOrder.setTotalVolume(volume);
            customerOrder.setCargoQuantity(tmsCargoInfoEntity.getCargoQuantity());
            customerOrder.setSubFlag(true);
            customerOrder.setEntrustedOrderNumber(subOrderNumber);
            customerOrder.setId(null);
            customerOrder.setForecastedPrice(null);
            //保存子单号
            this.save(customerOrder);
            subOrderNos.put(tmsCargoInfoEntity.getBoxNum(), subOrderNumber);
            // 保存轨迹
            String trackValue = customerOrder.getReceiveType().equals(NewOrderStatus.AWAITING_PICKUP.getCode()) ? "AWAITING_PICKUP" : "AWAITING_SHIPMENT";
            orderTrackService.addTrack(TrackDto.builder().orderNo(subOrderNumber)
                    .customerNo(customerOrder.getCustomerOrderNumber())
                    .orderStatus(trackValue)
                    .site(customerOrder.getOrigin())
                    .postalCode(customerOrder.getShipperPostalCode())
                    .trackLink(OrderTrackLink.ORDER_RECEIVED.getCode()).build());
            //更新货物子单号
            tmsCargoInfoEntity.setEntrustedOrderNumber(subOrderNumber);
            tmsCargoInfoService.updateById(tmsCargoInfoEntity);
        }
        return subOrderNos;
    }

    //订单调度-判断承运商区域(经纬度)
    @Override
    public Boolean isCarrierArea(TmsIsCarrierAreaVo vo) {
        // 先判断承运商是否为空
        if (ObjectUtil.isNull(vo.getCarrierId())) {
            return Boolean.FALSE;
        }
        // 根据承运商id查询查询承运商区域
        TmsRegionEntity carrierArea = regionMapper.selectOne(new LambdaQueryWrapper<TmsRegionEntity>()
                .eq(TmsRegionEntity::getCarrierId, vo.getCarrierId()).eq(TmsRegionEntity::getIsValid, true), false);

        // 获取区域，解析区域经纬度
        if (carrierArea != null) {
            String areaGeometry = carrierArea.getAreaGeometry();
            // 解析 JSON 数组
            JSONArray jsonArray = JSONUtil.parseArray(areaGeometry);
            JSONObject jsonObject = jsonArray.getJSONObject(0);

            // 获取 ne 和 sw 的坐标
            JSONObject ne = jsonObject.getJSONObject("ne");
            JSONObject sw = jsonObject.getJSONObject("sw");

            double maxLat = ne.getDouble("lat"); // 最大纬度（北）
            double minLat = sw.getDouble("lat"); // 最小纬度（南）
            double maxLng = ne.getDouble("lng"); // 最大经度（东）
            double minLng = sw.getDouble("lng"); // 最小经度（西）
            // 获取客户订单目的地经纬度
            double[] latLng = postalCodeService.getLatLng(vo.getDestAddress());
//            double destLat = latLng[0];
//            double destLng = latLng[1];
            double destLat = 43.735953187160888;
            double destLng = -79.59612630726815;

            // 判断目的地是否在矩形区域内
            if (destLat >= minLat && destLat <= maxLat
                    && destLng >= minLng && destLng <= maxLng) {
                return Boolean.TRUE;
            } else {
                return Boolean.FALSE;
            }

        }
        return Boolean.FALSE;
    }

    // 订单调度-判断承运商区域(三字邮编)
    @Override
    public Boolean isPostCarrierArea(TmsIsCarrierAreaVo vo) {
        // 先判断承运商是否为空
        if (ObjectUtil.isNull(vo.getCarrierId())) {
            return Boolean.FALSE;
        }

        // 获取要匹配的邮编，如果是六位邮编，则取前三位
        String shipperPostalCode = vo.getShipperPostalCode();
        if (StrUtil.isNotBlank(shipperPostalCode) && shipperPostalCode.length() > 3) {
            shipperPostalCode = shipperPostalCode.substring(0, 3);
        }

        // 根据承运商id查询查询承运商区域
        MPJLambdaWrapper<TmsRegionEntity> wrapper = new MPJLambdaWrapper<TmsRegionEntity>()
                .select(TmsRegionEntity::getAreaGeometry)
                .select(TmsZipZoneEntity::getOverlayPostcode)
                .leftJoin(TmsZipZoneEntity.class, TmsZipZoneEntity::getRegionId, TmsRegionEntity::getRegionId)
                .eq(TmsRegionEntity::getCarrierId, vo.getCarrierId())
                .eq(TmsRegionEntity::getIsValid, true);

        List<TmsReginDTO> resultList = regionMapper.selectJoinList(TmsReginDTO.class, wrapper);

        // 判断查询结果是否为空
        if (CollUtil.isEmpty(resultList)) {
            return Boolean.FALSE;
        }

        // 遍历每个区域，判断邮编是否匹配
        for (TmsReginDTO result : resultList) {
            if (StrUtil.isBlank(result.getOverlayPostcode())) {
                continue;  // 如果 OverlayPostcode 为空，则跳过当前区域
            }

            // 解析区域邮编并匹配
            Set<String> overlayPostcodeSet = Arrays.stream(result.getOverlayPostcode().split(","))
                    .map(String::trim)  // 去掉前后空格
                    .map(String::toUpperCase)  // 统一转为大写
                    .collect(Collectors.toSet());

            // 如果匹配到，直接返回 true
            if (overlayPostcodeSet.contains(shipperPostalCode.trim())) {
                return Boolean.TRUE;
            }
        }

        // 如果遍历完所有区域都没有匹配，返回 false
        return Boolean.FALSE;
    }

    //校验新增订单参数
    private String validateOrder(TmsCustomerOrderEntity order) {
        if (null == order.getCustomerId() || order.getCustomerId() == 0) {
            return "委托客户不能为空";
        }
        if (order.getCargoInfoEntityList() == null || order.getCargoInfoEntityList().isEmpty()) {
            return "货物信息不能为空";
        }
        // 始发地和目的地不能为同一个城市
        if (order.getOrigin() != null && order.getDestination() != null) {
            if ((order.getOrigin() + order.getShipperAddress()).equals(order.getDestination() + order.getDestAddress())) {
                return "始发地详细地址和目的地详细地址不能相同";
            }
        }
        if (order.getShipperName() == null || order.getShipperName().isEmpty()) {
            return "发货人姓名不能为空";
        }
        if (order.getShipperPhone() == null || order.getShipperPhone().isEmpty()) {
            return "发货人电话不能为空";
        }
        String routeNo1 = tmsOverAreaService.getRouteNo(order.getShipperPostalCode());
        if (StringUtils.isBlank(routeNo1)) {
            return "发货人邮编不在覆盖范围";
        }
        String routeNo2 = tmsOverAreaService.getRouteNo(order.getDestPostalCode());
        if (StringUtils.isBlank(routeNo2)) {
            return "收货人邮编不在覆盖范围";
        }
        if(StrUtil.isNotBlank(order.getCustomerOrderNumber()) && !LineValidator.isAlphaNumericOnly(order.getCustomerOrderNumber())){
            return "客户单号仅支持字母和数字组合";
        }


        return null; // 校验通过
    }

    /**
     * 新NB客户单号生成规则
     *
     * @param customerId
     * @return
     */
    private static final int MAX_RETRY = 10;

    @Override
    public String generalNewOrderNo(Long customerId, Integer businessModel) {
        int retryCount = 0;
        while (retryCount < MAX_RETRY) {
            System.out.println("当前重试次数" + retryCount);
            LambdaQueryWrapper<TmsCustomerEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.select(TmsCustomerEntity::getId, TmsCustomerEntity::getCount)
                    .eq(TmsCustomerEntity::getId, customerId);
            TmsCustomerEntity customer = customerMapper.selectOne(wrapper);
            if (customer == null) {
                throw new RuntimeException("未找到客户订单信息");
            }
            Integer count = customer.getCount();
            if (count == null) {
                count = 0;
            }
            String yyyy = OrderTools.getFormattedDateMinusDays("yyyy", 0);
            StringBuffer str = new StringBuffer();
            str.append("N");
            str.append(yyyy.substring(yyyy.length() - 2));
            if (customer.getId() < 10) {
                str.append("0");
            }
            str.append(customer.getId());
            if (businessModel == 1) {
                str.append("P");
            } else if (businessModel == 2) {
                str.append("D");
            } else if (businessModel == 3) {
                str.append("T");
            }
            str.append(String.format("%07d", count == 0 ? 1 : count + 1));

            String orderNo = str.toString();
            TmsCustomerEntity entity = new TmsCustomerEntity();
            entity.setId(customer.getId());
            entity.setCount(count + 1);

            int updatedRows = customerMapper.updateById(entity);
            if (updatedRows > 0) {
                if (Boolean.FALSE.equals(checkOrderNo(orderNo))) {
                    // 已存在，尝试下一次 retry
                    retryCount++;
                    continue;
                }
                return orderNo;
            } else {
                retryCount++;
                try {
                    Thread.sleep(10);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }

        }
        throw new RuntimeException("更新客户订单计数失败，重试超过最大次数");
    }


    //单号去重检查(原子锁)
    private Boolean checkOrderNo(String orderNo) {
        // Redis 去重检查（只加这一段）
        try {
            String redisKey = "entrusted_order:" + orderNo;
            Boolean flag = redisTemplate.opsForValue().setIfAbsent(redisKey, orderNo, 5, TimeUnit.DAYS);
            System.out.println(orderNo + "是否插入成功::" + flag);
            return flag;
        } catch (Exception e) {
            System.out.println("Redis连接异常");
            e.printStackTrace();
            return true;
        }
    }


    // 卡派订单询价
    @Override
    public R getOrderPrice(TmsEntrustedOrderVo vo) {
        // 获取始发地-目的地，总体积，总重量，总数量
        String origin = vo.getOrigin();
        String destination = vo.getDestination();
        // 将始发地和目的地，按/分割出来，并且重新组合省-市
        String[] origins = origin.split("/");
        String[] destinations = destination.split("/");
        if (origins.length < 2 || destinations.length < 2) {
            return LocalizedR.failed("tms.origin.or.destination.error", "");
        }
        String provinceCity = origins[1] + "-" + origins[2];
        String provinceCity2 = destinations[1] + "-" + destinations[2];
        BigDecimal weight = vo.getSumOrderWeight();
        BigDecimal volume = vo.getSumOrderVolume();

        // 定义一个map，用于存储运费信息，并返回
        Map<String, Object> result = new HashMap<>();

        // 1.先判断订单类型：1=托盘（体积/172.8和重量匹配价格取最低*利润），2=包裹（只算重量匹配价格*利润）
        if (vo.getOrderType() == 1) {
            // 查询托盘的基础运费信息
            TmsBasicFreightEntity basicFreight = basicFreightMapper.selectOne(
                    new LambdaQueryWrapper<TmsBasicFreightEntity>()
                            .eq(TmsBasicFreightEntity::getOrigin, provinceCity)
                            .eq(TmsBasicFreightEntity::getDestination, provinceCity2)
            );

            if (null == basicFreight) {
                //return R.failed("未找到对应的运费规则");
                return LocalizedR.failed("tms.no.corresponding.shipping.rule.was.found", "");
            }

            // 重量费用计算
            Map<String, Object> weightResult = calculateWeightCost(weight, basicFreight);
            // 体积费用计算
            Map<String, Object> volumeResult = calculateVolumeCost(volume, basicFreight);

            BigDecimal weightCost = (BigDecimal) weightResult.get("totalCost");
            BigDecimal volumeCost = (BigDecimal) volumeResult.get("totalCost");

            // 体积运费与重量运费取较大值
            BigDecimal basicPrice = weightCost.max(volumeCost);

            result.put("basicPrice", basicPrice);
            result.put("calculationFormula", weightCost.compareTo(volumeCost) < 0 ? volumeResult.get("calculationFormula") : weightResult.get("calculationFormula"));
            result.put("equation", weightCost.compareTo(volumeCost) < 0 ? true : false);
            result.put("totalPrice", basicPrice);
            // 附加费
            // 预约提货费
            // 总价格
            result.put("totalPrice", basicPrice);
            return R.ok(result);
        } else {
            // 2. 计算包裹的运费
            R r = calculatePackageFreight(provinceCity, provinceCity2, weight);
            if (r.getCode() != 0) {
                return LocalizedR.failed("tms.no.matching.package.shipping.rules.were.found", "");
            }

            // 解析返回数据
            Map<String, Object> freightData = (Map<String, Object>) r.getData();
            BigDecimal totalCost = (BigDecimal) freightData.get("totalCost");
            String calculationFormula = (String) freightData.get("calculationFormula");
            // 基础运费
            result.put("basicPrice", totalCost);
            // 添加计算公式返回
            result.put("calculationFormula", calculationFormula);
            // 附加费
            // 预约提货费
            // 总价格
            result.put("totalPrice", totalCost);
            return R.ok(result);
        }
    }


    /**
     * 根据重量匹配对应的运费
     */
    private Map<String, Object> calculateWeightCost(BigDecimal weight, TmsBasicFreightEntity basicFreight) {
        if (weight == null || weight.compareTo(BigDecimal.ZERO) <= 0) {
            weight = BigDecimal.ZERO;
        }

        Map<String, Object> result = new HashMap<>();
        // 记录原始重量
        BigDecimal originalWeight = weight;
        // 将weight的kg单位转换为cwt
        weight = weight.multiply(BigDecimal.valueOf(2.20462)).divide(BigDecimal.valueOf(100));
        BigDecimal unitPricePer100Lb; // 每 100lb 的单价
        if (weight.compareTo(BigDecimal.valueOf(10000)) >= 0) {
            unitPricePer100Lb = basicFreight.getCwt10000();
        } else if (weight.compareTo(BigDecimal.valueOf(5000)) >= 0) {
            unitPricePer100Lb = basicFreight.getCwt5000();
        } else if (weight.compareTo(BigDecimal.valueOf(2000)) >= 0) {
            unitPricePer100Lb = basicFreight.getCwt2000();
        } else if (weight.compareTo(BigDecimal.valueOf(1000)) >= 0) {
            unitPricePer100Lb = basicFreight.getCwt1000();
        } else {
            unitPricePer100Lb = basicFreight.getLtl();
        }

        // 将利润百分比转换为小数，例如 7 -> 0.07，20 -> 0.2
        // BigDecimal decimalUnitPrice = basicFreight.getProfitMargin().divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP); // 四舍五入

        // **计算总价**: 单价（每 100lb） ×（按 100lb 计）
        BigDecimal weightIn100Lb = weight.setScale(0, RoundingMode.CEILING); // 向上取整
        // 总费用
        BigDecimal totalCost = weightIn100Lb.multiply(unitPricePer100Lb);
        // 计算利润金额
        //BigDecimal profitMargin = totalCost.multiply(decimalUnitPrice);
        // 与最低运费比较，取较大值
        BigDecimal minFreight = basicFreight.getMin();
        BigDecimal finalCost = totalCost.max(minFreight);
        // 构建计算公式
        String calculationFormula;
        if (totalCost.compareTo(minFreight) < 0) {
            // 如果计算出来的费用低于最低运费，使用最低运费
            calculationFormula = originalWeight + "kg × 2.20462 / 100 ≈ " + weight + " cwt × " + weightIn100Lb +
                    "（单价）= " + totalCost + "，但低于最低运费，最终取最低运费：" + minFreight;
        } else {
            // 正常计算得出的费用
            calculationFormula = originalWeight + "kg × 2.20462 / 100 ≈ " + weight + " cwt × " + weightIn100Lb +
                    "（单价）= " + totalCost;
        }

        // 添加计算公式字段
        result.put("calculationFormula", calculationFormula);
        result.put("totalCost", finalCost);

        return result;
    }

    /**
     * 根据体积计算运费
     */
    private Map<String, Object> calculateVolumeCost(BigDecimal volume, TmsBasicFreightEntity basicFreight) {
        if (volume == null || volume.compareTo(BigDecimal.ZERO) <= 0) {
            volume = BigDecimal.ZERO;
        }

        Map<String, Object> result = new HashMap<>();

        // 将立方米转为cm，按cm的计算公式 长cm*宽cm*高cm/6000
        volume = volume.multiply(BigDecimal.valueOf(1000000));

        // 记录原始cm³体积
        BigDecimal originalVolume = volume;

        // 将volume 的 立方 m³ 单位转为 in 英尺 ft³
        //volume = volume.multiply(BigDecimal.valueOf(35.3147));

        // 计算体积重量：lb:体积 ÷ 172.8 lb（标准换算率）  厘米：体积 ÷ 6000 cm³ （标准换算率）
        BigDecimal volumeWeight = volume.divide(BigDecimal.valueOf(6000), 4, RoundingMode.HALF_UP);

        // 将计算后的体积重(kg)转为cwt
        volumeWeight = volumeWeight.multiply(BigDecimal.valueOf(2.20462)).divide(BigDecimal.valueOf(100));

        // 匹配 CWT（每 100 lb）费率区间
        BigDecimal unitPricePer100Lb;
        if (volumeWeight.compareTo(BigDecimal.valueOf(10000)) >= 0) {
            unitPricePer100Lb = basicFreight.getCwt10000();
        } else if (volumeWeight.compareTo(BigDecimal.valueOf(5000)) >= 0) {
            unitPricePer100Lb = basicFreight.getCwt5000();
        } else if (volumeWeight.compareTo(BigDecimal.valueOf(2000)) >= 0) {
            unitPricePer100Lb = basicFreight.getCwt2000();
        } else if (volumeWeight.compareTo(BigDecimal.valueOf(1000)) >= 0) {
            unitPricePer100Lb = basicFreight.getCwt1000();
        } else {
            unitPricePer100Lb = basicFreight.getLtl();
        }

        // 计算 CWT 数量（向上取整）
        BigDecimal volumeIn100Lb = volumeWeight.setScale(0, RoundingMode.CEILING); // 向上取整

        // 计算总费用
        BigDecimal totalCost = volumeIn100Lb.multiply(unitPricePer100Lb);

        // 计算利润  // 将利润百分比转换为小数，例如 7 -> 0.07，20 -> 0.2
//        BigDecimal decimalProfitMargin = basicFreight.getProfitMargin().divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP);  // 四舍五入
//        BigDecimal profitMargin = totalCost.multiply(decimalProfitMargin);
        // 获取最低运费
        BigDecimal minFreight = basicFreight.getMin();

        // 计算最终运费（取较大值）
        BigDecimal finalCost = totalCost.max(minFreight);

        // 构建计算公式
        String calculationFormula;
        if (totalCost.compareTo(minFreight) < 0) {
            // 计算费用小于最低运费，使用最低运费
            calculationFormula = "(" + originalVolume + " cm³ / 6000 kg ) × 2.20462 / 100 ≈ " + volumeIn100Lb + " cwt × " + unitPricePer100Lb +
                    "（单价）= " + totalCost + "，但低于最低运费，最终取最低运费：" + minFreight;
        } else {
            // 计算费用高于最低运费，使用正常计算结果
            calculationFormula = "(" + originalVolume + " cm³ / 6000 kg ) × 2.20462 / 100 ≈ " + volumeIn100Lb + " cwt × " + unitPricePer100Lb +
                    "（单价）= " + totalCost;
        }

        // 添加计算公式和最终费用
        result.put("calculationFormula", calculationFormula);
        result.put("totalCost", finalCost);
        return result;
    }


    /**
     * 计算包裹的运费
     */
    private R calculatePackageFreight(String origin, String destination, BigDecimal weight) {
        // 查询所有有效的包裹运费模版
        List<TmsBasicFreightPkgEntity> basicFreightPkgList = basicFreightPkgMapper.selectList(
                new LambdaQueryWrapper<TmsBasicFreightPkgEntity>()
                        .eq(TmsBasicFreightPkgEntity::getIsValid, 1)
        );

        if (CollUtil.isEmpty(basicFreightPkgList)) {
            //return R.failed("没有找到有效的基础运费模板");
            return LocalizedR.failed("tms.no.valid.base.shipping.template.found", "");
        }

        // **提前查询所有符合起始地和目的地的运费明细**
        List<TmsBasicFreightPkgDetailEntity> allPkgDetails = basicFreightPkgDetailMapper.selectList(
                new LambdaQueryWrapper<TmsBasicFreightPkgDetailEntity>()
                        .in(TmsBasicFreightPkgDetailEntity::getPkgFreId,
                                basicFreightPkgList.stream().map(TmsBasicFreightPkgEntity::getPkgFreId).collect(Collectors.toList()))
                        .eq(TmsBasicFreightPkgDetailEntity::getRegion, destination)
        );

        // **匹配符合重量范围的运费**
        return allPkgDetails.stream()
                .filter(detail -> weight.compareTo(detail.getStartWeight()) >= 0 && weight.compareTo(detail.getEndWeight()) <= 0)
                .findFirst()
                .map(detail -> {
                    // 计算总费用：基础运费
                    BigDecimal totalCost = detail.getPkgCost();

                    // 计算公式字符串
                    String calculationFormula = "重量: " + weight + " kg, 适用范围: " + detail.getStartWeight() + " - " + detail.getEndWeight() +
                            " kg, 计算费用: " + totalCost;

                    // 构造返回结果
                    Map<String, Object> result = new HashMap<>();
                    result.put("totalCost", totalCost);
                    result.put("calculationFormula", calculationFormula);
                    return R.ok(result);
                })
                .orElse(R.failed("No matching package shipping rules were found"));
    }

    /**
     * 订单调度-分配承运商(多个单号批量分配给指定的一个承运商)
     *
     * @param carrierId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R assignCarrier(String customerOrderNumber, Long carrierId) {
        try {
            // 1. 参数校验
            if (StrUtil.isBlank(customerOrderNumber)) {
                return LocalizedR.failed("tms.customer.order.number.cannot.be.empty", "");
            }
            if (carrierId == null || carrierId <= 0) {
                return LocalizedR.failed("tms.carrier.ID.is.invalid", "");
            }

            log.info("开始分配承运商[ID:{}]", carrierId);
            // 2. 查询承运商
            TmsCarrierEntity carrier = carrierMapper.selectOne(
                    new LambdaQueryWrapper<TmsCarrierEntity>()
                            .eq(TmsCarrierEntity::getCarrierId, carrierId)
            );
            if (carrier == null || carrier.getIsValid() == 0) {
                return LocalizedR.failed("tms.carrier.is.invalid", "");
            }

            // 3. 解析逗号分隔的客户单号
            List<String> customerOrderNumbers = StrUtil.split(customerOrderNumber, ',');
            if (CollUtil.isEmpty(customerOrderNumbers)) {
                return LocalizedR.failed("tms.customer.order.number.is.invalid", "");
            }

            // 4. 查询待指派和已驳回的客户订单
            List<TmsCustomerOrderEntity> orderList = customerOrderMapper.selectList(
                    new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                            .in(TmsCustomerOrderEntity::getCustomerOrderNumber, customerOrderNumbers)
                            .in(TmsCustomerOrderEntity::getOrderStatus, CustomerOrderStatus.UNASSIGNED.getCode(), CustomerOrderStatus.REJECTED.getCode())
            );
            if (CollUtil.isEmpty(orderList)) {
                return LocalizedR.failed("tms.current.order.status.is.not.operable", "");
            }

            // 5. 判断订单是否在承运商服务区域内
            for (TmsCustomerOrderEntity order : orderList) {
                TmsIsCarrierAreaVo vo = new TmsIsCarrierAreaVo();
                vo.setCarrierId(carrierId);
                // 根据地址判断区域
//                vo.setDestAddress(order.getDestAddress()); // 设置目的地地址
//                if (!isCarrierArea(vo)) {
//                    return R.failed("订单 [" + order.getCustomerOrderNumber() + "] 不在承运商服务区域内");
//                }

                // 根据三字邮编判断区域
                vo.setShipperPostalCode(order.getShipperPostalCode());
                if (!isPostCarrierArea(vo)) {
                    return LocalizedR.failed("CustomerOrderNumber [" + order.getCustomerOrderNumber() + "] Not within the carrier's service area.");
                }
            }

            // 6. 批量更新订单状态
            List<Long> orderIds = orderList.stream().map(TmsCustomerOrderEntity::getId).collect(Collectors.toList());
            int batchSize = 1000; // 每批处理1000条
            for (List<Long> batchOrderIds : Lists.partition(orderIds, batchSize)) {
                customerOrderMapper.update(
                        null, // 不更新实体，仅更新字段
                        new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                                .set(TmsCustomerOrderEntity::getCarrierId, carrierId)
                                .set(TmsCustomerOrderEntity::getOrderStatus, CustomerOrderStatus.ASSIGNED.getCode()) // 已指派
                                .set(TmsCustomerOrderEntity::getUpdateTime, LocalDateTime.now())
                                .set(TmsCustomerOrderEntity::getUpdateBy, SecurityUtils.getUser().getUsername())    // 更新人
                                .in(TmsCustomerOrderEntity::getId, batchOrderIds)
                );
                // 批量保存轨迹
                orderList.forEach(order -> {
                    orderTrackService.saveTrack(order.getEntrustedOrderNumber(), order.getCustomerOrderNumber(), CustomerOrderStatus.ASSIGNED.getValue(), "", "调度分配承运商", "", 1);
                });
            }

            // 7.生成审核记录
            List<TmsCarrierAuditRecordsEntity> auditRecords = createAuditRecords(orderList, carrier);
            carrierAuditRecordsService.saveBatch(auditRecords);

            // 8.生成委托订单（无论承运商是否为自营）
            for (TmsCustomerOrderEntity order : orderList) {
                if (Objects.equals(order.getOrderStatus(), CustomerOrderStatus.UNASSIGNED.getCode())) {
                    // 8.1 查询是否已有委托订单且是转单状态
                    TmsEntrustedOrderEntity entrustedOrder = entrustedOrderMapper.selectOne(new LambdaQueryWrapper<TmsEntrustedOrderEntity>()
                            .eq(TmsEntrustedOrderEntity::getEntrustedOrderNumber, order.getEntrustedOrderNumber()));
                    if (entrustedOrder != null && entrustedOrder.getTransferStatus() == 1) {
                        // 已转单的订单，跳过生成委托订单逻辑，且直接审批通过
                        entrustedOrder.setAuditStatus(AuditStatusConstant.AUDIT_STATUS_PASS);
                        entrustedOrder.setOrderStatus(EntrustedOrderStatus.PENDING_ALLOCATION.getCode());
                        entrustedOrder.setUpdateTime(LocalDateTime.now());
                        entrustedOrder.setUpdateBy(SecurityUtils.getUser().getUsername());
                        entrustedOrderMapper.updateById(entrustedOrder);
                        log.info("订单[{}]为已转单状态，跳过生成委托订单", order.getCustomerOrderNumber());
                        continue;
                    }

                    // 非转单状态的订单，正常生成委托订单
                    createEntrustedOrder(order, carrier);
                }
                // 9. 驳回的状态，二次调度时，需要修改委托单的订单状态为待分配，然后重新承运商审核
                if (Objects.equals(order.getOrderStatus(), CustomerOrderStatus.REJECTED.getCode())) {
                    // 查询所有主单及其子单
                    entrustedOrderMapper.update(new LambdaUpdateWrapper<TmsEntrustedOrderEntity>()
                            .likeRight(TmsEntrustedOrderEntity::getEntrustedOrderNumber, order.getEntrustedOrderNumber()) // 同时匹配主单和子单
                            .set(TmsEntrustedOrderEntity::getOrderStatus, EntrustedOrderStatus.PENDING_APPROVAL.getCode())  // 待审批
                            .set(TmsEntrustedOrderEntity::getCarrierId, carrierId)  // 设置承运商ID
                            .set(TmsEntrustedOrderEntity::getAuditStatus, 0)  // 待审核
                            .set(TmsEntrustedOrderEntity::getUpdateTime, LocalDateTime.now())
                            .set(TmsEntrustedOrderEntity::getUpdateBy, SecurityUtils.getUser().getUsername())); // 更新人

                    // 遍历所有订单（主单和子单），分别添加轨迹
                    orderTrackService.saveTrack(order.getEntrustedOrderNumber(), order.getCustomerOrderNumber(),
                            CustomerOrderStatus.REJECTED.getValue(), "", "二次订单调度", "", 1);
                }
            }

            log.info("成功分配{}个订单至{}", orderList.size(), carrier.getCarrierName());
            return LocalizedR.ok("成功分配：" + MapUtil.of("订单数量: ", orderList.size()));
        } catch (Exception e) {
            log.error("分配承运商发生系统异常 carrierId:{}, customerOrderNumber:{}", carrierId, customerOrderNumber, e);
            // 改回订单状态为待指派
            customerOrderMapper.update(
                    null, // 不更新实体，仅更新字段
                    new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                            .set(TmsCustomerOrderEntity::getOrderStatus, CustomerOrderStatus.UNASSIGNED.getCode()) // 待指派
                            .set(TmsCustomerOrderEntity::getUpdateTime, LocalDateTime.now())
                            .set(TmsCustomerOrderEntity::getUpdateBy, SecurityUtils.getUser().getUsername())    // 更新人
                            .in(TmsCustomerOrderEntity::getCustomerOrderNumber, customerOrderNumber)
            );
            return LocalizedR.failed("tms.anomalous.allocation.of.carriers", "");
        }

    }

    /**
     * 批量生成审核记录
     *
     * @param orderList 客户订单列表
     * @param carrier   承运商
     * @return 审核记录列表
     */
    private List<TmsCarrierAuditRecordsEntity> createAuditRecords(List<TmsCustomerOrderEntity> orderList, TmsCarrierEntity carrier) {
        return orderList.stream()
                .map(order -> createAuditRecord(order, carrier))
                .collect(Collectors.toList());
    }

    /**
     * 创建承运商审核记录
     *
     * @param order   客户订单
     * @param carrier 承运商
     * @return 审核记录实体
     * @throws IllegalArgumentException 如果order或carrier为空
     */
    private TmsCarrierAuditRecordsEntity createAuditRecord(TmsCustomerOrderEntity order, TmsCarrierEntity carrier) {
        // 空指针检查
        if (order == null || carrier == null) {
            throw new IllegalArgumentException("order和carrier都不能为空");
        }

        // 创建审核记录实体
        TmsCarrierAuditRecordsEntity record = new TmsCarrierAuditRecordsEntity();
        record.setCustomerOrderNumber(order.getCustomerOrderNumber()); // 客户订单号
        record.setCarrierCode(carrier.getCarrierCode()); // 承运商代码
        record.setCarrierName(carrier.getCarrierName()); // 承运商名称
        record.setCarrierType(carrier.getCarrierType()); // 承运商类型
        record.setName(carrier.getName()); // 承运商联系人
        record.setPhone(carrier.getPhone()); // 承运商电话

        // 设置审核状态：自营承运商自动通过（1），外部承运商待审核（0）
        record.setAuditStatus(carrier.getCarrierType() == 1 ? 1 : 0);

        return record;
    }

    /**
     * 生成委托订单
     *
     * @param order
     */
    private void createEntrustedOrder(TmsCustomerOrderEntity order, TmsCarrierEntity carrier) {
        try {
            log.info("开始生成委托订单，客户订单号：{}", order.getCustomerOrderNumber());

            // 1.查询货物信息
            LambdaQueryWrapper<TmsCargoInfoEntity> cargoWrapper = new LambdaQueryWrapper<>();
            cargoWrapper.eq(TmsCargoInfoEntity::getCustomerOrderNumber, order.getCustomerOrderNumber());
            List<TmsCargoInfoEntity> cargoInfos = cargoInfoService.list(cargoWrapper);

            if (cargoInfos == null || cargoInfos.isEmpty()) {
                throw new RuntimeException("货物信息为空，无法生成委托单");
            }

            // 2.校验所有货物信息的箱号格式
            for (TmsCargoInfoEntity cargoInfo : cargoInfos) {
                if (cargoInfo.getBagNum() == null || !cargoInfo.getBagNum().matches("\\d{3}")) {
                    throw new RuntimeException("箱号格式错误，必须为3位数字");
                }
            }

            // 3.主单号
            String baseEntrustedOrderNo = order.getEntrustedOrderNumber(); // 基础委托单号

            // 查询该客户订单的承运商ID
            LambdaQueryWrapper<TmsCustomerOrderEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TmsCustomerOrderEntity::getCustomerOrderNumber, order.getCustomerOrderNumber());
            TmsCustomerOrderEntity customerOrder = customerOrderMapper.selectOne(wrapper);

            // 生成主单号
            TmsEntrustedOrderEntity mainEntrustedOrder = new TmsEntrustedOrderEntity();
            BeanUtils.copyProperties(order, mainEntrustedOrder);
            mainEntrustedOrder.setId(null);
            mainEntrustedOrder.setEntrustedOrderNumber(baseEntrustedOrderNo); // 主单号
            mainEntrustedOrder.setIsSubOrderNo(false); // 主单

            // 设置主单的公共属性
            setCommonOrderProperties(mainEntrustedOrder, order, customerOrder);

            if (carrier.getCarrierType() == 1) {
                // 自营承运商，默认审核通过
                mainEntrustedOrder.setAuditStatus(AuditStatusConstant.AUDIT_STATUS_PASS);
                mainEntrustedOrder.setOrderStatus(EntrustedOrderStatus.PENDING_ALLOCATION.getCode());
                mainEntrustedOrder.setUpdateTime(LocalDateTime.now());
            }

            // 设置主单的总重量、总体积、总数量
            SummaryResultVo summaryResult = cargoInfoService.calculateSummary(cargoInfos);
            mainEntrustedOrder.setTotalWeight(summaryResult.getTotalWeight());
            mainEntrustedOrder.setTotalVolume(summaryResult.getTotalVolume());
            mainEntrustedOrder.setCargoQuantity(summaryResult.getTotalQuantity());

            // 插入主单
            entrustedOrderMapper.insert(mainEntrustedOrder);
            log.info("成功生成主委托单，委托单号：{}", baseEntrustedOrderNo);

            // 生成子单（不论是否一票多件）
            for (TmsCargoInfoEntity cargoInfo : cargoInfos) {
                // 生成子委托单号
                String subOrderNo = baseEntrustedOrderNo + cargoInfo.getBagNum(); // 主单号 + 箱号

                // 创建子单对象
                TmsEntrustedOrderEntity subEntrustedOrder = new TmsEntrustedOrderEntity();
                BeanUtils.copyProperties(order, subEntrustedOrder);
                subEntrustedOrder.setId(null);
                subEntrustedOrder.setEntrustedOrderNumber(subOrderNo); // 子单号
                subEntrustedOrder.setIsSubOrderNo(true); // 子单

                // 设置子单的公共属性
                setCommonOrderProperties(subEntrustedOrder, order, customerOrder);

                // 7.4 设置子单的总重量、总体积、总数量（单件货物的数据）
                subEntrustedOrder.setTotalWeight(cargoInfo.getWeight());
                subEntrustedOrder.setTotalVolume(cargoInfo.getLength()
                        .multiply(cargoInfo.getWidth())
                        .multiply(cargoInfo.getHeight())
                        .divide(new BigDecimal("1000000"), 6, RoundingMode.HALF_UP));
                subEntrustedOrder.setCargoQuantity(cargoInfo.getCargoQuantity());

                // 插入子单
                entrustedOrderMapper.insert(subEntrustedOrder);
                log.info("成功生成子委托单，委托单号：{}", subOrderNo);

                // 更新货物信息表的委托、客户单号
                cargoInfo.setEntrustedOrderNumber(subOrderNo);
                cargoInfo.setCustomerOrderNumber(order.getCustomerOrderNumber());
                cargoInfoService.updateById(cargoInfo);

                // 更新附加服务表的委托、客户单号
                List<TmsAdditionalServicesEntity> additionalServicesList = additionalServicesService.listByOrderNumber(order.getCustomerOrderNumber(), null);
                if (additionalServicesList != null && !additionalServicesList.isEmpty()) {
                    for (TmsAdditionalServicesEntity additionalService : additionalServicesList) {
                        additionalService.setEntrustedOrderNumber(subOrderNo);
                        additionalService.setCustomerOrderNumber(order.getCustomerOrderNumber());
                        additionalServicesService.updateById(additionalService);
                    }
                }

                // 自营的承运商，默认审批通过
                if (carrier.getCarrierType() == 1) {
                    // 修改委托订单的审核状态为：审核通过
                    entrustedOrderMapper.update(new LambdaUpdateWrapper<TmsEntrustedOrderEntity>()
                            .set(TmsEntrustedOrderEntity::getAuditStatus, AuditStatusConstant.AUDIT_STATUS_PASS)
                            .set(TmsEntrustedOrderEntity::getOrderStatus, EntrustedOrderStatus.PENDING_ALLOCATION.getCode())
                            .set(TmsEntrustedOrderEntity::getUpdateTime, LocalDateTime.now())
                            .set(TmsEntrustedOrderEntity::getUpdateBy, SecurityUtils.getUser().getUsername())
                            .eq(TmsEntrustedOrderEntity::getEntrustedOrderNumber, subOrderNo));

                    // 保存轨迹
                    orderTrackService.saveTrack(subOrderNo, order.getCustomerOrderNumber(), EntrustedOrderStatus.PENDING_ALLOCATION.getValue(), "", "承运商接单", "", 1);
                }

                // 更新审核记录表的委托、客户单号
                List<TmsCarrierAuditRecordsEntity> carrierAuditRecordsList = carrierAuditRecordsService.listByOrderNumber(order.getCustomerOrderNumber(), null);
                if (carrierAuditRecordsList != null && !carrierAuditRecordsList.isEmpty()) {
                    for (TmsCarrierAuditRecordsEntity record : carrierAuditRecordsList) {
                        record.setEntrustedOrderNumber(subOrderNo);
                        record.setCustomerOrderNumber(order.getCustomerOrderNumber());
                        record.setCreateTime(LocalDateTime.now());
                        record.setUpdateTime(LocalDateTime.now());
                        carrierAuditRecordsService.updateById(record);
                    }
                }

                log.info("成功生成子委托单 order:{}", subOrderNo);
                // 保存轨迹
                orderTrackService.saveTrack(subOrderNo, order.getCustomerOrderNumber(), EntrustedOrderStatus.PENDING_APPROVAL.getValue(), "", "生成委托订单", "", 1);
            }


            log.info("成功生成主委托单 order:{}", baseEntrustedOrderNo);
        } catch (Exception e) {
            log.error("生成委托单失败，客户订单号：{}", order.getCustomerOrderNumber(), e);
            throw new RuntimeException("生成委托单失败", e);
        }
    }

    // 设置通用的订单属性
    private void setCommonOrderProperties(TmsEntrustedOrderEntity entrustedOrder, TmsCustomerOrderEntity order, TmsCustomerOrderEntity customerOrder) {
        // 设置承运商ID
        entrustedOrder.setCarrierId(customerOrder.getCarrierId());

        // 设置订单状态
        entrustedOrder.setOrderStatus(EntrustedOrderStatus.PENDING_APPROVAL.getCode());

        // 设置发货地址经纬度
        String shipperLatLng = getLatLngByAddress(order.getShipperAddress(), order.getOrigin(), order.getShipperPostalCode());
        entrustedOrder.setShipperLatLng(shipperLatLng);

        // 设置收货地址经纬度
        String destLatLng = getLatLngByAddress(order.getDestAddress(), order.getDestination(), order.getDestPostalCode());
        entrustedOrder.setReceiverLatLng(destLatLng);
    }

    @Override
    public String getLatLngByAddress(String address, String region, String postalCode) {
//        String fullAddress = buildFullAddress(address, region, postalCode);
        //优化六字邮编格式-移去空格转大写再拼接  "L4C 0M1", "L4C0M1"-->"L4C 0M1", "L4C 0M1"
        String tempCode = postalCode.toUpperCase().replaceAll("\\s+", "");
        String finalPostalCode = tempCode.substring(0, 3) + " " + tempCode.substring(3);
        String finalAddress = normalizeAddress(address);
        //构建  详细地址 六字邮编  （根据六字邮编得到区域的中心，再匹配上具体街道 门牌号基本准确），如1590 Giles Pl V5A 3K6
        String fullAddress = finalAddress + " " + finalPostalCode;
        return routePlanService.getLatLngByAddress(fullAddress);
    }



    @Override
    public GeocodeResult getLatLngByAddress(String address, String postalCode) {
//        String fullAddress = buildFullAddress(address, region, postalCode);
        //优化六字邮编格式-移去空格转大写再拼接  "L4C 0M1", "L4C0M1"-->"L4C 0M1", "L4C 0M1"
        String tempCode = postalCode.toUpperCase().replaceAll("\\s+", "");
        String finalPostalCode = tempCode.substring(0, 3) + " " + tempCode.substring(3);
        String finalAddress = normalizeAddress(address);
        //构建  详细地址 六字邮编  （根据六字邮编得到区域的中心，再匹配上具体街道 门牌号基本准确），如1590 Giles Pl V5A 3K6
        String fullAddress = finalAddress + " " + finalPostalCode;
        return routePlanService.getLatLngByAddress2(fullAddress);
    }


    /**
     * 格式化输入的地址
     *
     * @param address
     * @return
     */
    public static String normalizeAddress(String address) {
        if (address == null || address.trim().isEmpty()) {
            return address;
        }

        // 去除首尾空格
        address = address.trim();

        // 替换破折号单元号写法：如 5501-1151 => Unit 5501, 1151
        address = address.replaceAll("(?i)^(\\d{3,5})-(\\d{3,5})\\s", "Unit $1, $2 ");

        // 将街道号与街道名粘连的分开，例如 27Tefley => 27 Tefley
        address = address.replaceAll("(?i)^(\\d{1,5})([A-Za-z])", "$1 $2");

        // 修复无空格的街道号和街道名，如 7428Lombard => 7428 Lombard
        address = address.replaceAll("(\\d{3,5})([A-Z][a-z]+)", "$1 $2");

        // 统一多个空格为一个空格
        address = address.replaceAll("\\s+", " ");

        return address.trim();
    }


    /**
     * 构建完整地址
     */
    private String buildFullAddress(String address, String location, String postalCode) {
        if (location.contains("Canada")) {
            String[] parts = location.split("/");
            String country = parts[0];
            String state = parts[1];
            String city = parts[2];
            postalCode = postalCode.replace(" ", "");
            return address + ", " + city + ", " + state + " " + postalCode + ", " + country;
        } else {
            return address;
        }
    }

    /**
     * 生成客户单号-列表不作展示
     * 格式：KH + 年月日 + Snowflake ID
     *
     * @return
     */
    private synchronized String generateCustomerOrderNumber() {
        String datePart = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd"));    // 250330,250331
        long snowflakeId = snowflakeIdGenerator.nextId();
        String snowflakeIdStr = String.valueOf(snowflakeId);
        String shortId = snowflakeIdStr.substring(snowflakeIdStr.length() - 6); // 取后 6 位
        return "KH" + datePart + shortId;
    }

    /**
     * 取消下单
     *
     * @param id
     * @return
     */
    @Override
    public Boolean cancelOrder(Long id) {
        //变更客户订单状态为已取消
        TmsCustomerOrderEntity order = customerOrderMapper.selectById(id);
        order.setOrderStatus(CustomerOrderStatus.CANCELLED.getCode());
        customerOrderMapper.updateById(order);
        //保存轨迹
        orderTrackService.saveTrack(order.getEntrustedOrderNumber(), order.getCustomerOrderNumber(), CustomerOrderStatus.CANCELLED.getValue(), "", "取消下单", "", 1);
        return true;
    }

    /**
     * 修改客户订单
     *
     * @param tmsCustomerOrder
     * @return
     */
    @Override
    public R updateCustomerOrder(TmsCustomerOrderEntity tmsCustomerOrder) {

        // 1. 更新货物信息
        if (tmsCustomerOrder.getCargoInfoEntityList() != null && !tmsCustomerOrder.getCargoInfoEntityList().isEmpty()) {
            log.info("开始保存货物信息");

            // 先删除原有的货物信息
            cargoInfoService.remove(new LambdaQueryWrapper<TmsCargoInfoEntity>()
                    .eq(TmsCargoInfoEntity::getCustomerOrderNumber, tmsCustomerOrder.getCustomerOrderNumber()));

            int i = 0; // 初始化计数器
            for (TmsCargoInfoEntity cargoInfo : tmsCustomerOrder.getCargoInfoEntityList()) {
                // 箱号-系统自动生成-由001依次递增，第一箱货就是001
                cargoInfo.setBagNum(String.format("%03d", i + 1));
                cargoInfo.setCustomerOrderNumber(tmsCustomerOrder.getCustomerOrderNumber());
                cargoInfo.setEntrustedOrderNumber(tmsCustomerOrder.getEntrustedOrderNumber());

                // 业务模式是2-中大件时，每个包裹或每个箱子的重量不超过30kg
                if (tmsCustomerOrder.getBusinessModel() == 2) {
                    if (tmsCustomerOrder.getOrderType() == 1) {
                        if (cargoInfo.getBoxMaxWeight().compareTo(BigDecimal.valueOf(30)) > 0) {
                            return LocalizedR.failed("tms.box.weight.exceeded", "");
                        }
                    }
                    if (tmsCustomerOrder.getOrderType() == 2) {
                        if (cargoInfo.getPkgMaxWeight().compareTo(BigDecimal.valueOf(30)) > 0) {
                            return LocalizedR.failed("tms.package.weight.exceeded", "");
                        }
                    }
                }
                i++;
            }
            cargoInfoService.saveBatch(tmsCustomerOrder.getCargoInfoEntityList());
        }
        // 2. 更新附加服务信息
        if (tmsCustomerOrder.getAdditionalServicesEntityList() != null && !tmsCustomerOrder.getAdditionalServicesEntityList().isEmpty()) {
            log.info("开始保存附加服务信息");

            // 先删除原有的附加服务信息
            additionalServicesService.remove(new LambdaQueryWrapper<TmsAdditionalServicesEntity>()
                    .eq(TmsAdditionalServicesEntity::getCustomerOrderNumber, tmsCustomerOrder.getCustomerOrderNumber()));

            List<TmsAdditionalServicesEntity> allServices = new ArrayList<>(tmsCustomerOrder.getAdditionalServicesEntityList());
            if (tmsCustomerOrder.getAddressType() == 2 || tmsCustomerOrder.getAddressType() == 3) {
                // 添加尾板提货服务（类型8）
                TmsAdditionalServicesEntity tailgatePickup = new TmsAdditionalServicesEntity();
                BeanUtils.copyProperties(tmsCustomerOrder.getAdditionalServicesEntityList().get(0), tailgatePickup);
                tailgatePickup.setId(null);
                tailgatePickup.setAdditionalServiceType("8");   // 尾板提货服务
                allServices.add(tailgatePickup);

                // 添加尾板卸货服务（类型9）
                TmsAdditionalServicesEntity tailgateDelivery = new TmsAdditionalServicesEntity();
                BeanUtils.copyProperties(tmsCustomerOrder.getAdditionalServicesEntityList().get(0), tailgateDelivery);
                tailgateDelivery.setId(null);
                tailgateDelivery.setAdditionalServiceType("9"); // 尾板卸货服务
                allServices.add(tailgateDelivery);
            }

            additionalServicesService.saveBatch(allServices.stream().peek(service -> service.setCustomerOrderNumber(tmsCustomerOrder.getCustomerOrderNumber()))
                    .collect(Collectors.toList()));
        }

        boolean flag = this.updateById(tmsCustomerOrder);
        if (!flag) {
            return LocalizedR.failed("Order update failed!");
        }
        return LocalizedR.ok("Order update success!");
    }


    /**
     * 修改中大件客户订单
     *
     * @param tmsCustomerOrder
     * @return
     */
    @Transactional
    @Override
    public R updateCustomerOrderZdj(TmsCustomerOrderEntity tmsCustomerOrder) {
        TmsCustomerOrderEntity order = getById(tmsCustomerOrder.getId());
        //判断订单是否可修改
        Integer orderStatus = order.getOrderStatus();
        if (!orderStatus.equals(NewOrderStatus.AWAITING_PICKUP.getCode()) && !orderStatus.equals(NewOrderStatus.AWAITING_SHIPMENT.getCode())) {
            return LocalizedR.failed("Order.status.is.not.available.for.modification", Optional.ofNullable(null));
        }

        //判断单号是否为空
        if (StringUtils.isBlank(tmsCustomerOrder.getEntrustedOrderNumber()) | StringUtils.isBlank(tmsCustomerOrder.getCustomerOrderNumber())) {
            //通过ID获取委托单号
            order = getById(tmsCustomerOrder.getId());
            if (order != null) {
                tmsCustomerOrder.setEntrustedOrderNumber(order.getEntrustedOrderNumber());
                tmsCustomerOrder.setCustomerOrderNumber(order.getCustomerOrderNumber());
            }
        }
        //获取订单经纬度
        try {
            String shipperLatLng = getLatLngByAddress(tmsCustomerOrder.getShipperAddress(), tmsCustomerOrder.getOrigin(), tmsCustomerOrder.getShipperPostalCode());
            String destLatLng = getLatLngByAddress(tmsCustomerOrder.getDestAddress(), tmsCustomerOrder.getDestination(), tmsCustomerOrder.getDestPostalCode());
            tmsCustomerOrder.setShipperLatLng(shipperLatLng);
            tmsCustomerOrder.setReceiverLatLng(destLatLng);
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("获取经纬度错误");
        }

        // 1. 更新货物信息
        if (tmsCustomerOrder.getCargoInfoEntityList() != null && !tmsCustomerOrder.getCargoInfoEntityList().isEmpty()) {
            log.info("开始保存货物信息");

            // 先删除原有的货物信息
            cargoInfoService.remove(new LambdaQueryWrapper<TmsCargoInfoEntity>()
                    .eq(TmsCargoInfoEntity::getCustomerOrderNumber, tmsCustomerOrder.getCustomerOrderNumber()));

            int i = 0; // 初始化计数器
            for (TmsCargoInfoEntity cargoInfo : tmsCustomerOrder.getCargoInfoEntityList()) {
                // 箱号-系统自动生成-由001依次递增，第一箱货就是001
                cargoInfo.setBagNum(String.format("%03d", i + 1));
                //主键设置为空让数据重新设置主键id,保证key主键不冲突（因为是先删除再新增的修改操作）
                cargoInfo.setId(null);
                cargoInfo.setCustomerOrderNumber(tmsCustomerOrder.getCustomerOrderNumber());
                cargoInfo.setEntrustedOrderNumber(tmsCustomerOrder.getEntrustedOrderNumber());
                i++;
            }
            cargoInfoService.saveBatch(tmsCustomerOrder.getCargoInfoEntityList());

            // 保存订单总重量、总体积、总数量
            SummaryResultVo result = cargoInfoService.calculateSummary(tmsCustomerOrder.getCargoInfoEntityList());
            tmsCustomerOrder.setTotalWeight(result.getTotalWeight());
            tmsCustomerOrder.setTotalVolume(result.getTotalVolume());
            tmsCustomerOrder.setCargoQuantity(result.getTotalQuantity());
        }
        int receiveType = tmsCustomerOrder.getReceiveType();
        tmsCustomerOrder.setOrderStatus(receiveType == 1 ? NewOrderStatus.AWAITING_PICKUP.getCode() : NewOrderStatus.AWAITING_SHIPMENT.getCode());


        //删除原有子单号信息
        deleteSubOrder(tmsCustomerOrder.getCustomerOrderNumber());

        try {
            this.updateById(tmsCustomerOrder);
            createSubOrderNumber(tmsCustomerOrder);
        } catch (Exception e) {
            return LocalizedR.failed("Order update failed!");
        }
        return LocalizedR.ok("Order update success!");

    }

    public void deleteSubOrder(String customerOrderNumber) {
        LambdaQueryWrapper<TmsCustomerOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TmsCustomerOrderEntity::getCustomerOrderNumber, customerOrderNumber)
                .eq(TmsCustomerOrderEntity::getSubFlag, true);
        List<TmsCustomerOrderEntity> list = this.list(wrapper);
        for (int i = 0; i < list.size(); i++) {
            TmsCustomerOrderEntity tmsCustomerOrderEntity = list.get(i);
            tmsThirdPartPostService.removeBySubOrderNumber(tmsCustomerOrderEntity.getEntrustedOrderNumber());
            this.removeById(tmsCustomerOrderEntity.getId());
        }
        //删除相关轨迹信息
        orderTrackService.deleteByCustomerOrderNo(customerOrderNumber);
    }


    //更新中大件客户订单(备用)
    @Override
    public R updateZdjCustomerOrder(TmsCustomerOrderEntity tmsCustomerOrder) {
        //更新主子单号信息
        LambdaUpdateWrapper<TmsCustomerOrderEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(TmsCustomerOrderEntity::getCustomerOrderNumber, tmsCustomerOrder.getCustomerOrderNumber());
        this.update(tmsCustomerOrder, wrapper);

        //更新货物信息
        List<TmsCargoInfoEntity> cargoInfoEntityList = tmsCustomerOrder.getCargoInfoEntityList();
        for (int i = 0; i < cargoInfoEntityList.size(); i++) {
            TmsCargoInfoEntity tmsCargoInfoEntity = cargoInfoEntityList.get(i);
            cargoInfoService.updateById(tmsCargoInfoEntity);
        }
        return R.ok();
    }


    // 客户端查询订单列表
    @Override
    public Page<TmsCustomerOrderPageVo> clientList(Page page, TmsCustomerOrderPageVo vo) {
        String username = SecurityUtils.getUser().getUsername();
        // 根据当前用户名获取客户信息id
        TmsCustomerEntity customer = customerMapper.selectOne(new LambdaQueryWrapper<TmsCustomerEntity>()
                .eq(TmsCustomerEntity::getIsValid, 1).eq(TmsCustomerEntity::getCustomerName, username), false);
        Long customerId = 0l;
        // 设置客户只能查看自己的订单信息
        if (customer != null) {
            customerId = customer.getId();
            vo.setCustomerId(customerId);
        }
        MPJLambdaWrapper clientListWrapper = getClientListWrapper(vo, null);
        return customerOrderMapper.selectJoinPage(page, TmsCustomerOrderPageVo.class, clientListWrapper);

    }

    // 客户端查询轨迹列表-- 废
    @Override
    public R getTrack(String orderNo) {
        List<String> orderNoList = Arrays.stream(orderNo.trim().split(","))
                .map(String::trim)
                .distinct()
                .collect(Collectors.toList());

        // 查询客户订单表，获取委托主单号
        List<TmsCustomerOrderEntity> customerOrders = customerOrderMapper.selectList(
                new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                        .in(TmsCustomerOrderEntity::getCustomerOrderNumber, orderNoList)
        );

        // 解析出委托单号
        List<String> entrustedOrderNumbers = customerOrders.stream()
                .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                .distinct()
                .collect(Collectors.toList());

        // 如果没有查到对应的委托单号，则直接用输入的 orderNoList 作为委托单号进行查询
        if (CollUtil.isEmpty(entrustedOrderNumbers)) {
            entrustedOrderNumbers.addAll(orderNoList);
        }

        // 查询委托订单表
        List<TmsEntrustedOrderEntity> entrustedOrders = entrustedOrderMapper.selectList(
                new LambdaQueryWrapper<TmsEntrustedOrderEntity>()
                        .in(TmsEntrustedOrderEntity::getEntrustedOrderNumber, entrustedOrderNumbers)
        );

        if (CollUtil.isEmpty(entrustedOrders)) {
            return R.failed("未查询到订单信息");
        }

        // 解析出主单号
        List<String> mainOrderNumbers = entrustedOrders.stream()
                .map(TmsEntrustedOrderEntity::getEntrustedOrderNumber)
                .distinct()
                .collect(Collectors.toList());

        LambdaQueryWrapper<TmsEntrustedOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TmsEntrustedOrderEntity::getIsSubOrderNo, 1);
        queryWrapper.and(wrapper -> {
            for (String mainOrderNo : mainOrderNumbers) {
                wrapper.or().likeRight(TmsEntrustedOrderEntity::getEntrustedOrderNumber, mainOrderNo);
            }
        });

        // 查询子单信息
        List<TmsEntrustedOrderEntity> subOrders = entrustedOrderMapper.selectList(queryWrapper);

        // 按主单号分组
        Map<String, List<TmsEntrustedOrderEntity>> result = subOrders.stream()
                .collect(Collectors.groupingBy(order -> order.getEntrustedOrderNumber().substring(0, mainOrderNumbers.get(0).length())));

        return R.ok(result);
    }

    // 根据箱号查询对应轨迹节点记录  --废
    @Override
    public R getTrackList(String subOrderNo) {
        if (subOrderNo.trim().length() < 14) {
            return R.ok();
        }

        LambdaQueryWrapper<TmsOrderTrackEntity> wrapper = Wrappers.lambdaQuery();
        String tempSubOrderNo = (subOrderNo == null) ? "0000" : subOrderNo;
        // 获取主单号（去掉后面 3 位）
        String masterOrderNo = (tempSubOrderNo.length() > 13) ? tempSubOrderNo.substring(0, tempSubOrderNo.length() - 3) : tempSubOrderNo;

        // 查询该箱单号的轨迹 + 额外查询主单号的两条关键轨迹
        wrapper.and(w -> w
                .eq(TmsOrderTrackEntity::getOrderNo, tempSubOrderNo)  // 查询当前箱单号轨迹
                .or()
                .eq(TmsOrderTrackEntity::getOrderNo, masterOrderNo) // 查询主单号轨迹
                .in(TmsOrderTrackEntity::getOrderStatus, Arrays.asList("UNASSIGNED", "ASSIGNED")) // 额外增加主单的这两种轨迹
        );

        List<TmsOrderTrackEntity> trackList = orderTrackService.list(wrapper);
        return R.ok(trackList);
    }


    // 中大件-轨迹查询
    @Override
    public R getZdjTrack(TmsOrderNoTrackVo orderNos) {
        // 解析输入单号，去重去空格
        List<String> orderNoList = orderNos.getOrderNos().stream()
                .map(String::trim)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        //  查询数据库找出匹配订单
        LambdaQueryWrapper<TmsCustomerOrderEntity> trackQueryWrapper = new LambdaQueryWrapper<>();
        trackQueryWrapper.eq(TmsCustomerOrderEntity::getDelFlag, "0")
                .and(wrapper -> wrapper
                        .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNoList)
                        .or()
                        .in(TmsCustomerOrderEntity::getCustomerOrderNumber, orderNoList));
        List<TmsCustomerOrderEntity> trackOrders = customerOrderMapper.selectList(trackQueryWrapper);
        if (CollUtil.isEmpty(trackOrders)) {
            return LocalizedR.failed("tms.No.order.tracking.info", "");
        }

        // 将输入单号统一映射为主单号
        List<String> inputMainOrderNos = orderNoList.stream()
                .map(inputNo -> {
                    for (TmsCustomerOrderEntity order : trackOrders) {
                        if (inputNo.equals(order.getEntrustedOrderNumber()) || inputNo.equals(order.getCustomerOrderNumber())) {
                            if (Boolean.TRUE.equals(order.getSubFlag())) {
                                String subNo = order.getEntrustedOrderNumber();
                                return (subNo != null && subNo.length() >= 13) ? subNo.substring(0, 13) : null;
                            } else {
                                return order.getEntrustedOrderNumber();
                            }
                        }
                    }
                    return null;
                })
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        // 查询正式主单数据
        LambdaQueryWrapper<TmsCustomerOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TmsCustomerOrderEntity::getSubFlag, Boolean.FALSE)
                .eq(TmsCustomerOrderEntity::getDelFlag, "0")
                .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, inputMainOrderNos);
        List<TmsCustomerOrderEntity> customerOrders = customerOrderMapper.selectList(queryWrapper);

        // 查询换单表，建立主单号到换单记录的映射
        LambdaQueryWrapper<TmsThirdPartPostEntity> thirdPartQuery = new LambdaQueryWrapper<>();
        thirdPartQuery.and(wrapper -> {
            for (String mainOrderNo : inputMainOrderNos) {
                wrapper.or().likeRight(TmsThirdPartPostEntity::getNbOrderNo, mainOrderNo);
            }
        });
        List<TmsThirdPartPostEntity> thirdPartOrders = tmsThirdPartPostService.list(thirdPartQuery);

        Map<String, List<TmsThirdPartPostEntity>> mainOrderToThirdPartMap = new HashMap<>();
        for (TmsThirdPartPostEntity thirdPartOrder : thirdPartOrders) {
            String nbSubOrderNo = thirdPartOrder.getNbOrderNo();
            if (StrUtil.isNotBlank(nbSubOrderNo) && nbSubOrderNo.length() > 3) {
                String mainOrderNo = nbSubOrderNo.substring(0, nbSubOrderNo.length() - 3);
                mainOrderToThirdPartMap.computeIfAbsent(mainOrderNo, k -> new ArrayList<>()).add(thirdPartOrder);
            }
        }

        // 分离UNI和NB订单
        List<String> uniOrderNumbers = new ArrayList<>();
        List<TmsCustomerOrderEntity> nbOrderNumbers = new ArrayList<>();
        List<TmsCustomerOrderEntity> mayFallbackToNbOrders = new ArrayList<>();

        for (TmsCustomerOrderEntity order : customerOrders) {
            String mainOrderNo = order.getEntrustedOrderNumber();
            if (mainOrderToThirdPartMap.containsKey(mainOrderNo)) {
                // 存在换单记录，先加入UNI查询集合
                mayFallbackToNbOrders.add(order);
                // 收集UNI订单号
                List<TmsThirdPartPostEntity> relatedThirdPartOrders = mainOrderToThirdPartMap.get(mainOrderNo);
                for (TmsThirdPartPostEntity thirdPart : relatedThirdPartOrders) {
                    if (StrUtil.isNotBlank(thirdPart.getChannelOrderNo())) {
                        uniOrderNumbers.add(thirdPart.getChannelOrderNo());
                    }
                }
            } else {
                // 没有换单记录，直接加入NB集合
                nbOrderNumbers.add(order);
            }
        }

        // 查询UNI轨迹并筛选有效订单
        Map<String, Map<String, Object>> uniTrackResult = new LinkedHashMap<>();
        Set<String> validUniMainOrders = new HashSet<>(); // 记录有效的UNI主单

        if (CollUtil.isNotEmpty(uniOrderNumbers)) {
            uniTrackResult = querySmallPackageTrack(uniOrderNumbers);

            // 筛选出有两条及以上轨迹的主单
            for (Map.Entry<String, Map<String, Object>> entry : uniTrackResult.entrySet()) {
                Map<String, Object> mainOrderInfo = entry.getValue();
                List<Map<String, Object>> subOrders = (List<Map<String, Object>>) mainOrderInfo.get("subOrders");

                if (CollUtil.isNotEmpty(subOrders)) {
                    for (Map<String, Object> subOrder : subOrders) {
                        List<Map<String, Object>> trackDetails = (List<Map<String, Object>>) subOrder.get("trackDetails");
                        if (CollUtil.isNotEmpty(trackDetails) && trackDetails.size() >= 2) {
                            validUniMainOrders.add(entry.getKey());
                            break;
                        }
                    }
                }
            }

            // 将不符合条件的订单加入回退集合
            for (TmsCustomerOrderEntity order : mayFallbackToNbOrders) {
                if (!validUniMainOrders.contains(order.getEntrustedOrderNumber())) {
                    nbOrderNumbers.add(order);
                }
            }
        }

        // 处理NB轨迹
        Map<String, Map<String, Object>> nbFinalResult = new LinkedHashMap<>();
        if (CollUtil.isNotEmpty(nbOrderNumbers)) {
            // 获取全部nbOrderNumbers中所有单号并去重
            Set<String> nbOrderNumbersSet = nbOrderNumbers.stream()
                    .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                    .collect(Collectors.toSet());

            // 签收图片映射
            Map<String, String> mainOrderSignImgMap = nbOrderNumbers.stream()
                    .filter(order -> order.getEntrustedOrderNumber() != null && order.getDeliveryProof() != null)
                    .collect(Collectors.toMap(
                            TmsCustomerOrderEntity::getEntrustedOrderNumber,
                            TmsCustomerOrderEntity::getDeliveryProof,
                            (v1, v2) -> v1
                    ));

            // 查询所有子单
            LambdaQueryWrapper<TmsCustomerOrderEntity> subQueryWrapper = new LambdaQueryWrapper<>();
            subQueryWrapper.eq(TmsCustomerOrderEntity::getSubFlag, true);
            subQueryWrapper.and(wrapper -> {
                for (String mainOrderNo : nbOrderNumbersSet) {
                    wrapper.or().likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainOrderNo);
                }
            });
            List<TmsCustomerOrderEntity> subOrders = customerOrderMapper.selectList(subQueryWrapper);

            // 查询每个子单最新轨迹
            Map<String, TmsOrderTrackEntity> latestTracks = new HashMap<>();
            for (TmsCustomerOrderEntity subOrder : subOrders) {
                String subOrderNo = subOrder.getEntrustedOrderNumber();
                TmsOrderTrackEntity latestTrack = orderTrackMapper.selectOne(
                        new LambdaQueryWrapper<TmsOrderTrackEntity>()
                                .eq(TmsOrderTrackEntity::getOrderNo, subOrderNo)
                                .orderByDesc(TmsOrderTrackEntity::getAddTime)
                                .last("LIMIT 1")
                );
                if (latestTrack != null) {
                    latestTracks.put(subOrderNo, latestTrack);
                }
            }

            // 构建NB轨迹结果
            for (String mainOrderNo : nbOrderNumbersSet) {
                List<Map<String, Object>> subOrderList = subOrders.stream()
                        .filter(subOrder -> subOrder.getEntrustedOrderNumber().startsWith(mainOrderNo))
                        .map(subOrder -> {
                            String subOrderNo = subOrder.getEntrustedOrderNumber();
                            TmsOrderTrackEntity track = latestTracks.get(subOrderNo);
                            Map<String, Object> item = new HashMap<>();
                            item.put("mainOrderNo", mainOrderNo);
                            item.put("subOrderNo", subOrderNo);
                            item.put("orderStatus", subOrder.getOrderStatus());
                            item.put("trackDesc", track != null ? track.getLocationDescription() : null);
                            item.put("trackTime", track != null ? track.getAddTime() : null);
                            item.put("trackOperator", track != null ? track.getCreateBy() : null);
                            item.put("signImgUrl", StrUtil.isNotBlank(subOrder.getDeliveryProof()) ? subOrder.getDeliveryProof() : null); // 加入子单签收图
                            return item;
                        })
                        .collect(Collectors.toList());

                Map<String, Object> mainOrderInfo = new HashMap<>();
                mainOrderInfo.put("signImgUrl", mainOrderSignImgMap.get(mainOrderNo));
                mainOrderInfo.put("subOrders", subOrderList);
                nbFinalResult.put(mainOrderNo, mainOrderInfo);
            }
        }

        // 汇总返回
        int totalOrders = customerOrders.size();
        Map<Integer, Long> statusCountMap = customerOrders.stream()
                .collect(Collectors.groupingBy(TmsCustomerOrderEntity::getOrderStatus, Collectors.counting()));

        Map<String, Object> response = new HashMap<>();
        response.put("totalOrders", totalOrders);
        response.put("statusCountMap", statusCountMap);
        // NB轨迹结果（包含纯NB订单和回退的订单）
        response.put("result", nbFinalResult);
        // UNI轨迹结果（仅包含符合条件的UNI订单）
        Map<String, Map<String, Object>> filteredUniResult = new LinkedHashMap<>();
        Map<String, Map<String, Object>> tempUniTrackResult = uniTrackResult;
        validUniMainOrders.forEach(mainOrderNo -> {
            if (tempUniTrackResult.containsKey(mainOrderNo)) {
                filteredUniResult.put(mainOrderNo, tempUniTrackResult.get(mainOrderNo));
            }
        });
        response.put("uniTrackResult", filteredUniResult);
        return R.ok(response);
    }


    // 中大件-根据箱号查询对应轨迹节点记录
    @Override
    public R getZdjTrackList(String subOrderNo) {
        if (subOrderNo.trim().length() < 15) {
            return R.ok();
        }

        LambdaQueryWrapper<TmsOrderTrackEntity> wrapper = Wrappers.lambdaQuery();
        String tempSubOrderNo = (subOrderNo == null) ? "0000" : subOrderNo;

        // 查询该箱单号的轨迹
        wrapper.eq(TmsOrderTrackEntity::getOrderNo, tempSubOrderNo);
        wrapper.in(TmsOrderTrackEntity::getTrackType, Arrays.asList(TrackTypeConstant.EXTERNAL, TrackTypeConstant.INTERIOR));
        wrapper.orderByDesc(TmsOrderTrackEntity::getAddTime);

        List<TmsOrderTrackEntity> trackList = orderTrackService.list(wrapper);

        if (CollUtil.isEmpty(trackList)) {
            return R.failed("未查询到该订单轨迹");
        }
        return R.ok(trackList);
    }

    // 订单详情箱号查询轨迹
    public List<TmsOrderTrackVo> getOrderTrackList(String orderNo) {
        // 存储本地查询单号
        String originOrderNo = orderNo;

        // 先去换单表查是否存在（只看是否推送UNI）
        TmsThirdPartPostEntity thirdPartOrder = tmsThirdPartPostService.getOne(
                new LambdaQueryWrapper<TmsThirdPartPostEntity>()
                        .eq(TmsThirdPartPostEntity::getNbOrderNo, originOrderNo)
                        .last("LIMIT 1")
        );

        // 在换单表里找到，说明是推送了UNI的
        if (thirdPartOrder != null && StrUtil.isNotBlank(thirdPartOrder.getChannelOrderNo())) {
            String uniOrderNo = thirdPartOrder.getChannelOrderNo();

            // 查询小包轨迹（注意这里只查单个UNI单号）
            List<String> uniOrderList = Collections.singletonList(uniOrderNo);
            Map<String, Map<String, Object>> trackResultMap = querySmallPackageTrack(uniOrderList);

            // 由于 querySmallPackageTrack 返回以主单为 key（主单号 = 子单号去掉后三位）
            String mainOrderNo = originOrderNo.substring(0, originOrderNo.length() - 3);
            Map<String, Object> trackData = trackResultMap.get(mainOrderNo);

            if (trackData != null) {
                // 从 subOrders 里找到本次子单的轨迹信息
                List<Map<String, Object>> subOrders = (List<Map<String, Object>>) trackData.get("subOrders");
                Map<String, Object> subOrderItem = subOrders.stream()
                        .filter(item -> originOrderNo.equals(String.valueOf(item.get("subOrderNo"))))
                        .findFirst()
                        .orElse(null);

                if (subOrderItem != null) {
                    // 获取轨迹明细并检查数量
                    List<Map<String, Object>> trackDetails = (List<Map<String, Object>>) subOrderItem.get("trackDetails");
                    if (CollUtil.isNotEmpty(trackDetails) && trackDetails.size() >= 2) {
                        // 转换并返回UNI轨迹
                        List<TmsOrderTrackVo> result = new ArrayList<>();
                        for (Map<String, Object> detail : trackDetails) {
                            TmsOrderTrackVo detailVo = new TmsOrderTrackVo();
                            detailVo.setOrderNo(originOrderNo);
                            detailVo.setLocationDescription(String.valueOf(detail.get("pathInfo")));
                            detailVo.setTrackType(TrackTypeConstant.EXTERNAL);
                            detailVo.setAddTime(LocalDateTime.parse(String.valueOf(detail.get("pathTime")),
                                    DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                            result.add(detailVo);
                        }
                        // 按时间降序排序
                        result.sort(Comparator.comparing(TmsOrderTrackVo::getAddTime).reversed());
                        return result;
                    }
                }
            }
            // 如果UNI轨迹不符合条件，继续查询NB轨迹
        }

        // 查询本地 NB 系统轨迹
        List<TmsOrderTrackEntity> localTracks = orderTrackMapper.selectList(
                new LambdaQueryWrapper<TmsOrderTrackEntity>()
                        .eq(TmsOrderTrackEntity::getOrderNo, originOrderNo)
                        .in(TmsOrderTrackEntity::getTrackType,
                                Arrays.asList(TrackTypeConstant.EXTERNAL, TrackTypeConstant.INTERIOR))
                        .orderByDesc(TmsOrderTrackEntity::getAddTime)
        );

        return localTracks.stream().map(track -> {
            TmsOrderTrackVo vo = new TmsOrderTrackVo();
            BeanUtils.copyProperties(track, vo);
            return vo;
        }).collect(Collectors.toList());
    }


    // 客户端订单详情箱号查询轨迹
    @Override
    public List<TmsOrderTrackVo> getClientBoxTrack(String orderNo) {
        //客户只能查询自己的订单
        TmsCustomerEntity clientCustomer = tmsCustomerService.getOne(new LambdaQueryWrapper<TmsCustomerEntity>()
                .eq(TmsCustomerEntity::getUserId, SecurityUtils.getUser().getId()));
        if (ObjectUtil.isNull(clientCustomer)) {
            return null;
        }

        // 存储本地查询单号
        String originOrderNo = orderNo;
        if (orderNo.length() > 14) {
            // 如果是子单，截取orderNo后三位
            orderNo = orderNo.substring(0, orderNo.length() - 3);
        }
        // 查询订单信息，判断是否是推送UNI订单
        TmsCustomerOrderEntity order = customerOrderMapper.selectOne(
                new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                        .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNo)
                        .eq(TmsCustomerOrderEntity::getCustomerId, clientCustomer.getId())
                        .last("LIMIT 1")
        );
        if (order == null) {
            return Collections.emptyList();
        }

        TmsCustomerEntity customer = customerMapper.selectById(order.getCustomerId());
        String isPush = customer != null ? customer.getIsPush() : null;

        // 是UNI推送订单 → 查小包轨迹
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(isPush) && isPush.contains("1")) {
            List<String> uniOrderList = Collections.singletonList(order.getPushOrder());
            Map<String, Map<String, Object>> trackResultMap = querySmallPackageTrack(uniOrderList);
            Map<String, Object> trackData = trackResultMap.get(orderNo);
            if (trackData == null) return Collections.emptyList();

            // 把 Map 数据转为 TmsOrderTrackVo
            List<Map<String, Object>> trackList = (List<Map<String, Object>>) trackData.get("subOrders");
            List<TmsOrderTrackVo> result = new ArrayList<>();
            for (Map<String, Object> item : trackList) {
                TmsOrderTrackVo vo = new TmsOrderTrackVo();
                vo.setOrderNo(String.valueOf(item.get("subOrderNo")));
                vo.setLocationDescription(String.valueOf(item.get("trackDesc")));
                vo.setTrackType(TrackTypeConstant.EXTERNAL);
                vo.setAddTime(LocalDateTime.parse(String.valueOf(item.get("trackTime")), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                result.add(vo);
            }
            return result;
        }

        // 否则：查本地轨迹
        List<TmsOrderTrackEntity> localTracks = orderTrackMapper.selectList(
                new LambdaQueryWrapper<TmsOrderTrackEntity>()
                        .eq(TmsOrderTrackEntity::getOrderNo, originOrderNo)
                        .in(TmsOrderTrackEntity::getTrackType, Arrays.asList(TrackTypeConstant.EXTERNAL, TrackTypeConstant.INTERIOR))
                        .orderByDesc(TmsOrderTrackEntity::getAddTime)
        );
        return localTracks.stream().map(track -> {
            TmsOrderTrackVo vo = new TmsOrderTrackVo();
            BeanUtils.copyProperties(track, vo);
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 查询小包轨迹封装方法
     *
     * @param orderNumbers 小包订单号列表
     * @return Map<运单号, 对应轨迹信息>
     */
    public Map<String, Map<String, Object>> querySmallPackageTrack(List<String> orderNumbers) {
        Map<String, Map<String, Object>> result = new LinkedHashMap<>();
        try {
//            String url = "https://api.otms.ltianexp.com/v1/api/tracking/query/trackInfo";
            String url = "https://api.jygjexp.com/v1/api/tracking/query/trackInfo";
            HashMap<String, String> headerMap = new HashMap<>();
            headerMap.put("code", "220999");
            String apiKey = "198e900a5a66403b86f6c916d05b43ae";
            headerMap.put("apiKey", apiKey);
            headerMap.put("Content-Type", "application/json");

            // 处理时间戳
            LocalDateTime now = LocalDateTime.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String osName = System.getProperty("os.name").toLowerCase();
            if (!osName.contains("win")) {
                now = now.plusHours(12);
            }
            headerMap.put("timestamp", now.format(formatter));
            headerMap.put("sign", OrderTools.getMD5("220999" + apiKey));

            String jsonBody = JSONUtil.toJsonStr(orderNumbers);
            String responseStr = OkHttpUtil.doPostJson(url, jsonBody, headerMap);

            JSONObject json = JSONUtil.parseObj(responseStr);
            if (json.getInt("code", 0) == 1 && json.containsKey("data")) {
                JSONArray data = json.getJSONArray("data");

                for (Object obj : data) {
                    JSONObject trackObj = (JSONObject) obj;
                    String trackingNo = trackObj.getStr("trackingNo");
                    JSONArray details = trackObj.getJSONArray("fromDetail");

                    if (CollUtil.isEmpty(details)) continue;

                    // 轨迹按时间倒序排序
                    details.sort((o1, o2) -> {
                        String t1 = ((JSONObject) o1).getStr("pathTime");
                        String t2 = ((JSONObject) o2).getStr("pathTime");
                        return t2.compareTo(t1);
                    });

                    // 查询对应的 NB 系统子单
                    TmsThirdPartPostEntity thirdPartPost = tmsThirdPartPostService.getOne(
                            new LambdaQueryWrapper<TmsThirdPartPostEntity>()
                                    .eq(TmsThirdPartPostEntity::getChannelOrderNo, trackingNo)
                                    .last("limit 1"));

                    if (thirdPartPost == null) continue; // 安全防护

                    String nbSubOrderNo = thirdPartPost.getNbOrderNo();
                    String mainOrderNo = nbSubOrderNo.substring(0, nbSubOrderNo.length() - 3);

                    // 子单基础信息
                    Map<String, Object> subOrderItem = new HashMap<>();
                    subOrderItem.put("mainOrderNo", mainOrderNo);
                    subOrderItem.put("subOrderNo", nbSubOrderNo);


                    // 获取pods图片,并将url上传至我们的oss
                    JSONArray podsArray = trackObj.getJSONArray("pods");
                    List<String> ossUrls = new ArrayList<>();

                    if (null != podsArray && !podsArray.isEmpty()) {
                        for (Object pod : podsArray) {
                            if (pod instanceof String) {
                                String remoteUrl = (String) pod;
                                try {
                                    String ossUrl = AliYunOSS.sendToOss(remoteUrl);
                                    if (ossUrl != null) {
                                        ossUrls.add(ossUrl);
                                    }
                                } catch (Exception e) {
                                    log.error("上传图片到OSS失败，url: {}", remoteUrl, e);
                                }
                            }
                        }
                    }
                    // 将OSS返回的URL列表放入 signImgUrl 字段
                    subOrderItem.put("signImgUrl", ossUrls.isEmpty() ? null : String.join(",", ossUrls));

                    // 最新轨迹信息
                    JSONObject latest = details.getJSONObject(0);
                    subOrderItem.put("orderStatus", latest.getStr("pathCode"));
                    subOrderItem.put("trackDesc", latest.getStr("pathInfo"));
                    subOrderItem.put("trackTime", latest.getStr("pathTime"));
                    subOrderItem.put("trackOperator", latest.getStr("pathLocation"));

                    // 完整轨迹明细列表
                    List<Map<String, Object>> trackDetailList = new ArrayList<>();
                    for (Object detailObj : details) {
                        JSONObject detail = (JSONObject) detailObj;
                        Map<String, Object> detailItem = new HashMap<>();
                        detailItem.put("pathTime", detail.getStr("pathTime"));
                        detailItem.put("pathCode", detail.getStr("pathCode"));
                        detailItem.put("pathInfo", detail.getStr("pathInfo"));
                        detailItem.put("pathLocation", detail.getStr("pathLocation"));
                        detailItem.put("timezone", detail.getStr("timezone"));
                        trackDetailList.add(detailItem);
                    }
                    subOrderItem.put("trackDetails", trackDetailList);

                    if (!result.containsKey(mainOrderNo)) {
                        Map<String, Object> mainOrderInfo = new HashMap<>();
                        mainOrderInfo.put("signImgUrl", null);
                        List<Map<String, Object>> subOrderList = new ArrayList<>();
                        subOrderList.add(subOrderItem);
                        mainOrderInfo.put("subOrders", subOrderList);
                        result.put(mainOrderNo, mainOrderInfo);
                    } else {
                        Map<String, Object> mainOrderInfo = result.get(mainOrderNo);
                        List<Map<String, Object>> subOrderList = (List<Map<String, Object>>) mainOrderInfo.get("subOrders");
                        subOrderList.add(subOrderItem);
                    }
                }
            }
        } catch (Exception e) {
            log.error("小包轨迹查询失败", e);
        }
        return result;
    }

    /**
     * 查询小包轨迹并转换为track方法的返回格式
     *
     * @param subOrderNos 子单号列表
     * @return Map<子单号, 轨迹列表> 格式与track方法一致
     */
    private Map<String, List<TmsApiOrderTaskDto>> querySmallPackageTrackForTrackMethod(List<String> subOrderNos) {
        Map<String, List<TmsApiOrderTaskDto>> result = new LinkedHashMap<>();

        if (CollUtil.isEmpty(subOrderNos)) {
            return result;
        }

        try {
            // 查询换单表，获取对应的小包系统订单号
            List<String> channelOrderNos = new ArrayList<>();
            Map<String, String> channelToNbOrderMap = new HashMap<>(); // 小包订单号 -> NB子单号的映射

            for (String subOrderNo : subOrderNos) {
                TmsThirdPartPostEntity thirdPartPost = tmsThirdPartPostService.getOne(
                        new LambdaQueryWrapper<TmsThirdPartPostEntity>()
                                .eq(TmsThirdPartPostEntity::getNbOrderNo, subOrderNo)
                                .last("limit 1"));

                if (thirdPartPost != null && StrUtil.isNotBlank(thirdPartPost.getChannelOrderNo())) {
                    channelOrderNos.add(thirdPartPost.getChannelOrderNo());
                    channelToNbOrderMap.put(thirdPartPost.getChannelOrderNo(), subOrderNo);
                }
            }

            if (CollUtil.isEmpty(channelOrderNos)) {
                return result;
            }

            // 调用小包系统查询轨迹
            Map<String, Map<String, Object>> smallPackageResult = querySmallPackageTrack(channelOrderNos);

            // 转换数据格式
            for (Map.Entry<String, Map<String, Object>> mainOrderEntry : smallPackageResult.entrySet()) {
                Map<String, Object> mainOrderInfo = mainOrderEntry.getValue();
                List<Map<String, Object>> subOrders = (List<Map<String, Object>>) mainOrderInfo.get("subOrders");

                if (CollUtil.isNotEmpty(subOrders)) {
                    for (Map<String, Object> subOrder : subOrders) {
                        String subOrderNo = (String) subOrder.get("subOrderNo");
                        List<Map<String, Object>> trackDetails = (List<Map<String, Object>>) subOrder.get("trackDetails");

                        if (StrUtil.isNotBlank(subOrderNo) && CollUtil.isNotEmpty(trackDetails)) {
                            List<TmsApiOrderTaskDto> trackDtoList = new ArrayList<>();

                            for (Map<String, Object> trackDetail : trackDetails) {
                                TmsApiOrderTaskDto dto = new TmsApiOrderTaskDto();
                                dto.setOrderNo(subOrderNo);
                                dto.setOrderStatus("");
                                dto.setLocationDescription((String) trackDetail.get("pathInfo"));
                                dto.setCity((String) trackDetail.get("pathLocation"));
                                dto.setTimeZone((String) trackDetail.get("timezone"));

                                // 解析时间
                                String pathTime = (String) trackDetail.get("pathTime");
                                if (StrUtil.isNotBlank(pathTime)) {
                                    try {
                                        LocalDateTime addTime = LocalDateTime.parse(pathTime,
                                                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                                        dto.setAddTime(addTime);
                                    } catch (Exception e) {
                                        log.warn("解析轨迹时间失败: {}", pathTime, e);
                                    }
                                }

                                // 解析状态码
                                Object pathCodeObj = trackDetail.get("pathCode");
                                if (pathCodeObj != null) {
                                    try {
                                        dto.setPathCode(Integer.valueOf(pathCodeObj.toString()));
                                    } catch (NumberFormatException e) {
                                        log.warn("解析状态码失败: {}", pathCodeObj, e);
                                    }
                                }

                                trackDtoList.add(dto);
                            }

                            result.put(subOrderNo, trackDtoList);
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("查询小包轨迹并转换格式失败", e);
        }

        return result;
    }


    /**
     * 官网中大件-轨迹查询新
     *
     * @param vo
     * @return
     */
    @Override
    public R getZdjWebTrackNew(TmsWebOrderTrackVo vo) {
        //  获取输入的订单列表和邮编
        List<String> orderNoList = vo.getOrderList();
        String zipInput = vo.getZip();
        List<String> zipList = Arrays.stream(StrUtil.nullToEmpty(zipInput).split(","))
                .map(String::trim)
                .filter(StrUtil::isNotBlank)
                .map(String::toUpperCase)
                .collect(Collectors.toList());

        // 查询所有匹配的订单（主单和子单都查）
        LambdaQueryWrapper<TmsCustomerOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TmsCustomerOrderEntity::getDelFlag, "0")
                .and(wrapper -> wrapper
                        .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNoList)
                        .or()
                        .in(TmsCustomerOrderEntity::getCustomerOrderNumber, orderNoList));
        List<TmsCustomerOrderEntity> customerOrders = customerOrderMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(customerOrders)) {
            return LocalizedR.failed("tms.No.order.tracking.info", "");
        }

        // 提取主单号
        Set<String> mainOrderNumbers = customerOrders.stream()
                .map(order -> {
                    if (Boolean.TRUE.equals(order.getSubFlag())) {
                        String subNo = order.getEntrustedOrderNumber();
                        return (subNo != null && subNo.length() >= 15) ? subNo.substring(0, 13) : null;
                    } else {
                        return order.getEntrustedOrderNumber();
                    }
                })
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());

        // 邮编过滤逻辑
        boolean hasZipMatch = CollUtil.isNotEmpty(zipList) && customerOrders.stream().anyMatch(order -> {
            String destZip = order.getDestPostalCode();
            return StrUtil.isNotBlank(destZip) && zipList.contains(destZip.toUpperCase());
        });

        if (hasZipMatch) {
            mainOrderNumbers = customerOrders.stream()
                    .filter(order -> {
                        String destZip = order.getDestPostalCode();
                        return StrUtil.isNotBlank(destZip) && zipList.contains(destZip.toUpperCase());
                    })
                    .map(order -> {
                        if (Boolean.TRUE.equals(order.getSubFlag())) {
                            String subNo = order.getEntrustedOrderNumber();
                            return (subNo != null && subNo.length() >= 15) ? subNo.substring(0, 13) : null;
                        } else {
                            return order.getEntrustedOrderNumber();
                        }
                    })
                    .filter(StrUtil::isNotBlank)
                    .collect(Collectors.toSet());
        }

        // 查询主单数据
        Set<String> finalMainOrderNos = mainOrderNumbers;
        List<TmsCustomerOrderEntity> mainOrders = customerOrderMapper.selectList(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .eq(TmsCustomerOrderEntity::getDelFlag, "0")
                .eq(TmsCustomerOrderEntity::getSubFlag, false)
                .and(w -> w
                        .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, finalMainOrderNos)
                        .or()
                        .in(TmsCustomerOrderEntity::getCustomerOrderNumber, finalMainOrderNos)));

        // 查询换单表并建立映射
        Set<String> thirdPartPostNos = mainOrders.stream().map(TmsCustomerOrderEntity::getEntrustedOrderNumber).collect(Collectors.toSet());
        LambdaQueryWrapper<TmsThirdPartPostEntity> thirdPartQuery = new LambdaQueryWrapper<>();
        thirdPartQuery.and(wrapper -> {
            for (String mainOrderNo : thirdPartPostNos) {
                wrapper.or().likeRight(TmsThirdPartPostEntity::getNbOrderNo, mainOrderNo);
            }
        });
        List<TmsThirdPartPostEntity> thirdPartOrders = tmsThirdPartPostService.list(thirdPartQuery);

        Map<String, List<TmsThirdPartPostEntity>> mainOrderToThirdPartMap = new HashMap<>();
        for (TmsThirdPartPostEntity thirdPartOrder : thirdPartOrders) {
            String nbSubOrderNo = thirdPartOrder.getNbOrderNo();
            if (StrUtil.isNotBlank(nbSubOrderNo) && nbSubOrderNo.length() > 3) {
                String mainOrderNo = nbSubOrderNo.substring(0, nbSubOrderNo.length() - 3);
                mainOrderToThirdPartMap.computeIfAbsent(mainOrderNo, k -> new ArrayList<>()).add(thirdPartOrder);
            }
        }

        // 分离UNI和NB订单
        List<String> uniOrderNumbers = new ArrayList<>();
        Set<String> nbOrderNumbers = new HashSet<>();
        List<TmsCustomerOrderEntity> mayFallbackToNbOrders = new ArrayList<>();

        for (TmsCustomerOrderEntity order : mainOrders) {
            String mainOrderNo = order.getEntrustedOrderNumber();
            if (mainOrderToThirdPartMap.containsKey(mainOrderNo)) {
                // 存在换单记录，先加入UNI查询集合
                mayFallbackToNbOrders.add(order);
                // 收集UNI订单号
                List<TmsThirdPartPostEntity> relatedThirdPartOrders = mainOrderToThirdPartMap.get(mainOrderNo);
                for (TmsThirdPartPostEntity thirdPart : relatedThirdPartOrders) {
                    if (StrUtil.isNotBlank(thirdPart.getChannelOrderNo())) {
                        uniOrderNumbers.add(thirdPart.getChannelOrderNo());
                    }
                }
            } else {
                // 没有换单记录，直接加入NB集合
                nbOrderNumbers.add(order.getEntrustedOrderNumber());
            }
        }

        // 查询UNI轨迹并筛选有效订单
        Map<String, Map<String, Object>> uniTrackResult = new LinkedHashMap<>();
        Set<String> validUniMainOrders = new HashSet<>(); // 记录有效的UNI主单

        if (CollUtil.isNotEmpty(uniOrderNumbers)) {
            uniTrackResult = querySmallPackageTrack(uniOrderNumbers);

            // 筛选出有两条及以上轨迹的主单
            for (Map.Entry<String, Map<String, Object>> entry : uniTrackResult.entrySet()) {
                Map<String, Object> mainOrderInfo = entry.getValue();
                List<Map<String, Object>> subOrders = (List<Map<String, Object>>) mainOrderInfo.get("subOrders");

                if (CollUtil.isNotEmpty(subOrders)) {
                    for (Map<String, Object> subOrder : subOrders) {
                        List<Map<String, Object>> trackDetails = (List<Map<String, Object>>) subOrder.get("trackDetails");
                        if (CollUtil.isNotEmpty(trackDetails) && trackDetails.size() >= 2) {
                            validUniMainOrders.add(entry.getKey());
                            break;
                        }
                    }
                }
            }

            // 将不符合条件的订单加入回退集合
            for (TmsCustomerOrderEntity order : mayFallbackToNbOrders) {
                if (!validUniMainOrders.contains(order.getEntrustedOrderNumber())) {
                    nbOrderNumbers.add(order.getEntrustedOrderNumber());
                }
            }
        }

        // 处理NB轨迹
        Map<String, Map<String, Object>> nbFinalResult = new LinkedHashMap<>();
        if (CollUtil.isNotEmpty(nbOrderNumbers)) {
            // 从主单中查询签收图片
            LambdaQueryWrapper<TmsCustomerOrderEntity> mainOrderQuery = new LambdaQueryWrapper<>();
            mainOrderQuery.eq(TmsCustomerOrderEntity::getSubFlag, false)
                    .eq(TmsCustomerOrderEntity::getDelFlag, "0")
                    .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, nbOrderNumbers);

            List<TmsCustomerOrderEntity> mainOrderEntities = customerOrderMapper.selectList(mainOrderQuery);

            Map<String, String> mainOrderSignImgMap = mainOrderEntities.stream()
                    .filter(order -> StrUtil.isNotBlank(order.getDeliveryProof()))
                    .collect(Collectors.toMap(
                            TmsCustomerOrderEntity::getEntrustedOrderNumber,
                            TmsCustomerOrderEntity::getDeliveryProof,
                            (v1, v2) -> v1
                    ));

            // 查询所有匹配主单号的子单
            LambdaQueryWrapper<TmsCustomerOrderEntity> subQueryWrapper = new LambdaQueryWrapper<>();
            subQueryWrapper.eq(TmsCustomerOrderEntity::getSubFlag, true);
            Set<String> finalMainOrderNumbers = nbOrderNumbers;
            subQueryWrapper.and(wrapper -> {
                for (String mainOrderNo : finalMainOrderNumbers) {
                    wrapper.or().likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainOrderNo);
                }
            });

            List<TmsCustomerOrderEntity> subOrders = customerOrderMapper.selectList(subQueryWrapper);
            if (CollUtil.isEmpty(subOrders)) {
                return LocalizedR.failed("tms.No.order.tracking.info", "");
            }

            // 提取子单号
            List<String> subOrderNumbers = subOrders.stream().map(TmsCustomerOrderEntity::getEntrustedOrderNumber).distinct().collect(Collectors.toList());

            // 查询所有子单轨迹
            List<TmsOrderTrackEntity> allTracks = orderTrackMapper.selectList(new LambdaQueryWrapper<TmsOrderTrackEntity>()
                    .in(TmsOrderTrackEntity::getOrderNo, subOrderNumbers)
                    .eq(TmsOrderTrackEntity::getDelFlag, "0")
                    .eq(TmsOrderTrackEntity::getTrackType, TrackTypeConstant.EXTERNAL)
                    .orderByDesc(TmsOrderTrackEntity::getAddTime)
            );

            // Map<subOrderNo, List<track>>
            Map<String, List<TmsOrderTrackEntity>> subOrderTrackMap = allTracks.stream()
                    .collect(Collectors.groupingBy(TmsOrderTrackEntity::getOrderNo, LinkedHashMap::new, Collectors.toList()));

            for (String mainOrderNo : nbOrderNumbers) {
                List<Map<String, Object>> subOrderList = subOrders.stream()
                        .filter(subOrder -> subOrder.getEntrustedOrderNumber().startsWith(mainOrderNo))
                        .map(subOrder -> {
                            String subOrderNo = subOrder.getEntrustedOrderNumber();
                            List<TmsOrderTrackEntity> trackList = subOrderTrackMap.getOrDefault(subOrderNo, Collections.emptyList());

                            Map<String, Object> item = new HashMap<>();
                            item.put("mainOrderNo", mainOrderNo);
                            item.put("subOrderNo", subOrderNo);
                            item.put("orderStatus", trackList.get(0).getOrderStatus());
                            item.put("destination", subOrder.getDestination());
                            if (hasZipMatch) {
                                item.put("signImgUrl", StrUtil.isNotBlank(subOrder.getDeliveryProof()) ? subOrder.getDeliveryProof() : null); // 加入子单签收图
                            }
                            item.put("trackList", trackList.stream().map(track -> {
                                Map<String, Object> t = new HashMap<>();
                                t.put("trackDesc", track.getExternalDescription());
                                t.put("trackTime", track.getAddTime());
                                t.put("trackOperator", track.getCreateBy());
                                t.put("status", track.getOrderStatus());
                                t.put("city", track.getCity());
                                t.put("timeZone", track.getTimeZone());
                                t.put("code", track.getStatusCode());
                                return t;
                            }).collect(Collectors.toList()));
                            return item;
                        }).collect(Collectors.toList());

                Map<String, Object> mainOrderInfo = new HashMap<>();
                if (hasZipMatch) {
                    mainOrderInfo.put("signImgUrl", mainOrderSignImgMap.get(mainOrderNo));
                }
                mainOrderInfo.put("subOrders", subOrderList);
                nbFinalResult.put(mainOrderNo, mainOrderInfo);
            }
        }

        // 汇总返回
        Map<String, Object> response = new HashMap<>();

        // UNI轨迹结果（仅包含符合条件的UNI订单）
        Map<String, Map<String, Object>> filteredUniResult = new LinkedHashMap<>();
        Map<String, Map<String, Object>> tempUniTrackResult = uniTrackResult;
        validUniMainOrders.forEach(mainOrderNo -> {
            if (tempUniTrackResult.containsKey(mainOrderNo)) {
                filteredUniResult.put(mainOrderNo, tempUniTrackResult.get(mainOrderNo));
            }
        });

        response.put("result", nbFinalResult);
        response.put("uniTrackResult", filteredUniResult);

        return R.ok(response);
//        return R.ok();
    }


    // 中大件-客户端轨迹查询
    @Override
    public R getZdjClientTrack(TmsOrderNoTrackVo orderNos) {
        // 解析输入单号
        List<String> orderNoList = orderNos.getOrderNos().stream()
                .map(String::trim)
                .distinct()
                .collect(Collectors.toList());

        // 获取客户信息
        Long userId = SecurityUtils.getUser().getId();
        TmsCustomerEntity customer = customerMapper.selectOne(
                new LambdaQueryWrapper<TmsCustomerEntity>()
                        .eq(TmsCustomerEntity::getUserId, userId), false
        );
        Long customerId = Optional.ofNullable(customer)
                .map(TmsCustomerEntity::getId)
                .orElse(0L);

        // 查询匹配的订单
        LambdaQueryWrapper<TmsCustomerOrderEntity> trackQueryWrapper = new LambdaQueryWrapper<>();
        trackQueryWrapper.eq(TmsCustomerOrderEntity::getCustomerId, customerId)
                .eq(TmsCustomerOrderEntity::getDelFlag, "0")
                .and(wrapper -> wrapper
                        .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNoList)
                        .or()
                        .in(TmsCustomerOrderEntity::getCustomerOrderNumber, orderNoList));
        List<TmsCustomerOrderEntity> trackOrders = customerOrderMapper.selectList(trackQueryWrapper);
        if (CollUtil.isEmpty(trackOrders)) {
            return LocalizedR.failed("tms.No.order.tracking.info", "");
        }

        // 提取主单号
        List<String> inputMainOrderNos = orderNoList.stream()
                .map(inputNo -> {
                    for (TmsCustomerOrderEntity order : trackOrders) {
                        if (inputNo.equals(order.getEntrustedOrderNumber()) || inputNo.equals(order.getCustomerOrderNumber())) {
                            if (Boolean.TRUE.equals(order.getSubFlag())) {
                                String subNo = order.getEntrustedOrderNumber();
                                return (subNo != null && subNo.length() >= 13) ? subNo.substring(0, 13) : null;
                            } else {
                                return order.getEntrustedOrderNumber();
                            }
                        }
                    }
                    return null;
                })
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        // 查询主单数据
        LambdaQueryWrapper<TmsCustomerOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TmsCustomerOrderEntity::getSubFlag, Boolean.FALSE)
                .eq(TmsCustomerOrderEntity::getDelFlag, "0")
                .eq(TmsCustomerOrderEntity::getCustomerId, customerId)
                .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, inputMainOrderNos);
        List<TmsCustomerOrderEntity> customerOrders = customerOrderMapper.selectList(queryWrapper);

        // 查询换单表并建立映射
        LambdaQueryWrapper<TmsThirdPartPostEntity> thirdPartQuery = new LambdaQueryWrapper<>();
        thirdPartQuery.and(wrapper -> {
            for (String mainOrderNo : inputMainOrderNos) {
                wrapper.or().likeRight(TmsThirdPartPostEntity::getNbOrderNo, mainOrderNo);
            }
        });
        List<TmsThirdPartPostEntity> thirdPartOrders = tmsThirdPartPostService.list(thirdPartQuery);

        Map<String, List<TmsThirdPartPostEntity>> mainOrderToThirdPartMap = new HashMap<>();
        for (TmsThirdPartPostEntity thirdPartOrder : thirdPartOrders) {
            String nbSubOrderNo = thirdPartOrder.getNbOrderNo();
            if (StrUtil.isNotBlank(nbSubOrderNo) && nbSubOrderNo.length() > 3) {
                String mainOrderNo = nbSubOrderNo.substring(0, nbSubOrderNo.length() - 3);
                mainOrderToThirdPartMap.computeIfAbsent(mainOrderNo, k -> new ArrayList<>()).add(thirdPartOrder);
            }
        }

        //  分离UNI和NB订单
        List<String> uniOrderNumbers = new ArrayList<>();
        List<TmsCustomerOrderEntity> nbOrderNumbers = new ArrayList<>();
        List<TmsCustomerOrderEntity> mayFallbackToNbOrders = new ArrayList<>();

        for (TmsCustomerOrderEntity order : customerOrders) {
            String mainOrderNo = order.getEntrustedOrderNumber();
            if (mainOrderToThirdPartMap.containsKey(mainOrderNo)) {
                // 存在换单记录，先加入UNI查询集合
                mayFallbackToNbOrders.add(order);
                // 收集UNI订单号
                List<TmsThirdPartPostEntity> relatedThirdPartOrders = mainOrderToThirdPartMap.get(mainOrderNo);
                for (TmsThirdPartPostEntity thirdPart : relatedThirdPartOrders) {
                    if (StrUtil.isNotBlank(thirdPart.getChannelOrderNo())) {
                        uniOrderNumbers.add(thirdPart.getChannelOrderNo());
                    }
                }
            } else {
                // 没有换单记录，直接加入NB集合
                nbOrderNumbers.add(order);
            }
        }

        // 查询UNI轨迹并筛选有效订单
        Map<String, Map<String, Object>> uniTrackResult = new LinkedHashMap<>();
        Set<String> validUniMainOrders = new HashSet<>(); // 记录有效的UNI主单

        if (CollUtil.isNotEmpty(uniOrderNumbers)) {
            uniTrackResult = querySmallPackageTrack(uniOrderNumbers);

            // 筛选出有两条及以上轨迹的主单
            for (Map.Entry<String, Map<String, Object>> entry : uniTrackResult.entrySet()) {
                Map<String, Object> mainOrderInfo = entry.getValue();
                List<Map<String, Object>> subOrders = (List<Map<String, Object>>) mainOrderInfo.get("subOrders");

                if (CollUtil.isNotEmpty(subOrders)) {
                    for (Map<String, Object> subOrder : subOrders) {
                        List<Map<String, Object>> trackDetails = (List<Map<String, Object>>) subOrder.get("trackDetails");
                        if (CollUtil.isNotEmpty(trackDetails) && trackDetails.size() >= 2) {
                            validUniMainOrders.add(entry.getKey());
                            break;
                        }
                    }
                }
            }

            // 将不符合条件的订单加入回退集合
            for (TmsCustomerOrderEntity order : mayFallbackToNbOrders) {
                if (!validUniMainOrders.contains(order.getEntrustedOrderNumber())) {
                    nbOrderNumbers.add(order);
                }
            }
        }

        // 处理NB轨迹
        Map<String, Map<String, Object>> nbFinalResult = new LinkedHashMap<>();
        if (CollUtil.isNotEmpty(nbOrderNumbers)) {
            // 获取全部nbOrderNumbers中所有单号并去重
            Set<String> nbOrderNumbersSet = nbOrderNumbers.stream()
                    .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                    .collect(Collectors.toSet());

            // 签收图片映射
            Map<String, String> mainOrderSignImgMap = nbOrderNumbers.stream()
                    .filter(order -> order.getEntrustedOrderNumber() != null && order.getDeliveryProof() != null)
                    .collect(Collectors.toMap(
                            TmsCustomerOrderEntity::getEntrustedOrderNumber,
                            TmsCustomerOrderEntity::getDeliveryProof,
                            (v1, v2) -> v1
                    ));

            // 查询所有子单
            LambdaQueryWrapper<TmsCustomerOrderEntity> subQueryWrapper = new LambdaQueryWrapper<>();
            subQueryWrapper.eq(TmsCustomerOrderEntity::getSubFlag, true);
            subQueryWrapper.and(wrapper -> {
                for (String mainOrderNo : nbOrderNumbersSet) {
                    wrapper.or().likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainOrderNo);
                }
            });
            List<TmsCustomerOrderEntity> subOrders = customerOrderMapper.selectList(subQueryWrapper);

            // 查询每个子单最新轨迹
            Map<String, TmsOrderTrackEntity> latestTracks = new HashMap<>();
            for (TmsCustomerOrderEntity subOrder : subOrders) {
                String subOrderNo = subOrder.getEntrustedOrderNumber();
                TmsOrderTrackEntity latestTrack = orderTrackMapper.selectOne(
                        new LambdaQueryWrapper<TmsOrderTrackEntity>()
                                .eq(TmsOrderTrackEntity::getOrderNo, subOrderNo)
                                .eq(TmsOrderTrackEntity::getTrackType, TrackTypeConstant.EXTERNAL)
                                .orderByDesc(TmsOrderTrackEntity::getAddTime)
                                .last("LIMIT 1")
                );
                if (latestTrack != null) {
                    latestTracks.put(subOrderNo, latestTrack);
                }
            }

            // 构建返回结果
            for (String mainOrderNo : nbOrderNumbersSet) {
                List<Map<String, Object>> subOrderList = subOrders.stream()
                        .filter(subOrder -> subOrder.getEntrustedOrderNumber().startsWith(mainOrderNo))
                        .map(subOrder -> {
                            String subOrderNo = subOrder.getEntrustedOrderNumber();
                            TmsOrderTrackEntity track = latestTracks.get(subOrderNo);
                            Map<String, Object> item = new HashMap<>();
                            item.put("mainOrderNo", mainOrderNo);
                            item.put("subOrderNo", subOrderNo);
                            item.put("orderStatus", subOrder.getOrderStatus());
                            item.put("trackDesc", track != null ? track.getExternalDescription() : null);
                            item.put("trackTime", track != null ? track.getAddTime() : null);
                            item.put("trackOperator", track != null ? track.getCreateBy() : null);
                            item.put("signImgUrl", StrUtil.isNotBlank(subOrder.getDeliveryProof()) ? subOrder.getDeliveryProof() : null); // 加入子单签收图
                            return item;
                        })
                        .collect(Collectors.toList());

                Map<String, Object> mainOrderInfo = new HashMap<>();
                mainOrderInfo.put("signImgUrl", mainOrderSignImgMap.get(mainOrderNo));
                mainOrderInfo.put("subOrders", subOrderList);
                nbFinalResult.put(mainOrderNo, mainOrderInfo);
            }
        }

        // 汇总返回
        Map<String, Object> response = new HashMap<>();

        // UNI轨迹结果（仅包含符合条件的UNI订单）
        Map<String, Map<String, Object>> filteredUniResult = new LinkedHashMap<>();
        Map<String, Map<String, Object>> tempUniTrackResult = uniTrackResult;
        validUniMainOrders.forEach(mainOrderNo -> {
            if (tempUniTrackResult.containsKey(mainOrderNo)) {
                filteredUniResult.put(mainOrderNo, tempUniTrackResult.get(mainOrderNo));
            }
        });

        int totalOrders = customerOrders.size();
        Map<Integer, Long> statusCountMap = customerOrders.stream()
                .collect(Collectors.groupingBy(TmsCustomerOrderEntity::getOrderStatus, Collectors.counting()));

        response.put("totalOrders", totalOrders);
        response.put("statusCountMap", statusCountMap);
        response.put("result", nbFinalResult);   // NB轨迹结果
        response.put("uniTrackResult", filteredUniResult);  // 仅包含符合条件的UNI轨迹

        return R.ok(response);
    }


    // 中大件客户端-根据箱号查询对应轨迹节点记录
    @Override
    public R getZdjClientTrackList(String subOrderNo) {
        if (subOrderNo.trim().length() < 15) {
            return R.ok();
        }

        LambdaQueryWrapper<TmsOrderTrackEntity> wrapper = Wrappers.lambdaQuery();
        String tempSubOrderNo = (subOrderNo == null) ? "0000" : subOrderNo;

        // 查询该箱单号的轨迹
        wrapper.eq(TmsOrderTrackEntity::getOrderNo, tempSubOrderNo);
        wrapper.in(TmsOrderTrackEntity::getTrackType, Arrays.asList(TrackTypeConstant.EXTERNAL));
        wrapper.orderByDesc(TmsOrderTrackEntity::getAddTime);

        List<TmsOrderTrackEntity> trackList = orderTrackService.list(wrapper);

        if (CollUtil.isEmpty(trackList)) {
            return R.failed("未查询到该订单轨迹");
        }
        return R.ok(trackList);
    }

    /**
     * 卡派客户订单API下单接口
     *
     * @param apiOrder
     * @return
     */
    @Override
    public R createKpApiOrder(ApiOrder apiOrder) {
        TmsCustomerOrderEntity order = new TmsCustomerOrderEntity();
        order.setId(null); // 主键ID，由数据库自增生成

        TmsCustomerEntity customer = customerMapper.selectOne(new LambdaQueryWrapper<TmsCustomerEntity>()
                .eq(TmsCustomerEntity::getIsValid, 1).eq(TmsCustomerEntity::getCustomerCode, apiOrder.getChannelCode()));

        String customerOrderNumber = apiOrder.getReferenceNo();
        order.setCustomerOrderNumber(customerOrderNumber); // 客户单号，apiOrder中的客户参考号

        // 生成委托单号
        String entrustedOrderNumber = generalNewOrderNo(customer.getId(), apiOrder.getShipMode());
        order.setEntrustedOrderNumber(entrustedOrderNumber);

        order.setCustomerId(customer.getId()); // 客户ID
        order.setOrderStatus(0); // 订单状态(23001=待指派、23002=已取消、23003=已指派、23004=已驳回、23005=待运输、23006=待收货、23007=已完成
        order.setAuditStatus(0); // 审核状态，默认0
        order.setShipperName(apiOrder.getShipperName()); // 发货人姓名
        order.setShipperPhone(apiOrder.getShipperPhone()); // 发货人电话
        String shipperCountryCode = apiOrder.getShipperCountryCode();
        if (apiOrder.getShipperCountryCode().contains("CA")) {
            shipperCountryCode = "Canada";
        }
        order.setOrigin(shipperCountryCode + "/" + apiOrder.getShipperProvince() + "/" + apiOrder.getShipperCity()); // 始发地（拼接发件人国家/州/城市）
        order.setShipperPostalCode(apiOrder.getShipperPostcode()); // 发货邮编
        order.setShipperAddress(apiOrder.getShipperAddress()); // 发货详细地址
        order.setCarrierId(0L);
        order.setEstimatedShippingTimeStart(apiOrder.getEstimatedShippingTimeStart()); // 预计发货时间开始
        order.setEstimatedShippingTimeEnd(apiOrder.getEstimatedShippingTimeEnd()); // 预计发货时间结束
        order.setOrderStatus(CustomerOrderStatus.UNASSIGNED.getCode());
        order.setOrderType(apiOrder.getOrderType());
        order.setCargoType(apiOrder.getCargoType());
        order.setTransportType(apiOrder.getTransportType());
        order.setCustomerOrderNumber(apiOrder.getReferenceNo());
        Integer addressType = apiOrder.getAddressType();
        if (addressType != null) {
            // 是否尾板提货：0 否，1 是
            order.setIsTailgatePickup((addressType == 2 || addressType == 3) ? 1 : 0);
            // 是否尾板卸货：0 否，1 是
            order.setIsTailgateUnloaded((addressType == 2 || addressType == 3) ? 1 : 0);
        }
        order.setAddressType(apiOrder.getAddressType() == null ? 1 : addressType);
        order.setReceiverName(apiOrder.getConsigneeName()); // 到货人姓名
        order.setReceiverPhone(apiOrder.getConsigneePhone()); // 到货人电话
        String consigneeCountryCode = apiOrder.getConsigneeCountryCode();
        if (apiOrder.getConsigneeCountryCode().contains("CA")) {
            consigneeCountryCode = "Canada";
        }
        order.setDestination(consigneeCountryCode + "/" + apiOrder.getConsigneeProvince() + "/" + apiOrder.getConsigneeCity()); // 目的地（拼接收件人国家/州/城市）
        order.setDestPostalCode(apiOrder.getConsigneePostcode()); // 到货邮编
        order.setDestAddress(apiOrder.getConsigneeAddress()); // 到货详细地址
        order.setEstimatedArrivalTimeStart(apiOrder.getEstimatedArrivalTimeStart()); // 预计到货时间开始
        order.setEstimatedArrivalTimeEnd(apiOrder.getEstimatedArrivalTimeEnd()); // 预计到货时间结束
        order.setBusinessModel(apiOrder.getShipMode()); // 业务模式：1 揽收，2 中大件，3 卡派
        order.setCargoQuantity(apiOrder.getApiOrderVolumeList().size()); // 货品总数量，默认0，订单材积明细列表的长度
        order.setTotalWeight(apiOrder.getTotalWeight()); // 总重量
        order.setTotalVolume(apiOrder.getTotalVolume()); // 总体积
        order.setTotalFreight(apiOrder.getTotalFreight()); // 总运费合计
        order.setOperator(customer.getCustomerName()); // 操作人(客户下单的，取客户名称)

        List<SubOrderEntity> apiOrderVolumeList = apiOrder.getApiOrderVolumeList();
        List<TmsCargoInfoEntity> cargoInfoEntityList = new ArrayList<>();
        for (int i = 0; i < apiOrderVolumeList.size(); i++) {
            SubOrderEntity volume = apiOrderVolumeList.get(i);
            TmsCargoInfoEntity cargoInfo = new TmsCargoInfoEntity();
            cargoInfo.setId(null);
            cargoInfo.setCargoDescription(volume.getCargoDescription());  // 货物描述
            cargoInfo.setCargoQuantity(volume.getQuantity()); // 单箱货物数量
            cargoInfo.setLength(volume.getLength());
            cargoInfo.setWidth(volume.getWidth());
            cargoInfo.setHeight(volume.getHeight());
            cargoInfo.setWeight(volume.getWeight());
            if (apiOrder.getShipMode() == 2) {
                if (apiOrder.getOrderType() == 1) {
                    cargoInfo.setBoxMaxWeight(BigDecimal.valueOf(30.00));   //单箱重量不超过30
                }
                if (apiOrder.getOrderType() == 2) {
                    cargoInfo.setPkgMaxWeight(BigDecimal.valueOf(30.00));   //单包裹重量不超过30
                }
            }

            cargoInfo.setBagNum(String.format("%03d", i + 1));  // 箱号，从001开始
//            cargoInfo.setBagNum(volume.getBagNum());
            cargoInfo.setEntrustedOrderNumber(entrustedOrderNumber);    // 委托单号
            cargoInfo.setCustomerOrderNumber(customerOrderNumber);  // 客户参考号
            cargoInfo.setCargoDescription(volume.getCargoDescription());
            cargoInfo.setCreateBy(customer.getCustomerName());
            cargoInfo.setUpdateBy(customer.getCustomerName());
            cargoInfo.setCreateTime(LocalDateTime.now());
            cargoInfo.setUpdateTime(LocalDateTime.now());
            cargoInfoEntityList.add(cargoInfo);
        }
        cargoInfoService.saveBatch(cargoInfoEntityList);    // 批量保存货物信息
        order.setCargoInfoEntityList(cargoInfoEntityList); // 货物信息

        // 附加服务
        List<TmsAdditionalServicesEntity> additionalServicesList = apiOrder.getTmsAdditionalServicesList();
        List<TmsAdditionalServicesEntity> additionalServices = new ArrayList<>();
        for (int i = 0; i < additionalServicesList.size(); i++) {
            TmsAdditionalServicesEntity servicesEntity = additionalServicesList.get(i);
            TmsAdditionalServicesEntity service = new TmsAdditionalServicesEntity();
            service.setId(null);
            service.setEntrustedOrderNumber(entrustedOrderNumber);    // 委托单号
            service.setCustomerOrderNumber(customerOrderNumber);    // 客户单号(客户参考号)
            service.setAdditionalServiceType(servicesEntity.getAdditionalServiceType());  // 附加服务类型
            service.setAdditionalServiceValue(servicesEntity.getAdditionalServiceValue());// 附加服务取值
            service.setAdditionalServiceTime(Optional.ofNullable(servicesEntity.getAdditionalServiceTime()).map(Object::toString).orElse(""));
            service.setIsUrban(servicesEntity.getIsUrban());  // 是否城市郊区：0 郊区，1 城市
            service.setCreateBy(customer.getCustomerName());
            service.setUpdateBy(customer.getCustomerName());
            service.setCreateTime(LocalDateTime.now());
            service.setUpdateTime(LocalDateTime.now());
            additionalServices.add(service);
            tmsAdditionalServicesService.saveBatch(additionalServices); // 批量保存附加服务
        }
        order.setAdditionalServicesEntityList(additionalServices); // 附加服务

        // 调用服务层方法保存订单
        boolean isSuccess = save(order);
        if (isSuccess) {
            // 保存轨迹
            orderTrackService.saveTrack(entrustedOrderNumber, order.getCustomerOrderNumber(), CustomerOrderStatus.UNASSIGNED.getValue(), "", "API下单待询价", "", 1);

            // API下单询价
            TmsEntrustedOrderVo vo = new TmsEntrustedOrderVo();
            vo.setOrderType(order.getOrderType());
            vo.setOrigin(order.getOrigin());
            vo.setDestination(order.getDestination());
            vo.setSumOrderWeight(order.getTotalWeight());
            vo.setSumOrderNumber(order.getCargoQuantity());
            vo.setSumOrderVolume(order.getTotalVolume());
            this.getOrderPrice(vo);

            return LocalizedR.ok(order.getEntrustedOrderNumber());
        } else {
            return LocalizedR.failed("tms.order.creation.failed", "");
        }

    }


    // （暂时解决办法）
    private final ThrottleLock throttleLock = new ThrottleLock(360);

    /**
     * 中大件客户订单API下单接口
     *
     * @param apiOrder
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R createZdjApiOrder(ApiOrder apiOrder) {

        //转换API信息
        TmsCustomerOrderEntity order = new TmsCustomerOrderEntity();
        String customerOrderNumber = apiOrder.getReferenceNo();
        order.setReceiveType(apiOrder.getReceiveType());
        order.setId(null);
        order.setCustomerOrderNumber(customerOrderNumber);
        TmsCustomerEntity customer = customerMapper.selectOne(new LambdaQueryWrapper<TmsCustomerEntity>()
                .eq(TmsCustomerEntity::getIsValid, 1).eq(TmsCustomerEntity::getCustomerCode, apiOrder.getChannelCode()));
        order.setCustomerId(customer.getId());
        order.setShipperName(apiOrder.getShipperName());
        order.setShipperPhone(apiOrder.getShipperPhone());
        String shipperCountryCode = apiOrder.getShipperCountryCode();
        if (apiOrder.getShipperCountryCode().contains("CA")) {
            shipperCountryCode = "Canada";
        }
        order.setOrigin(shipperCountryCode + "/" + apiOrder.getShipperProvince() + "/" + apiOrder.getShipperCity());
        order.setShipperPostalCode(apiOrder.getShipperPostcode());
        order.setShipperAddress(apiOrder.getShipperAddress());
        order.setEstimatedShippingTimeStart(apiOrder.getEstimatedShippingTimeStart());
        order.setEstimatedShippingTimeEnd(apiOrder.getEstimatedShippingTimeEnd());
        order.setOrderType(apiOrder.getOrderType());
        order.setCargoType(apiOrder.getCargoType());
        order.setTransportType(apiOrder.getTransportType());
        order.setCustomerOrderNumber(apiOrder.getReferenceNo());
        order.setAddressType(apiOrder.getAddressType());
        order.setReceiverName(apiOrder.getConsigneeName());
        order.setReceiverPhone(apiOrder.getConsigneePhone());
        String consigneeCountryCode = apiOrder.getConsigneeCountryCode();
        if (apiOrder.getConsigneeCountryCode().contains("CA")) {
            consigneeCountryCode = "Canada";
        }
        order.setDestination(consigneeCountryCode + "/" + apiOrder.getConsigneeProvince() + "/" + apiOrder.getConsigneeCity());
        order.setDestPostalCode(apiOrder.getConsigneePostcode());
        order.setDestAddress(apiOrder.getConsigneeAddress());
        order.setEstimatedArrivalTimeStart(apiOrder.getEstimatedArrivalTimeStart());
        order.setEstimatedArrivalTimeEnd(apiOrder.getEstimatedArrivalTimeEnd());
        order.setBusinessModel(apiOrder.getShipMode());
        order.setCargoQuantity(apiOrder.getApiOrderVolumeList().size());
        order.setOperator(customer.getCustomerName());
        //处理货物信息
        List<SubOrderEntity> apiOrderVolumeList = apiOrder.getApiOrderVolumeList();
        List<TmsCargoInfoEntity> cargoInfoEntityList = new ArrayList<>();
        for (int i = 0; i < apiOrderVolumeList.size(); i++) {
            SubOrderEntity volume = apiOrderVolumeList.get(i);
            TmsCargoInfoEntity cargoInfo = new TmsCargoInfoEntity();
            cargoInfo.setId(null);
            cargoInfo.setCargoDescription(volume.getCargoDescription());
            cargoInfo.setCargoQuantity(volume.getQuantity());
            cargoInfo.setLength(volume.getLength());
            cargoInfo.setWidth(volume.getWidth());
            cargoInfo.setHeight(volume.getHeight());
            cargoInfo.setWeight(volume.getWeight());
            cargoInfo.setCustomerOrderNumber(customerOrderNumber);
            cargoInfo.setCargoDescription(volume.getCargoDescription());
            cargoInfo.setCreateBy(customer.getCustomerName());
            cargoInfo.setUpdateBy(customer.getCustomerName());
            cargoInfo.setBoxNum(volume.getBagNum());
            if (order.getOrderType() == 1) {
                cargoInfo.setBoxMaxWeight(volume.getBigWeight());
            } else {
                cargoInfo.setPkgMaxWeight(volume.getBigWeight());
            }
            cargoInfoEntityList.add(cargoInfo);
        }
        order.setCargoInfoEntityList(cargoInfoEntityList);

        //设置订单基本信息
        String validationError = validateOrder(order);
        if (validationError != null) {
            return R.failed(validationError);
        }

        // 如何页面没有填写单号，则后台自动生成客户单号
        if (StrUtil.isBlank(customerOrderNumber)) {
            customerOrderNumber = generateCustomerOrderNumber();
            order.setCustomerOrderNumber(customerOrderNumber);
        }
        String entrustedOrderNumber = throttleLock.call(() -> generalNewOrderNo(order.getCustomerId(), order.getBusinessModel()));
        System.out.println("本次获取到的委托单号为" + entrustedOrderNumber + "time" + System.currentTimeMillis());
        order.setEntrustedOrderNumber(entrustedOrderNumber);
        // 调用服务层方法统一处理订单
        return createZDJOrder(order, true);
    }

    // 根据客户单号或委托单号查询客户订单
    @Override
    public TmsCustomerOrderEntity getByCustomerOrderNumber(String customerOrderNumber, Boolean isReferenceNumber) {
        LambdaQueryWrapper<TmsCustomerOrderEntity> wrapper = new LambdaQueryWrapper<>();
        if (isReferenceNumber) {
            wrapper.eq(TmsCustomerOrderEntity::getCustomerOrderNumber, customerOrderNumber)
                    .eq(TmsCustomerOrderEntity::getDelFlag, "0")
                    .last("limit 1");
            TmsCustomerOrderEntity tmsCustomerOrderEntity =
                    Optional.ofNullable(customerOrderMapper.selectOne(wrapper))
                            .orElseGet(TmsCustomerOrderEntity::new);
            //获取货物信息
            List<TmsCargoInfoEntity> tmsCargoInfoEntities = tmsCargoInfoService.listByOrderNumber(customerOrderNumber, null);
            //获取附加服务信息(卡派）
            List<TmsAdditionalServicesEntity> tmsAdditionalServicesEntities = tmsAdditionalServicesService.listByOrderNumber(customerOrderNumber, null);
            tmsCustomerOrderEntity.setCargoInfoEntityList(tmsCargoInfoEntities);
            tmsCustomerOrderEntity.setAdditionalServicesEntityList(tmsAdditionalServicesEntities);
            return tmsCustomerOrderEntity;
        }
        wrapper.eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, customerOrderNumber)
                .eq(TmsCustomerOrderEntity::getDelFlag, "0")
                .last("limit 1");
        TmsCustomerOrderEntity tmsCustomerOrderEntity =
                Optional.ofNullable(customerOrderMapper.selectOne(wrapper))
                        .orElseGet(TmsCustomerOrderEntity::new);
        //获取货物信息
        List<TmsCargoInfoEntity> tmsCargoInfoEntities = tmsCargoInfoService.listByOrderNumber(null, tmsCustomerOrderEntity.getEntrustedOrderNumber());
        //获取附加服务信息
        List<TmsAdditionalServicesEntity> tmsAdditionalServicesEntities = tmsAdditionalServicesService.listByOrderNumber(null, tmsCustomerOrderEntity.getEntrustedOrderNumber());
        tmsCustomerOrderEntity.setCargoInfoEntityList(tmsCargoInfoEntities);
        tmsCustomerOrderEntity.setAdditionalServicesEntityList(tmsAdditionalServicesEntities);
        return tmsCustomerOrderEntity;
    }


    //根据子单号获取订单信息(主要用于面单)
    @Override
    public TmsCustomerOrderEntity getCustomerOrderBySubOrder(String trackingNo) {
        LambdaQueryWrapper<TmsCustomerOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, trackingNo)
                .eq(TmsCustomerOrderEntity::getDelFlag, "0")
                .last("limit 1");
        TmsCustomerOrderEntity tmsCustomerOrderEntity =
                Optional.ofNullable(customerOrderMapper.selectOne(wrapper))
                        .orElseGet(TmsCustomerOrderEntity::new);
        //获取货物信息
        List<TmsCargoInfoEntity> tmsCargoInfoEntities = tmsCargoInfoService.listByOrderNumber(null, trackingNo);
        tmsCustomerOrderEntity.setCargoInfoEntityList(tmsCargoInfoEntities);
        return tmsCustomerOrderEntity;
    }

    @Override
    public R updatePod(String orderNo, String picUrl) {
        TmsCustomerOrderEntity one = this.getOne(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNo));
        if (ObjectUtil.isNotNull(one)) {
            one.setDeliveryProof(picUrl);
        } else {
            return LocalizedR.failed("tms.the.order.does.not.exist", "");
        }
        return R.ok(this.updateById(one));
    }

    // 面单打印校验
    @Override
    public R labelCheckout(String orderNo) {
        // 根据主单号 likeRight 查询换单表中所有子单
        List<TmsThirdPartPostEntity> thirdPartOrders = tmsThirdPartPostService.list(
                new LambdaQueryWrapper<TmsThirdPartPostEntity>()
                        .likeRight(TmsThirdPartPostEntity::getNbOrderNo, orderNo)
        );

        if (CollUtil.isEmpty(thirdPartOrders)) {
            // 未找到任何记录
            return R.ok(null);
        }

        // 收集所有子单的面单地址
        List<Map<String, Object>> labelList = thirdPartOrders.stream()
                .filter(item -> StrUtil.isNotBlank(item.getLabelUrl())) // 有面单地址的
                .map(item -> {
                    Map<String, Object> labelMap = new HashMap<>();
                    labelMap.put("subOrderNo", item.getNbOrderNo());
                    labelMap.put("labelUrl", item.getLabelUrl());
                    return labelMap;
                })
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(labelList)) {
            // 有换单记录但没有有效面单地址
            return R.ok(null);
        }

        return R.ok(labelList);
    }

    // 根据订单号查询订单日志
    @Override
    public R getOrderLog(String orderNo) {
        // 查询日志记录
        List<TmsOrderLogEntity> logList = tmsOrderLogService.list(
                new LambdaQueryWrapper<TmsOrderLogEntity>()
                        .likeRight(TmsOrderLogEntity::getOrderNo, orderNo)
                        .orderByDesc(TmsOrderLogEntity::getTime)
        );

        // 如果没有日志记录，返回空集合
        if (logList == null || logList.isEmpty()) {
            return R.ok(Collections.emptyList());
        }

        // 转换返回（可选：如果你需要VO返回，这里可以做VO封装）
        return R.ok(logList);
    }

    @Override
    public R sortingUpdate(String orderNo) {
        // 根据分拣机传过来的包裹（子单号）
            TmsCustomerOrderEntity customerOrder = this.getOne(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNo));
        if (ObjectUtil.isNull(customerOrder)) {
            throw new CustomBusinessException("未查到该包裹单号！");
        }

        // 复用分拣更新逻辑，传入单个订单的列表
        tmsLmdSortingService.updateOrderStatusBySorting(Arrays.asList(customerOrder),"Canada/BC/Vancouver","V7P 3N6");

        return R.ok("分拣更新成功");
    }

    @Override
    public void sortingOperated(List<String> entrustedOrderNumbers) {
        if(CollUtil.isEmpty(entrustedOrderNumbers)){
            return;
        }
        Map<String, List<TmsCustomerOrderEntity>> customerOrderMap = baseMapper.getMapByEntrustedOrderNumbers(entrustedOrderNumbers);
        if(null == customerOrderMap){
            return;
        }
        for (String item : entrustedOrderNumbers) {
            List<TmsCustomerOrderEntity> tmsCustomerOrderEntities = customerOrderMap.get(item);
            if(CollUtil.isEmpty(tmsCustomerOrderEntities)){
                continue;
            }
            tmsLmdSortingService.updateOrderStatusBySorting(tmsCustomerOrderEntities,"Canada/BC/Vancouver","V7P 3N6");
        }
    }

    @Override
    public List<TmsCustomerOrderEntity> getCustomerOrderByMain(String orderNo) {
        if(StrUtil.isBlank(orderNo)){
            return new ArrayList<>();
        }
        return baseMapper.getCustomerOrderByMain(orderNo);
    }

    //获取订单POD信息
    @Override
    public PodVo getCustomerOrderPod(String trackingNo) {
        PodVo podVo = new PodVo();
        LocalDateTime  finishTime = null;
        LocalDateTime  updateTime = null;
        // 签收地址
        String signAddress = null;
        // 签收经纬度
        String receiverLatLng = null;
        StringBuilder builder = new StringBuilder();
        LambdaQueryWrapper<TmsCustomerOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, trackingNo)
                .eq(TmsCustomerOrderEntity::getDelFlag, "0");
        List<TmsCustomerOrderEntity> list = this.list(wrapper);
        for (TmsCustomerOrderEntity entity : list){
            builder.append(entity.getDeliveryProof()==null?"":entity.getDeliveryProof()+ ",");
            if (finishTime == null){
                finishTime = entity.getFinishTime();
            }
            if (updateTime == null){
                updateTime = entity.getUpdateTime();
            }
            if (signAddress == null){
                signAddress = entity.getDestination()+"/"+entity.getDestAddress();
            }
            if (receiverLatLng == null){
                receiverLatLng = entity.getReceiverLatLng();
            }
        }
        podVo.setPod(builder.toString());
        podVo.setTime(finishTime==null?updateTime:finishTime);
        podVo.setSignAddress(signAddress);
        podVo.setReceiverLatLng(receiverLatLng);
        return podVo;
    }

    //获取订子单POD信息
    @Override
    public List<SubOrderPodVo> getCustomerSubOrderPod(String trackingNo){
        ArrayList<SubOrderPodVo> podVos = new ArrayList<>();
        LambdaQueryWrapper<TmsCustomerOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, trackingNo)
                .eq(TmsCustomerOrderEntity::getSubFlag, true)
                .eq(TmsCustomerOrderEntity::getDelFlag, "0");
        List<TmsCustomerOrderEntity> list = this.list(wrapper);
        for (TmsCustomerOrderEntity entity : list){
            if (entity.getDeliveryProof() == null){
                continue;
            }
            SubOrderPodVo podVo = new SubOrderPodVo();
            LocalDateTime  finishTime = null;
            LocalDateTime  updateTime = null;
            // 签收地址
            String signAddress = null;
            // 签收经纬度
            String receiverLatLng = null;
            finishTime = entity.getFinishTime();
            updateTime = entity.getUpdateTime();
            signAddress = entity.getDestination()+"/"+entity.getDestAddress();
            receiverLatLng = entity.getReceiverLatLng();
            podVo.setOrderNo(entity.getEntrustedOrderNumber());
            podVo.setPods(entity.getDeliveryProof());
            podVo.setTime(finishTime==null?updateTime:finishTime);
            podVo.setSignAddress(signAddress);
            setLatLngFromReceiver(receiverLatLng, podVo);
            podVos.add(podVo);
        }
        return podVos;
    }

    @Override
    public boolean addReviewVolumeAndWeight(BigDecimal volume, BigDecimal weight, String orderNo) {
        if (orderNo.length() < 13){
            return false;
        }
        // 获取主单号
        String mainOrderNo = orderNo.substring(0, 13);

        // 更新当前子单的复核重量和体积
        boolean updateResult = this.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNo)
                .eq(TmsCustomerOrderEntity::getSubFlag, Boolean.TRUE)
                .set(TmsCustomerOrderEntity::getReviewVolume, volume)
                .set(TmsCustomerOrderEntity::getReviewWeight, weight));

        if (!updateResult) {
            return false;
        }

        // 查询所有子单并统计主单的总复核重量和体积
        List<TmsCustomerOrderEntity> subOrders = this.list(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainOrderNo)
                .eq(TmsCustomerOrderEntity::getSubFlag, Boolean.TRUE));

        if (CollUtil.isEmpty(subOrders)) {
            return false;
        }

        // 计算总重量和体积（处理null值）
        BigDecimal totalVolume = subOrders.stream()
                .map(TmsCustomerOrderEntity::getReviewVolume)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalWeight = subOrders.stream()
                .map(TmsCustomerOrderEntity::getReviewWeight)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 更新主单的复核重量和体积
        return this.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainOrderNo)
                .eq(TmsCustomerOrderEntity::getSubFlag, Boolean.FALSE)
                .set(TmsCustomerOrderEntity::getReviewVolume, totalVolume)
                .set(TmsCustomerOrderEntity::getReviewWeight, totalWeight));
    }


    //设置PUD经纬度
    private void setLatLngFromReceiver(String receiverLatLng , SubOrderPodVo vo) {
        if (receiverLatLng == null || receiverLatLng.isEmpty()) return;
        String[] latLng = receiverLatLng.split(",");
        if (latLng.length == 2) {
            vo.setLatitude(latLng[0]);
            vo.setLongitude(latLng[1]);
        }
    }

    //查询指定时间订单PDF合并
    @Override
    public void getOrderByJobCondition() {
        LocalDateTime time = LocalDateTime.now().minusHours(1);
        LambdaQueryWrapper<TmsCustomerOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.gt(TmsCustomerOrderEntity::getCreateTime, time)
                .isNull(TmsCustomerOrderEntity::getPushLabel)
                .eq(TmsCustomerOrderEntity::getSubFlag, false);
        List<TmsCustomerOrderEntity> list = this.list(wrapper);
        for (TmsCustomerOrderEntity entity : list) {
            LambdaQueryWrapper<TmsThirdPartPostEntity> thirdWrapper = new LambdaQueryWrapper<>();
            thirdWrapper.likeRight(TmsThirdPartPostEntity::getNbOrderNo, entity.getEntrustedOrderNumber())
                    .gt(TmsThirdPartPostEntity::getTime, time);
            List<TmsThirdPartPostEntity> thirdList = tmsThirdPartPostService.list(thirdWrapper);
            ArrayList<String> urls = new ArrayList<>();
            for (TmsThirdPartPostEntity thirdEntity : thirdList) {
                urls.add(thirdEntity.getLabelUrl());
            }
            if (!urls.isEmpty()) {
                try {
                    String url = OrderTools.mergeAndUpload(urls, entity.getEntrustedOrderNumber());
                    LambdaUpdateWrapper<TmsCustomerOrderEntity> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, entity.getEntrustedOrderNumber())
                            .set(TmsCustomerOrderEntity::getPushLabel, url);
                    customerOrderMapper.update(null, updateWrapper);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }

        }
    }

    @Override
    public TmsCustomerOrderEntity getCustomerOrderByOrderNo(String orderNo) {
        if(StrUtil.isBlank(orderNo)){
            return null;
        }
        // 根据跟踪单号或者客户单号进行查询
        LambdaQueryWrapper<TmsCustomerOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TmsCustomerOrderEntity::getEntrustedOrderNumber,orderNo);
        queryWrapper.or();
        queryWrapper.eq(TmsCustomerOrderEntity::getCustomerOrderNumber,orderNo);
        queryWrapper.eq(TmsCustomerOrderEntity::getSubFlag,Boolean.TRUE);
        queryWrapper.last("limit 1");
        return baseMapper.selectOne(queryWrapper);
    }

    //通过客户单号获取子单号
    @Override
   public TmsCustomerOrderEntity getByOrderByCustomerOrderNumber(String customerOrderNumber){
        LambdaQueryWrapper<TmsCustomerOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TmsCustomerOrderEntity::getCustomerOrderNumber, customerOrderNumber)
                .eq(TmsCustomerOrderEntity::getDelFlag, "0")
                .eq(TmsCustomerOrderEntity::getSubFlag, true)
                .last("limit 1");
        return this.getOne(wrapper);
    }


    //通过客户单号或者委托单号查询订单
    @Override
    public TmsCustomerOrderEntity getByOrderNumber(String orderNumber) {
        return this.getOne(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .nested(wrapper -> wrapper
                        .eq(TmsCustomerOrderEntity::getCustomerOrderNumber, orderNumber)
                        .or()
                        .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNumber)
                )
                .eq(TmsCustomerOrderEntity::getSubFlag, true)
                .last("LIMIT 1")
        );
    }



    //换单
    @Override
    public R exchangeOrder(ExchangeVo vo) {
        //查询子单号是否存在
        TmsCustomerOrderEntity tmsCustomerOrderEntity = getCustomerOrderBySubOrderForExchange(vo.getOldOrderNo());
        if (tmsCustomerOrderEntity == null || tmsCustomerOrderEntity.getId() == null) {
            return R.failed("单号不存在！");
        }
        if (StringUtils.isNotBlank(tmsCustomerOrderEntity.getPushLabel())) {
            return R.failed("订单已进行过换单！");
        }
        LambdaUpdateWrapper<TmsCustomerOrderEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, vo.getOldOrderNo())
                .set(TmsCustomerOrderEntity::getPushOrder, vo.getNewOrderNo())
                .set(TmsCustomerOrderEntity::getPushLabel, vo.getLabelUrl());
        //同步到换单列表
        TmsThirdPartPostEntity tmsThirdPartPostEntity = new TmsThirdPartPostEntity();
        tmsThirdPartPostEntity.setChannel(vo.getChannel());
        tmsThirdPartPostEntity.setNbOrderNo(vo.getOldOrderNo());
        tmsThirdPartPostEntity.setChannelOrderNo(vo.getNewOrderNo());
        tmsThirdPartPostEntity.setLabelUrl(vo.getLabelUrl());
        tmsThirdPartPostEntity.setTime(LocalDateTime.now());
        tmsThirdPartPostService.save(tmsThirdPartPostEntity);
        customerOrderMapper.update(null, wrapper);
        return R.ok();
    }

    //更新换单
    @Override
    public R updateExchangeOrder(ExchangeVo vo) {
        //查询子单号是否存在
        TmsCustomerOrderEntity tmsCustomerOrderEntity = getCustomerOrderBySubOrderForExchange(vo.getOldOrderNo());
        if (tmsCustomerOrderEntity == null || tmsCustomerOrderEntity.getId() == null) {
            return R.failed("单号不存在！");
        }
        LambdaUpdateWrapper<TmsCustomerOrderEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, vo.getOldOrderNo())
                .set(TmsCustomerOrderEntity::getPushOrder, vo.getNewOrderNo())
                .set(TmsCustomerOrderEntity::getPushLabel, vo.getLabelUrl());
        //同步到换单列表
        LambdaUpdateWrapper<TmsThirdPartPostEntity> thirdWrapper = new LambdaUpdateWrapper<>();
        thirdWrapper.eq(TmsThirdPartPostEntity::getNbOrderNo, vo.getOldOrderNo())
                .set(TmsThirdPartPostEntity::getChannel, vo.getChannel())
                .set(TmsThirdPartPostEntity::getChannelOrderNo, vo.getNewOrderNo())
                .set(TmsThirdPartPostEntity::getLabelUrl, vo.getLabelUrl())
                .set(TmsThirdPartPostEntity::getTime, LocalDateTime.now());
        tmsThirdPartPostService.update(thirdWrapper);
        customerOrderMapper.update(null, wrapper);
        return R.ok();
    }



    TmsCustomerOrderEntity getCustomerOrderBySubOrderForExchange(String orderNo) {
        LambdaQueryWrapper<TmsCustomerOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNo)
                .eq(TmsCustomerOrderEntity::getSubFlag, true)
                .last("limit 1");
        return customerOrderMapper.selectOne(wrapper);

    }


    //客户推送删除订单
    @Override
    public R deleteOrder(String orderNo) {
        if (orderNo.length() > 50) {
            String[] split = orderNo.split("(?=SY)");
            for (String data : split) {
                TmsCustomerOrderEntity customerOrder = getByCustomerOrderNumber(data, true);
                if (customerOrder == null || customerOrder.getId() == null) {
                    return R.failed("订单不存在" + "订单号：" + data);
                }
                Integer status = customerOrder.getOrderStatus();
                if (!NewOrderStatus.AWAITING_PICKUP.getCode().equals(status) &&
                        !NewOrderStatus.AWAITING_SHIPMENT.getCode().equals(status)) {
                    return R.failed("存在路由信息" + "订单号：" + data);
                }
                if (customerOrder.getCargoInfoEntityList() != null && !customerOrder.getCargoInfoEntityList().isEmpty()) {
                    // 删除原有的货物信息
                    cargoInfoService.remove(new LambdaQueryWrapper<TmsCargoInfoEntity>()
                            .eq(TmsCargoInfoEntity::getCustomerOrderNumber, customerOrder.getCustomerOrderNumber()));
                }
                //删除单号信息
                deleteSubOrder(customerOrder.getCustomerOrderNumber());
                LambdaUpdateWrapper<TmsCustomerOrderEntity> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNo)
                        .set(TmsCustomerOrderEntity::getDelFlag, "1");
                customerOrderMapper.update(null, updateWrapper);
            }
            return R.ok();
        }
        //查询订单信息
        TmsCustomerOrderEntity customerOrder = getByCustomerOrderNumber(orderNo, false);
        if (customerOrder == null || customerOrder.getId() == null) {
            return LocalizedR.failed("tms.the.order.does.not.exist", "");
        }
        Integer status = customerOrder.getOrderStatus();
        if (!NewOrderStatus.AWAITING_PICKUP.getCode().equals(status) &&
                !NewOrderStatus.AWAITING_SHIPMENT.getCode().equals(status)) {
            return LocalizedR.failed("tms.already.has.routing.information", "");
        }
        if (customerOrder.getCargoInfoEntityList() != null && !customerOrder.getCargoInfoEntityList().isEmpty()) {
            // 删除原有的货物信息
            cargoInfoService.remove(new LambdaQueryWrapper<TmsCargoInfoEntity>()
                    .eq(TmsCargoInfoEntity::getCustomerOrderNumber, customerOrder.getCustomerOrderNumber()));
        }

        //删除单号信息
        deleteSubOrder(customerOrder.getCustomerOrderNumber());
        LambdaUpdateWrapper<TmsCustomerOrderEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNo)
                .set(TmsCustomerOrderEntity::getDelFlag, "1");
        customerOrderMapper.update(null, updateWrapper);
        return R.ok();
    }

    //拦截订单接口
    @Override
    public R blockOrder(String orderNo) {
        //查询订单信息
        TmsCustomerOrderEntity customerOrder = getByCustomerOrderNumber(orderNo, true);
        if (customerOrder == null) {
            return LocalizedR.failed("tms.the.order.does.not.exist", "");
        }
        LambdaUpdateWrapper<TmsCustomerOrderEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(TmsCustomerOrderEntity::getCustomerOrderNumber, orderNo)
                .set(TmsCustomerOrderEntity::getOrderStatus, CustomerOrderStatus.BLOCK.getCode());
        customerOrderMapper.update(null, wrapper);
        return R.ok();
    }


    //查询订单是否被拦截
    @Override
    public Boolean isBlock(String orderNo) {
        //查询订单信息
        TmsCustomerOrderEntity customerOrder = getByCustomerOrderNumber(orderNo, true);
        if (customerOrder == null) {
            return false;
        }
        return customerOrder.getOrderStatus().equals(CustomerOrderStatus.BLOCK.getCode());

    }


    //获取订单轨迹
    @Override
    public R track(String orderNo) {
        if (StrUtil.isBlank(orderNo)) {
            return R.failed("单号不能为空");
        }

        // 查询主单信息
        TmsCustomerOrderEntity customerOrder = customerOrderMapper.selectOne(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                        .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNo)
                , false);

        // 如果委托单号没查到，按客户单号查
        if (customerOrder == null) {
            customerOrder = customerOrderMapper.selectOne(
                    new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                            .eq(TmsCustomerOrderEntity::getCustomerOrderNumber, orderNo),
                    false
            );
        }

        if (customerOrder == null) {
            return R.failed("订单不存在！");
        }

        // 检查客户token，如果匹配特定值则返回特定格式
/*        if (customerOrder.getCustomerId() != null) {
            TmsCustomerEntity customer = tmsCustomerService.getById(customerOrder.getCustomerId());
            if (customer != null && "g7Xc9P2vLqM1WdTf84eZnKyB".equals(customer.getToken())) {
                return buildSpecialTrackResponse(orderNo, customerOrder);
            }
        }*/

        // 查询子单列表
        List<TmsCustomerOrderEntity> subOrders = customerOrderMapper.selectList(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, customerOrder.getEntrustedOrderNumber())
                .eq(TmsCustomerOrderEntity::getSubFlag, true));

        if (CollUtil.isEmpty(subOrders)) {
            return R.ok(Collections.emptyMap());
        }

        // 拿到子单号
        List<String> subOrderNos = subOrders.stream()
                .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                .collect(Collectors.toList());

        // 查询本系统子单轨迹
        List<TmsOrderTrackEntity> orderTrackList = orderTrackMapper.selectList(
                new LambdaQueryWrapper<TmsOrderTrackEntity>()
                        .in(TmsOrderTrackEntity::getOrderNo, subOrderNos)
                        .orderByDesc(TmsOrderTrackEntity::getAddTime) // 按轨迹时间倒序排序
        );

        // 将本系统轨迹按子单号分组
        Map<String, List<TmsOrderTrackEntity>> localTrackMap = orderTrackList.stream()
                .collect(Collectors.groupingBy(TmsOrderTrackEntity::getOrderNo));

        // 最终返回的轨迹数据
        Map<String, List<TmsApiOrderTaskDto>> finalTrackMap = new LinkedHashMap<>();

        // 遍历每个子单，分别判断轨迹数量
        for (String subOrderNo : subOrderNos) {
            List<TmsOrderTrackEntity> localTracks = localTrackMap.getOrDefault(subOrderNo, Collections.emptyList());
            int localTrackCount = localTracks.size();

            // 如果该子单本系统轨迹数量 < 2条，尝试查询小包系统轨迹
            if (localTrackCount < 2) {
                Map<String, List<TmsApiOrderTaskDto>> smallPackageTrackMap = querySmallPackageTrackForTrackMethod(Collections.singletonList(subOrderNo));

                // 检查该子单的小包系统轨迹数量
                List<TmsApiOrderTaskDto> smallPackageTracks = smallPackageTrackMap.get(subOrderNo);
                int smallPackageTrackCount = CollUtil.isEmpty(smallPackageTracks) ? 0 : smallPackageTracks.size();

                // 如果小包系统该子单轨迹数量 >= 2条，使用小包系统轨迹
                if (smallPackageTrackCount >= 2) {
                    finalTrackMap.put(subOrderNo, smallPackageTracks);
                } else {
                    // 否则使用本系统轨迹（即使<2条）
                    List<TmsApiOrderTaskDto> localTrackDtos = localTracks.stream()
                            .map(entity -> {
                                TmsApiOrderTaskDto dto = new TmsApiOrderTaskDto();
                                BeanUtils.copyProperties(entity, dto);
                                dto.setLocationDescription(entity.getExternalDescription());
                                dto.setZip(entity.getPostalCode());
                                dto.setPathCode(entity.getStatusCode());
                                return dto;
                            })
                            .collect(Collectors.toList());
                    finalTrackMap.put(subOrderNo, localTrackDtos);
                }
            } else {
                // 本系统轨迹数量 >= 2条，直接使用本系统轨迹
                List<TmsApiOrderTaskDto> localTrackDtos = localTracks.stream()
                        .map(entity -> {
                            TmsApiOrderTaskDto dto = new TmsApiOrderTaskDto();
                            BeanUtils.copyProperties(entity, dto);
                            dto.setLocationDescription(entity.getExternalDescription());
                            dto.setZip(entity.getPostalCode());
                            dto.setPathCode(entity.getStatusCode());
                            return dto;
                        })
                        .collect(Collectors.toList());
                finalTrackMap.put(subOrderNo, localTrackDtos);
            }
        }

        return R.ok(finalTrackMap);
    }

    /**
     * 卡派客户订单导出
     *
     * @param vo
     * @param ids
     * @return
     */
    @Override
    public List<TmsCustomerOrderExcelVo> getExcel(TmsCustomerOrderPageVo vo, Long[] ids) {
        MPJLambdaWrapper wrapper = getWrapper(vo, ids, true);
        return customerOrderMapper.selectJoinList(TmsCustomerOrderExcelVo.class, wrapper);
    }

    /**
     * 中大件客户订单导出
     *
     * @param vo
     * @param ids
     * @return
     */
    @Override
    public List<TmsCustomerZdjOrderExcelVo> getZdjExcel(TmsCustomerOrderPageVo vo, Long[] ids) {
        MPJLambdaWrapper wrapper = getWrapper(vo, ids, false);
        List<TmsCustomerZdjOrderExcelVo> list = customerOrderMapper.selectJoinList(TmsCustomerZdjOrderExcelVo.class, wrapper);
        //过滤
        for (TmsCustomerZdjOrderExcelVo tmsCustomerZdjOrderExcelVo : list) {
            String customerOrderNumber = tmsCustomerZdjOrderExcelVo.getCustomerOrderNumber();
            if (StringUtils.startsWith(customerOrderNumber, "KH")) {
                tmsCustomerZdjOrderExcelVo.setCustomerOrderNumber("");
            }

        }
        return list;

    }

    @Override
    public Page<TmsCustomerOrderPageVo> clientSearch(Page page, TmsCustomerOrderPageVo orderPageVo, Boolean kpFlag) {
        TmsCustomerEntity customer = tmsCustomerService.getOne(new LambdaQueryWrapper<TmsCustomerEntity>()
                .eq(TmsCustomerEntity::getUserId, SecurityUtils.getUser().getId()));
        if (ObjectUtil.isNull(customer)) {
            return null;
        }
        //设置当前客户的id
        orderPageVo.setCustomerId(customer.getId());
        MPJLambdaWrapper wrapper = getClientWrapper(orderPageVo, null, kpFlag);
        Page newPage = customerOrderMapper.selectJoinPage(page, TmsCustomerOrderPageVo.class, wrapper);
        List<TmsCustomerOrderPageVo> records = newPage.getRecords();
        //如果数据中的customerOrderNumber是以KH开头，则设置为空
        for (TmsCustomerOrderPageVo record : records) {
            String customerOrderNumber = record.getCustomerOrderNumber();
            if (StringUtils.startsWith(customerOrderNumber, "KH")) {
                record.setCustomerOrderNumber(null);
            }
        }
        return newPage.setRecords(records);
    }

    @Override
    public List<TmsCustomerOrderExcelVo> getClientExcel(TmsCustomerOrderPageVo vo, Long[] ids) {
        TmsCustomerEntity customer = tmsCustomerService.getOne(new LambdaQueryWrapper<TmsCustomerEntity>()
                .eq(TmsCustomerEntity::getUserId, SecurityUtils.getUser().getId()));
        //设置当前客户的id
        vo.setCustomerId(customer.getId());
        MPJLambdaWrapper wrapper = getClientWrapper(vo, ids, false);
        return customerOrderMapper.selectJoinList(TmsCustomerOrderExcelVo.class, wrapper);
    }


    //根据跟踪单号获取子单号
    @Override
    public List<TmsCustomerOrderEntity> getSubOrderNoByTrackNo(String trackNo) {
        LambdaQueryWrapper<TmsCustomerOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(TmsCustomerOrderEntity::getEntrustedOrderNumber, trackNo)
                .eq(TmsCustomerOrderEntity::getSubFlag, true);
        return customerOrderMapper.selectList(wrapper);
    }


    //删除订单
    @Override
    @Transactional
    public R updateCustomerOrderByIds(Long[] ids) {
        //获取订单信息
        List<TmsCustomerOrderEntity> customerOrderList = customerOrderMapper.selectList(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .in(TmsCustomerOrderEntity::getId, Arrays.asList(ids)));
        for (TmsCustomerOrderEntity customerOrder : customerOrderList) {
            if (customerOrder == null || customerOrder.getId() == null) {
                return LocalizedR.failed("tms.the.order.does.not.exist", "");
            }
            Integer status = customerOrder.getOrderStatus();
            if (!NewOrderStatus.AWAITING_PICKUP.getCode().equals(status) &&
                    !NewOrderStatus.AWAITING_SHIPMENT.getCode().equals(status)) {
                return LocalizedR.failed("tms.already.has.routing.information", "");
            }
            // 删除原有的货物信息
            cargoInfoService.remove(new LambdaQueryWrapper<TmsCargoInfoEntity>()
                    .eq(TmsCargoInfoEntity::getCustomerOrderNumber, customerOrder.getCustomerOrderNumber()));
            //删除单号信息
            deleteSubOrder(customerOrder.getCustomerOrderNumber());
            LambdaUpdateWrapper<TmsCustomerOrderEntity> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, customerOrder.getEntrustedOrderNumber())
                    .set(TmsCustomerOrderEntity::getDelFlag, "1");
            customerOrderMapper.update(null, updateWrapper);
        }
        return R.ok();
    }

    // 揽收指派列表(排除送货到仓且发货地三字邮编对应一级仓情况，不可揽收)
    @Override
    public Page<TmsCustomerOrderPageVo> listCollectionAssign(Page page, TmsCustomerOrderPageVo vo) {
        MPJLambdaWrapper<TmsCustomerOrderEntity> wrapper = new MPJLambdaWrapper<>();

        // 选择分组字段
        wrapper.selectAs(TmsCustomerEntity::getCustomerNameCn, TmsCustomerOrderPageVo.Fields.entrustedCustomer)
                .selectAs(TmsSiteEntity::getSiteName, TmsCustomerOrderPageVo.Fields.warehouseName)
                .selectAs(TmsCustomerEntity::getId, TmsCustomerOrderEntity::getCustomerId)
                .selectAs(TmsSiteEntity::getId, TmsCustomerOrderEntity::getCollectWarehouseId)
                .select(TmsCustomerOrderEntity::getTransportType)
                .select(TmsCustomerOrderEntity::getCargoType)
                .select(TmsCustomerOrderEntity::getOrderType)


                // 汇总订单数量
                .selectCount(TmsCustomerOrderEntity::getId, TmsCustomerOrderPageVo.Fields.orderCount)
                // 汇总订单货物数量
                .selectSum(TmsCustomerOrderEntity::getCargoQuantity, TmsCustomerOrderPageVo.Fields.totalCargoQuantity)
                // 已揽数量
                .select("SUM(CASE WHEN t.order_status >= 3 THEN 1 ELSE 0 END) AS collectedCount")
                // 未揽数量
                .select("SUM(CASE WHEN t.order_status < 3 THEN 1 ELSE 0 END) AS unCollectedCount")
                // 汇总总体积
                .selectSum(TmsCustomerOrderEntity::getTotalWeight, TmsCustomerOrderPageVo.Fields.collectedTotalWeight)

                // 关联表
                .leftJoin(TmsCustomerEntity.class, TmsCustomerEntity::getId, TmsCustomerOrderEntity::getCustomerId)
                .leftJoin(TmsSiteEntity.class, TmsSiteEntity::getId, TmsCustomerOrderEntity::getCollectWarehouseId)

                // where 条件
                .eq(ObjectUtil.isNotNull(vo.getCollectWarehouseId()), TmsCustomerOrderEntity::getCollectWarehouseId, vo.getCollectWarehouseId())   // 揽收仓库
                .eq(ObjectUtil.isNotNull(vo.getCustomerId()), TmsCustomerEntity::getId, vo.getCustomerId())    // 客户信息
                .between(ObjectUtil.isNotNull(vo.getBeginTime()) && ObjectUtil.isNotNull(vo.getEndTime()),     // 创建时间
                        TmsCustomerOrderEntity::getCreateTime, vo.getBeginTime(), vo.getEndTime())
                .eq(TmsCustomerOrderEntity::getSubFlag, false)
                .ne(TmsCustomerOrderEntity::getWarehouseGrade, 1)
                .isNull(TmsCustomerOrderEntity::getPTaskOrder)

                // 按客户、仓库、运输类型分组
                .groupBy("t1.id,t2.id,t.transport_type,t.cargo_type,t.order_type");

        return customerOrderMapper.selectJoinPage(page, TmsCustomerOrderPageVo.class, wrapper);
    }


    //导入Excel处理
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R processFile(MultipartFile file, Long customerId) throws IOException {
        // 验证文件类型和空文件
        if (file == null || file.isEmpty() || !isExcelFile(file.getOriginalFilename())) {
            return LocalizedR.failed("order.upload.valid.file", Optional.ofNullable(null));
        }
        try (InputStream inputStream = file.getInputStream()) {
            ExcelReader reader = ExcelUtil.getReader(inputStream);
            // 跳过表头，确保从第二行开始读取
            List<List<Object>> lines = reader.read(1, reader.getRowCount());
            // 验证Excel数据是否为空
            if (lines == null || lines.isEmpty()) {
                return LocalizedR.failed("order.upload.empty.data", Optional.ofNullable(null));
            }
            int size = lines.size();
            if (size < 1) {
                return LocalizedR.failed("order.upload.empty.data", Optional.ofNullable(null));
            }
            //判断模板是否正确
            if (!LineValidator.isValid(lines)) {
                return LocalizedR.failed("tms.imported.template.is.incorrect", Optional.ofNullable(null));
            }

            // 创建集合来记录所有问题的字段
            List<String> massages = new ArrayList<>();
            for (int i = 1; i < lines.size(); i++) {
                List<Object> line = lines.get(i);
                for (int j = 0; j < line.size(); j++) {
                    int rowIndex = i + 2;
                    List<String> strings = LineValidator.validateLineData(line, rowIndex);
                    massages.addAll(strings);
                    if (j == 0) {
                        if (isValidAddress(line.get(8)) && isValidAddress(line.get(15))) {
                            //校验地址是否正确
                            String[] origins = line.get(8).toString().split("/");
                            String[] destinations = line.get(15).toString().split("/");
                            StatesEntity provinceByName1 = statesService.getProvinceByName(origins[1]);
                            CitiesEntity cityByName1 = citiesService.getCityByName(origins[2]);
                            if (provinceByName1 == null || cityByName1 == null) {
                                massages.add("第" + rowIndex + "行 始发地不存在");
                            }

                            StatesEntity provinceByName2 = statesService.getProvinceByName(destinations[1]);
                            CitiesEntity cityByName2 = citiesService.getCityByName(destinations[2]);

                            if (provinceByName2 == null || cityByName2 == null) {
                                massages.add("第" + rowIndex + "行 目的地不存在");
                            }
                        }
                        //判断客户单号是否存在
                        TmsCustomerOrderEntity customerOrder = getByCustomerOrderNumber(line.get(0) == null ? "" : line.get(0).toString(), true);
                        if (customerOrder.getId() != null) {
                            massages.add(line.get(0) + "客户单号已存在");
                        }
                        //判断邮编是否存在
                        if (StringUtils.isBlank(tmsOverAreaService.getRouteNo(line.get(10) == null ? "" : line.get(9).toString()))) {
                            massages.add("第" + rowIndex + "行始发地邮编未覆盖");
                        }
                        if (StringUtils.isBlank(tmsOverAreaService.getRouteNo(line.get(16) == null ? "" : line.get(15).toString()))) {
                            massages.add("第" + rowIndex + "目的地邮编未覆盖");
                        }
                    }

                }

            }
            if (massages.size() > 0) {
                return R.failed(OrderTools.check(massages));
            }

            //校验完毕，开始处理数据
            for (int i = 1; i < lines.size(); i++) {
                List<Object> line = lines.get(i);
                Object customerNo = line.get(0);
                Object transportationType = line.get(1);
                Object orderType = line.get(2);
                Object cargoType = line.get(3);
                Object businessType = line.get(4);
                Object receiveType = line.get(5);
                Object f_contact = line.get(6);
                Object f_contactPhone = line.get(7);
                Object origin = line.get(8);
                Object f_Zip = line.get(9);
                Object f_detailedAddress = line.get(10);
                Object estimatedShippingTime = line.get(11);
                Object s_contact = line.get(12);
                Object s_contactPhone = line.get(13);
                Object destination = line.get(14);
                Object s_zip = line.get(15);
                Object s_detailedAddress = line.get(16);
                Object length = line.get(17);
                Object width = line.get(18);
                Object height = line.get(19);
                Object wight = line.get(20);
                Object note = "";
                try {
                    note = line.get(21);
                } catch (Exception e) {
                    note = "";
                }
                TmsZdjCustomerOrderVo orderVo = new TmsZdjCustomerOrderVo();
                orderVo.setCustomerOrderNumber(customerNo.toString());
                orderVo.setTransportType("整车运输".equals(transportationType.toString()) ? 1 : 2);
                orderVo.setOrderType("托盘".equals(orderType.toString()) ? 1 : 2);
                orderVo.setCargoType("普通货物".equals(cargoType.toString()) ? 1 : 2);
                if (businessType.equals("揽收")) {
                    orderVo.setBusinessModel(1);
                } else if (businessType.equals("中大件")) {
                    orderVo.setBusinessModel(2);
                } else {
                    orderVo.setBusinessModel(3);
                }
                orderVo.setReceiveType("上门揽收".equals(receiveType.toString()) ? 1 : 2);
                orderVo.setShipperName(f_contact.toString());
                orderVo.setShipperPhone(f_contactPhone.toString());
                orderVo.setOrigin(origin.toString());
                orderVo.setShipperPostalCode(f_Zip.toString());
                orderVo.setShipperAddress(f_detailedAddress.toString());
                orderVo.setEstimatedShippingTimeStart(DateUtil.parse(estimatedShippingTime.toString()).toLocalDateTime());
                orderVo.setEstimatedShippingTimeEnd(DateUtil.parse(estimatedShippingTime.toString()).toLocalDateTime());
                orderVo.setReceiverName(s_contact.toString());
                orderVo.setReceiverPhone(s_contactPhone.toString());
                orderVo.setDestination(destination.toString());
                orderVo.setDestPostalCode(s_zip.toString());
                orderVo.setDestAddress(s_detailedAddress.toString());
                orderVo.setTotalWeight(wight.toString());
                orderVo.setTotalVolume(length.toString() + width.toString() + height.toString());
                orderVo.setRemark(note == null ? null : note.toString());

                // 始发地和目的地不能为同一个城市
                if (orderVo.getOrigin() != null && orderVo.getDestination() != null) {
                    if ((orderVo.getOrigin() + orderVo.getShipperAddress()).equals(orderVo.getDestination() + orderVo.getDestAddress())) {
                        return R.failed("客户单号" + orderVo.getCustomerOrderNumber() + "始发地和目的地地址不能相同");
                    }
                }

                //设置货物信息
                List<TmsCargoInfoEntity> cargoInfoEntityList = new ArrayList<>();
                TmsCargoInfoEntity tmsCargoInfoEntity = new TmsCargoInfoEntity();
                tmsCargoInfoEntity.setLength(new BigDecimal(length.toString()));
                tmsCargoInfoEntity.setWidth(new BigDecimal(width.toString()));
                tmsCargoInfoEntity.setHeight(new BigDecimal(height.toString()));
                tmsCargoInfoEntity.setWeight(new BigDecimal(wight.toString()));
                cargoInfoEntityList.add(tmsCargoInfoEntity);
                //判断下一条数据行客户单号是否和当前一致，如果一致则获取货物信息
                int currentIndex = i + 1;
                while (currentIndex < lines.size()) {
                    List<Object> nextLine = lines.get(currentIndex);
                    Object next_customerNo = nextLine.get(0);
                    if (Objects.equals(next_customerNo, customerNo)) {
                        // 相同客户单号，处理当前行数据
                        Object next_length = nextLine.get(17);
                        Object next_width = nextLine.get(18);
                        Object next_height = nextLine.get(19);
                        Object next_wight = nextLine.get(20);

                        TmsCargoInfoEntity next_tmsCargoInfoEntity = new TmsCargoInfoEntity();
                        next_tmsCargoInfoEntity.setLength(new BigDecimal(next_length.toString()));
                        next_tmsCargoInfoEntity.setWidth(new BigDecimal(next_width.toString()));
                        next_tmsCargoInfoEntity.setHeight(new BigDecimal(next_height.toString()));
                        next_tmsCargoInfoEntity.setWeight(new BigDecimal(next_wight.toString()));
                        cargoInfoEntityList.add(next_tmsCargoInfoEntity);
                        currentIndex++;
                    } else {
                        break;
                    }
                }
                orderVo.setCargoInfoEntityList(cargoInfoEntityList);
                TmsCustomerOrderEntity tmsCustomerOrderEntity = new TmsCustomerOrderEntity();
                BeanUtils.copyProperties(orderVo, tmsCustomerOrderEntity);
                if (customerId > 1000) {
                    tmsCustomerOrderEntity.setCustomerId(getCustomerId(customerId));
                } else {
                    tmsCustomerOrderEntity.setCustomerId(customerId);
                }
                if (tmsCustomerOrderEntity.getCustomerId() == null) {
                    return R.failed("旧账号请联系管理员关联系统用户");
                }
                TmsCustomerEntity customer = tmsCustomerService.getById(customerId);
                tmsCustomerOrderEntity.setOperator(customer == null ? null : customer.getCustomerName());
                R createResult = create(tmsCustomerOrderEntity);
            }
        }
        return R.ok();
    }

    /**
     * 发送 Best Office 订单同步消息
     * 检查客户是否为 Best Office 客户（isPush 包含 "2"），如果是则发送同步消息
     *
     * @param order 订单实体
     * @param sourceType 消息来源类型（CREATE/IMPORT）
     */
    @Async
    public void sendBestOfficeOrderSyncMessage(TmsCustomerOrderEntity order, String sourceType) {
        if (order.getEntrustedOrderNumber().length()>15){
            order.setEntrustedOrderNumber(order.getEntrustedOrderNumber().substring(0, order.getEntrustedOrderNumber().length()-3));
        }

        if (order == null || order.getCustomerId() == null) {
            return;
        }

        try {
            // 查询客户信息
            TmsCustomerEntity customer = customerMapper.selectById(order.getCustomerId());
            if (customer == null) {
                log.warn("客户不存在，跳过 Best Office 订单同步消息发送 - 客户ID: {}, 订单号: {}", order.getCustomerId(), order.getEntrustedOrderNumber());
                return;
            }

            // 检查是否为 Best Office 客户（isPush 字段包含 "2"）
            String isPush = customer.getIsPush();
            if (StrUtil.isBlank(isPush) || !isPush.contains("2")) {
                log.debug("非 Best Office 客户，跳过订单同步消息发送 - 客户: {}, isPush: {}, 订单号: {}", customer.getCustomerName(), isPush, order.getEntrustedOrderNumber());
                return;
            }

            // 发送同步消息
//            if ("CREATE".equals(sourceType)) {
//                bestOfficeOrderSyncProducer.sendOrderCreateMessage(order, customer.getCustomerName());
//            } else if ("IMPORT".equals(sourceType)) {
//                bestOfficeOrderSyncProducer.sendOrderImportMessage(order, customer.getCustomerName());
//            }

            log.info("Best Office 订单同步消息发送成功 - 客户: {}, 订单号: {}, 来源: {}",
                    customer.getCustomerName(), order.getEntrustedOrderNumber(), sourceType);

        } catch (Exception e) {
            log.error("发送 Best Office 订单同步消息异常 - 订单号: {}, 来源: {}, 错误: {}",
                    order.getEntrustedOrderNumber(), sourceType, e.getMessage(), e);
            throw e; // 重新抛出异常，让调用方决定如何处理
        }
    }


    //获取用户id
    public Long getCustomerId(Long userId) {
        LambdaQueryWrapper<TmsCustomerEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(TmsCustomerEntity::getId)
                .eq(TmsCustomerEntity::getUserId, userId)
                .last("limit 1");
        TmsCustomerEntity customer = customerMapper.selectOne(wrapper);
        return customer == null ? null : customer.getId();
    }

    public boolean isValidAddress(Object obj) {
        if (obj == null) return false;
        String str = obj.toString().trim();
        // 是否以 "Canada" 开头
        if (!str.startsWith("Canada")) {
            return false;
        }
        // 检查是否包含两个斜杠（不止一个）
        long slashCount = str.chars().filter(ch -> ch == '/').count();
        return slashCount == 2;
    }


    // 校验文件是否是.xls或.xlsx格式
    private boolean isExcelFile(String fileName) {
        return fileName != null && (fileName.endsWith(".xls") || fileName.endsWith(".xlsx"));
    }

    // 封装分页、导出查询条件
    private MPJLambdaWrapper getWrapper(TmsCustomerOrderPageVo orderPageVo, Long[] ids, Boolean kpFlag) {
        //判断是否批量查询
        boolean batchOriginFlag = OrderTools.isBatchConditions(orderPageVo.getOrigin());
        boolean batchDestinationFlag = OrderTools.isBatchConditions(orderPageVo.getDestination());
        boolean batchOrderStatusFlag = OrderTools.isBatchConditions(orderPageVo.getStrOrderStatus());
        boolean batchCustomerOrderFlag = OrderTools.isBatchConditions(orderPageVo.getCustomerOrderNumber());
        boolean batchEntrustedOrderFlag = OrderTools.isBatchEntrustedOrder(orderPageVo.getEntrustedOrderNumber());
        MPJLambdaWrapper<TmsCustomerOrderEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.like(StrUtil.isNotBlank(orderPageVo.getCustomerOrderNumber()) && !batchCustomerOrderFlag, TmsCustomerOrderEntity::getCustomerOrderNumber, orderPageVo.getCustomerOrderNumber())
                .like(StrUtil.isNotBlank(orderPageVo.getEntrustedOrderNumber()) && !batchEntrustedOrderFlag, TmsCustomerOrderEntity::getEntrustedOrderNumber, OrderTools.processEntrustedOrderNumber(orderPageVo.getEntrustedOrderNumber()))
                .eq(ObjectUtil.isNotNull(orderPageVo.getCustomerId()), TmsCustomerOrderEntity::getCustomerId, orderPageVo.getCustomerId())
                .selectAll(TmsCustomerOrderEntity.class)
                .selectAs(TmsCustomerEntity::getCustomerName, TmsCustomerOrderPageVo.Fields.entrustedCustomer)
                .selectAs(TmsCustomerEntity::getIsPush, TmsCustomerOrderPageVo.Fields.customerIsPush)
                .leftJoin(TmsCustomerEntity.class, TmsCustomerEntity::getId, TmsCustomerOrderEntity::getCustomerId)
                .like(StrUtil.isNotBlank(orderPageVo.getOrigin()) && !batchOriginFlag, TmsCustomerOrderEntity::getOrigin, orderPageVo.getOrigin())
                .like(StrUtil.isNotBlank(orderPageVo.getDestination()) && !batchDestinationFlag, TmsCustomerOrderEntity::getDestination, orderPageVo.getDestination())
                .eq(ObjectUtil.isNotNull(orderPageVo.getReceiveType()), TmsCustomerOrderEntity::getReceiveType, orderPageVo.getReceiveType())
                .eq(StrUtil.isNotBlank(orderPageVo.getStrOrderStatus()) && !batchOrderStatusFlag, TmsCustomerOrderEntity::getOrderStatus, orderPageVo.getStrOrderStatus())
                .eq(ObjectUtil.isNotNull(orderPageVo.getOrderType()), TmsCustomerOrderEntity::getOrderType, orderPageVo.getOrderType())     // 订单类型：1=托盘，2=包裹
                .like(StrUtil.isNotBlank(orderPageVo.getJyOrderNo()), TmsCustomerOrderEntity::getJyOrderNo, orderPageVo.getJyOrderNo())
                .eq(TmsCustomerOrderEntity::getDelFlag, "0")
                .and(w -> w.eq(TmsCustomerOrderEntity::getSubFlag, false).or().isNull(TmsCustomerOrderEntity::getSubFlag))

                // 业务模式：1 揽收，2 中大件，3 卡派
                // 下单时间、发货时间、到货时间 根据范围搜索
                .between(ObjectUtil.isNotNull(orderPageVo.getBeginTime()) && ObjectUtil.isNotNull(orderPageVo.getEndTime()),
                        TmsCustomerOrderEntity::getCreateTime, orderPageVo.getBeginTime(), orderPageVo.getEndTime())
                //预计发货时间开始 预计发货时间结束
                .between(ObjectUtil.isNotNull(orderPageVo.getShippingBeginTime()) && ObjectUtil.isNotNull(orderPageVo.getShippingEndTime()),
                        TmsCustomerOrderEntity::getEstimatedShippingTimeStart, orderPageVo.getShippingBeginTime(), orderPageVo.getShippingEndTime())
                //预计到货时间开始 预计到货时间结束
                .between(ObjectUtil.isNotNull(orderPageVo.getArrivalBeginTime()) && ObjectUtil.isNotNull(orderPageVo.getArrivalEndTime()),
                        TmsCustomerOrderEntity::getEstimatedArrivalTimeStart, orderPageVo.getArrivalBeginTime(), orderPageVo.getArrivalEndTime())
                //完成时间开始，完成时间结束
                .between(ObjectUtil.isNotNull(orderPageVo.getFinishBeginTime()) && ObjectUtil.isNotNull(orderPageVo.getFinishEndTime()),
                        TmsCustomerOrderEntity::getFinishTime, orderPageVo.getFinishBeginTime(), orderPageVo.getFinishEndTime());

        // 处理联系人：shipper 或 receiver
        if (StrUtil.isNotBlank(orderPageVo.getContacts())) {
            wrapper.and(w -> w.like(TmsCustomerOrderEntity::getShipperName, orderPageVo.getContacts())
                    .or()
                    .like(TmsCustomerOrderEntity::getReceiverName, orderPageVo.getContacts()));
        }
        //系统单号是否批量查询
        if (batchEntrustedOrderFlag) {
            wrapper.in(TmsCustomerOrderEntity::getEntrustedOrderNumber, Arrays.asList(orderPageVo.getEntrustedOrderNumber().split("(?=N)")));
        }
        //客户单号是否批量查询
        if (batchCustomerOrderFlag) {
            wrapper.in(TmsCustomerOrderEntity::getCustomerOrderNumber, Arrays.asList(orderPageVo.getCustomerOrderNumber().split(",")));
        }
        //订单状态批量查询
        if (batchOrderStatusFlag) {
            wrapper.in(TmsCustomerOrderEntity::getOrderStatus, Arrays.asList(orderPageVo.getStrOrderStatus().split(",")));
        }
        //始发地批量查询
        if (batchOriginFlag) {
            wrapper.in(TmsCustomerOrderEntity::getOrigin, Arrays.asList(orderPageVo.getOrigin().split(",")));
        }
        //目的地批量查询
        if (batchDestinationFlag) {
            wrapper.in(TmsCustomerOrderEntity::getDestination, Arrays.asList(orderPageVo.getDestination().split(",")));
        }

        // 处理联系人电话：shipperPhone 或 receiverPhone
        if (StrUtil.isNotBlank(orderPageVo.getContactPhone())) {
            wrapper.and(w -> w.like(TmsCustomerOrderEntity::getShipperPhone, orderPageVo.getContactPhone())
                    .or()
                    .like(TmsCustomerOrderEntity::getReceiverPhone, orderPageVo.getContactPhone()));
        }
        wrapper.orderByDesc(TmsCustomerOrderEntity::getCreateTime)
                .in(ObjectUtil.isNotNull(ids) && ids.length > 0, TmsCustomerOrderEntity::getId, ids);   // 导出用ID
        if (ObjectUtil.isNull(orderPageVo.getBusinessModel())) {
            if (kpFlag) {
                wrapper.eq(TmsCustomerOrderEntity::getBusinessModel, 3);
            } else {
                wrapper.in(TmsCustomerOrderEntity::getBusinessModel, Arrays.asList(1, 2));
            }
        } else {
            wrapper.eq(TmsCustomerOrderEntity::getBusinessModel, orderPageVo.getBusinessModel());
        }

        //添加客户端过滤
        TmsCustomerEntity customer = customerMapper.selectOne(new LambdaQueryWrapper<TmsCustomerEntity>()
                .eq(TmsCustomerEntity::getUserId, SecurityUtils.getUser().getId()));
        if (ObjectUtil.isNotNull(customer)) {
            wrapper.eq(TmsCustomerOrderEntity::getCustomerId, customer.getId());
        }

        return wrapper;
    }

    // 封装客户端查询条件
    private MPJLambdaWrapper getClientListWrapper(TmsCustomerOrderPageVo orderPageVo, Long[] ids) {
        MPJLambdaWrapper<TmsCustomerOrderEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.like(StrUtil.isNotBlank(orderPageVo.getCustomerOrderNumber()), TmsCustomerOrderEntity::getCustomerOrderNumber, orderPageVo.getCustomerOrderNumber())
                .like(StrUtil.isNotBlank(orderPageVo.getEntrustedOrderNumber()), TmsCustomerOrderEntity::getEntrustedOrderNumber, orderPageVo.getEntrustedOrderNumber())
                .eq(TmsCustomerOrderEntity::getCustomerId, orderPageVo.getCustomerId())
                .selectAll(TmsCustomerOrderEntity.class)
                .selectAs(TmsCustomerEntity::getCustomerName, TmsCustomerOrderPageVo.Fields.entrustedCustomer)
                .leftJoin(TmsCustomerEntity.class, TmsCustomerEntity::getId, TmsCustomerOrderEntity::getCustomerId)
                .like(StrUtil.isNotBlank(orderPageVo.getOrigin()), TmsCustomerOrderEntity::getOrigin, orderPageVo.getOrigin())
                .like(StrUtil.isNotBlank(orderPageVo.getDestination()), TmsCustomerOrderEntity::getDestination, orderPageVo.getDestination())
                .eq(ObjectUtil.isNotNull(orderPageVo.getOrderStatus()), TmsCustomerOrderEntity::getOrderStatus, orderPageVo.getOrderStatus())   // 客户订单状态
                .eq(ObjectUtil.isNotNull(orderPageVo.getOrderType()), TmsCustomerOrderEntity::getOrderType, orderPageVo.getOrderType())     // 订单类型：1=托盘，2=包裹
                .eq(ObjectUtil.isNotNull(orderPageVo.getBusinessModel()), TmsCustomerOrderEntity::getBusinessModel, orderPageVo.getBusinessModel()) // 业务模式：1 揽收，2 中大件，3 卡派
                // 下单时间、发货时间、到货时间 根据范围搜索
                .between(ObjectUtil.isNotNull(orderPageVo.getBeginTime()) && ObjectUtil.isNotNull(orderPageVo.getEndTime()),            // 搜索框 下单时间
                        TmsCustomerOrderEntity::getCreateTime, orderPageVo.getBeginTime(), orderPageVo.getEndTime())
                //预计发货时间开始 预计发货时间结束
                .between(ObjectUtil.isNotNull(orderPageVo.getEstimatedShippingTimeStart()) && ObjectUtil.isNotNull(orderPageVo.getEstimatedShippingTimeEnd()),
                        TmsCustomerOrderEntity::getEstimatedShippingTimeStart, orderPageVo.getEstimatedShippingTimeStart(), orderPageVo.getEstimatedShippingTimeEnd())
                //预计到货时间开始 预计到货时间结束
                .between(ObjectUtil.isNotNull(orderPageVo.getEstimatedArrivalTimeStart()) && ObjectUtil.isNotNull(orderPageVo.getEstimatedArrivalTimeEnd()),
                        TmsCustomerOrderEntity::getEstimatedArrivalTimeStart, orderPageVo.getEstimatedArrivalTimeStart(), orderPageVo.getEstimatedArrivalTimeEnd());
        // 处理联系人：shipper 或 receiver
        if (StrUtil.isNotBlank(orderPageVo.getContacts())) {
            wrapper.and(w -> w.like(TmsCustomerOrderEntity::getShipperName, orderPageVo.getContacts())
                    .or()
                    .like(TmsCustomerOrderEntity::getReceiverName, orderPageVo.getContacts()));
        }

        // 处理联系人电话：shipperPhone 或 receiverPhone
        if (StrUtil.isNotBlank(orderPageVo.getContactPhone())) {
            wrapper.and(w -> w.like(TmsCustomerOrderEntity::getShipperPhone, orderPageVo.getContactPhone())
                    .or()
                    .like(TmsCustomerOrderEntity::getReceiverPhone, orderPageVo.getContactPhone()));
        }
        wrapper.orderByDesc(TmsCustomerOrderEntity::getCreateTime)
                .in(ObjectUtil.isNotNull(ids) && ids.length > 0, TmsCustomerOrderEntity::getId, ids);   // 导出用ID
        return wrapper;
    }

    // 客户端封装分页、导出查询条件
    private MPJLambdaWrapper getClientWrapper(TmsCustomerOrderPageVo orderPageVo, Long[] ids, Boolean kpFlag) {
        //判断是否批量查询
        boolean batchOriginFlag = OrderTools.isBatchConditions(orderPageVo.getOrigin());
        boolean batchDestinationFlag = OrderTools.isBatchConditions(orderPageVo.getDestination());
        boolean batchOrderStatusFlag = OrderTools.isBatchConditions(orderPageVo.getStrOrderStatus());
        boolean batchCustomerOrderFlag = OrderTools.isBatchConditions(orderPageVo.getCustomerOrderNumber());
        boolean batchEntrustedOrderFlag = OrderTools.isBatchEntrustedOrder(orderPageVo.getEntrustedOrderNumber());
        MPJLambdaWrapper<TmsCustomerOrderEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.like(StrUtil.isNotBlank(orderPageVo.getCustomerOrderNumber()) && !batchCustomerOrderFlag, TmsCustomerOrderEntity::getCustomerOrderNumber, orderPageVo.getCustomerOrderNumber())
                .like(StrUtil.isNotBlank(orderPageVo.getEntrustedOrderNumber()) && !batchEntrustedOrderFlag, TmsCustomerOrderEntity::getEntrustedOrderNumber, OrderTools.processEntrustedOrderNumber(orderPageVo.getEntrustedOrderNumber()))
                .eq(ObjectUtil.isNotNull(orderPageVo.getCustomerId()), TmsCustomerOrderEntity::getCustomerId, orderPageVo.getCustomerId())
                .selectAll(TmsCustomerOrderEntity.class)
                .selectAs(TmsCustomerEntity::getCustomerName, TmsCustomerOrderPageVo.Fields.entrustedCustomer)
                .selectAs(TmsCustomerEntity::getIsPush, TmsCustomerOrderPageVo.Fields.customerIsPush)
                .leftJoin(TmsCustomerEntity.class, TmsCustomerEntity::getId, TmsCustomerOrderEntity::getCustomerId)
                .like(StrUtil.isNotBlank(orderPageVo.getOrigin()) && !batchOriginFlag, TmsCustomerOrderEntity::getOrigin, orderPageVo.getOrigin())
                .like(StrUtil.isNotBlank(orderPageVo.getDestination()) && !batchDestinationFlag, TmsCustomerOrderEntity::getDestination, orderPageVo.getDestination())
                .like(StrUtil.isNotBlank(orderPageVo.getJyOrderNo()), TmsCustomerOrderEntity::getJyOrderNo, orderPageVo.getJyOrderNo())
                .eq(ObjectUtil.isNotNull(orderPageVo.getReceiveType()), TmsCustomerOrderEntity::getReceiveType, orderPageVo.getReceiveType())
                .eq(StrUtil.isNotBlank(orderPageVo.getStrOrderStatus()) && !batchOrderStatusFlag, TmsCustomerOrderEntity::getOrderStatus, orderPageVo.getStrOrderStatus())
                .eq(ObjectUtil.isNotNull(orderPageVo.getOrderType()), TmsCustomerOrderEntity::getOrderType, orderPageVo.getOrderType())     // 订单类型：1=托盘，2=包裹
                .eq(TmsCustomerOrderEntity::getDelFlag, "0")
                //增加客户端Id-隔离--客户端
                .eq(ObjectUtil.isNotNull(orderPageVo.getCustomerId()), TmsCustomerOrderEntity::getCustomerId, orderPageVo.getCustomerId())
                .and(w -> w.eq(TmsCustomerOrderEntity::getSubFlag, false).or().isNull(TmsCustomerOrderEntity::getSubFlag))

                // 业务模式：1 揽收，2 中大件，3 卡派
                // 下单时间、发货时间、到货时间 根据范围搜索
                .between(ObjectUtil.isNotNull(orderPageVo.getBeginTime()) && ObjectUtil.isNotNull(orderPageVo.getEndTime()),
                        TmsCustomerOrderEntity::getCreateTime, orderPageVo.getBeginTime(), orderPageVo.getEndTime())
                //预计发货时间开始 预计发货时间结束
                .between(ObjectUtil.isNotNull(orderPageVo.getShippingBeginTime()) && ObjectUtil.isNotNull(orderPageVo.getShippingEndTime()),
                        TmsCustomerOrderEntity::getEstimatedShippingTimeStart, orderPageVo.getShippingBeginTime(), orderPageVo.getShippingEndTime())
                //预计到货时间开始 预计到货时间结束
                .between(ObjectUtil.isNotNull(orderPageVo.getArrivalBeginTime()) && ObjectUtil.isNotNull(orderPageVo.getArrivalEndTime()),
                        TmsCustomerOrderEntity::getEstimatedArrivalTimeStart, orderPageVo.getArrivalBeginTime(), orderPageVo.getArrivalEndTime());
        // 处理联系人：shipper 或 receiver
        if (StrUtil.isNotBlank(orderPageVo.getContacts())) {
            wrapper.and(w -> w.like(TmsCustomerOrderEntity::getShipperName, orderPageVo.getContacts())
                    .or()
                    .like(TmsCustomerOrderEntity::getReceiverName, orderPageVo.getContacts()));
        }
        //系统单号是否批量查询
        if (batchEntrustedOrderFlag) {
            wrapper.in(TmsCustomerOrderEntity::getEntrustedOrderNumber, Arrays.asList(orderPageVo.getEntrustedOrderNumber().split("(?=N)")));
        }
        //客户单号是否批量查询
        if (batchCustomerOrderFlag) {
            wrapper.in(TmsCustomerOrderEntity::getCustomerOrderNumber, Arrays.asList(orderPageVo.getCustomerOrderNumber().split(",")));
        }
        //订单状态批量查询
        if (batchOrderStatusFlag) {
            wrapper.in(TmsCustomerOrderEntity::getOrderStatus, Arrays.asList(orderPageVo.getStrOrderStatus().split(",")));
        }
        //始发地批量查询
        if (batchOriginFlag) {
            wrapper.in(TmsCustomerOrderEntity::getOrigin, Arrays.asList(orderPageVo.getOrigin().split(",")));
        }
        //目的地批量查询
        if (batchDestinationFlag) {
            wrapper.in(TmsCustomerOrderEntity::getDestination, Arrays.asList(orderPageVo.getDestination().split(",")));
        }

        // 处理联系人电话：shipperPhone 或 receiverPhone
        if (StrUtil.isNotBlank(orderPageVo.getContactPhone())) {
            wrapper.and(w -> w.like(TmsCustomerOrderEntity::getShipperPhone, orderPageVo.getContactPhone())
                    .or()
                    .like(TmsCustomerOrderEntity::getReceiverPhone, orderPageVo.getContactPhone()));
        }
        wrapper.orderByDesc(TmsCustomerOrderEntity::getCreateTime)
                .in(ObjectUtil.isNotNull(ids) && ids.length > 0, TmsCustomerOrderEntity::getId, ids);   // 导出用ID
        if (ObjectUtil.isNull(orderPageVo.getBusinessModel())) {
            if (kpFlag) {
                wrapper.eq(TmsCustomerOrderEntity::getBusinessModel, 3);
            } else {
                wrapper.in(TmsCustomerOrderEntity::getBusinessModel, Arrays.asList(1, 2));
            }
        } else {
            wrapper.eq(TmsCustomerOrderEntity::getBusinessModel, orderPageVo.getBusinessModel());
        }

        //添加客户端过滤
        TmsCustomerEntity customer = customerMapper.selectOne(new LambdaQueryWrapper<TmsCustomerEntity>()
                .eq(TmsCustomerEntity::getUserId, SecurityUtils.getUser().getId()));
        if (ObjectUtil.isNotNull(customer)) {
            wrapper.eq(TmsCustomerOrderEntity::getCustomerId, customer.getId());
        }

        return wrapper;
    }

    /**
     * 揽收指派-可揽收客户订单列表
     *
     * @param page
     * @param vo
     * @return
     */
    @Override
    public Page<TmsCustomerOrderPageVo> getAllPickupCustomerOrder(Page page, TmsCustomerOrderPageVo vo) {
        MPJLambdaWrapper<TmsCustomerOrderEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(TmsCustomerOrderEntity.class)
                // 客户信息
                .selectAs(TmsCustomerEntity::getCustomerName, TmsCustomerOrderPageVo.Fields.entrustedCustomer)
                // 仓库信息
                .selectAs(TmsSiteEntity::getSiteName, TmsCustomerOrderPageVo.Fields.warehouseName)
                // 条件查询-客户单号
                .like(StrUtil.isNotBlank(vo.getCustomerOrderNumber()), TmsCustomerOrderEntity::getCustomerOrderNumber, vo.getCustomerOrderNumber())
                // 跟踪单号
                .like(StrUtil.isNotBlank(vo.getEntrustedOrderNumber()), TmsCustomerOrderEntity::getEntrustedOrderNumber, vo.getEntrustedOrderNumber())
                // 条件查询-揽收任务单号
                .like(StrUtil.isNotBlank(vo.getPTaskOrder()), TmsCustomerOrderEntity::getPTaskOrder, vo.getPTaskOrder())
                // 条件查询-订单状态
                .eq(ObjectUtil.isNotNull(vo.getOrderStatus()), TmsCustomerOrderEntity::getOrderStatus, vo.getOrderStatus())
                // 条件查询-业务模式
                .eq(ObjectUtil.isNotNull(vo.getBusinessModel()), TmsCustomerOrderEntity::getBusinessModel, vo.getBusinessModel())
                // 条件查询-订单类型
                .eq(ObjectUtil.isNotNull(vo.getOrderType()), TmsCustomerOrderEntity::getOrderType, vo.getOrderType())
                // 条件查询-派送仓库id
                .eq(ObjectUtil.isNotNull(vo.getDeliveryWarehouseId()), TmsCustomerOrderEntity::getDeliveryWarehouseId, vo.getDeliveryWarehouseId())
                // 条件查询-联系人
                .and(StrUtil.isNotBlank(vo.getContacts()), w -> w.like(TmsCustomerOrderEntity::getShipperName, vo.getContacts())
                        .or().like(TmsCustomerOrderEntity::getReceiverName, vo.getContacts()))
                // 条件查询-联系方式（发货人或收货人）
                .and(StrUtil.isNotBlank(vo.getContactPhone()), w -> w.like(TmsCustomerOrderEntity::getShipperPhone, vo.getContactPhone())
                        .or().like(TmsCustomerOrderEntity::getReceiverPhone, vo.getContactPhone()))
                // 收货方式
                .eq(ObjectUtil.isNotNull(vo.getReceiveType()), TmsCustomerOrderEntity::getReceiveType, vo.getReceiveType())

                // 仓库ID
                .eq(ObjectUtil.isNotNull(vo.getCollectWarehouseId()), TmsCustomerOrderEntity::getCollectWarehouseId, vo.getCollectWarehouseId())
                // 条件查询-委托客户
                .eq(StrUtil.isNotBlank(vo.getEntrustedCustomer()), TmsCustomerEntity::getCustomerName, vo.getEntrustedCustomer())
                // 始发地
                .like(StrUtil.isNotBlank(vo.getOrigin()), TmsCustomerOrderEntity::getOrigin, vo.getOrigin())
                // 目的地
                .like(StrUtil.isNotBlank(vo.getDestination()), TmsCustomerOrderEntity::getDestination, vo.getDestination())
                .leftJoin(TmsCustomerEntity.class, TmsCustomerEntity::getId, TmsCustomerOrderEntity::getCustomerId)
                .leftJoin(TmsSiteEntity.class, TmsSiteEntity::getId, TmsCustomerOrderEntity::getCollectWarehouseId)
                .selectAs(TmsTransportTaskOrderEntity::getId, TmsCustomerOrderPageVo.Fields.taskOrderId)
                .leftJoin(TmsTransportTaskOrderEntity.class, TmsTransportTaskOrderEntity::getTaskOrderNo, TmsCustomerOrderEntity::getPTaskOrder)
                // 下单时间 根据范围搜索
                .between(ObjectUtil.isNotNull(vo.getBeginTime()) && ObjectUtil.isNotNull(vo.getEndTime()),
                        TmsCustomerOrderEntity::getCreateTime, vo.getBeginTime(), vo.getEndTime())
                // 预计发货时间开始 预计发货时间结束
                .between(ObjectUtil.isNotNull(vo.getEstimatedShippingTimeStart()) && ObjectUtil.isNotNull(vo.getEstimatedShippingTimeEnd()),
                        TmsCustomerOrderEntity::getEstimatedShippingTimeStart, vo.getEstimatedShippingTimeStart(), vo.getEstimatedShippingTimeEnd())
                // 预计到货时间开始 预计到货时间结束
                .between(ObjectUtil.isNotNull(vo.getEstimatedArrivalTimeStart()) && ObjectUtil.isNotNull(vo.getEstimatedArrivalTimeEnd()),
                        TmsCustomerOrderEntity::getEstimatedArrivalTimeStart, vo.getEstimatedArrivalTimeStart(), vo.getEstimatedArrivalTimeEnd())
                // 主单
                .eq(TmsCustomerOrderEntity::getSubFlag, false)
                // 排除送货到仓且发货地三字邮编前三位对应一级仓
                .ne(TmsCustomerOrderEntity::getWarehouseGrade, 1)
                .eq(TmsCustomerOrderEntity::getDelFlag, "0")
                .orderByDesc(TmsCustomerOrderEntity::getCreateTime);

        if (vo.getType().equals(1)) {
            // 没有揽收任务单号，则为待指派
            wrapper.isNull(TmsCustomerOrderEntity::getPTaskOrder);
        }
        if (vo.getType().equals(2)) {
            // 有揽收任务单号，则为已指派
            wrapper.isNotNull(TmsCustomerOrderEntity::getPTaskOrder);
        }
        // 批量查询待揽收指派客户单
        if (CollUtil.isNotEmpty(vo.getEntrustedOrderNoList())) {
            wrapper.in(TmsCustomerOrderEntity::getEntrustedOrderNumber, vo.getEntrustedOrderNoList());
        }

        return customerOrderMapper.selectJoinPage(page, TmsCustomerOrderPageVo.class, wrapper);
    }

    // 揽收指派列表详情
    @Override
    public Page<TmsCollectionAssignDetailPageVo> listCollectionAssignDetail(Page page, TmsCollectionAssignDetailPageVo orderPageVo) {
        MPJLambdaWrapper<TmsCustomerOrderEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(TmsCustomerOrderEntity.class)
                .selectAs(TmsCustomerEntity::getCustomerName, TmsCustomerOrderPageVo.Fields.entrustedCustomer)
                .selectAs(TmsCustomerEntity::getIsPush, TmsCustomerOrderPageVo.Fields.customerIsPush)
                .leftJoin(TmsCustomerEntity.class, TmsCustomerEntity::getId, TmsCustomerOrderEntity::getCustomerId)

                .like(StrUtil.isNotBlank(orderPageVo.getCustomerOrderNumber()), TmsCustomerOrderEntity::getCustomerOrderNumber, orderPageVo.getCustomerOrderNumber())
                .like(StrUtil.isNotBlank(orderPageVo.getEntrustedOrderNumber()), TmsCustomerOrderEntity::getEntrustedOrderNumber, orderPageVo.getEntrustedOrderNumber())
                .like(StrUtil.isNotBlank(orderPageVo.getShipperName()), TmsCustomerOrderEntity::getShipperName, orderPageVo.getShipperName())
                .like(StrUtil.isNotBlank(orderPageVo.getShipperPhone()), TmsCustomerOrderEntity::getShipperPhone, orderPageVo.getShipperPhone())
                .like(StrUtil.isNotBlank(orderPageVo.getOrigin()), TmsCustomerOrderEntity::getOrigin, orderPageVo.getOrigin())
                .like(StrUtil.isNotBlank(orderPageVo.getDestination()), TmsCustomerOrderEntity::getDestination, orderPageVo.getDestination())
                .eq(ObjectUtil.isNotNull(orderPageVo.getReceiveType()), TmsCustomerOrderEntity::getReceiveType, orderPageVo.getReceiveType())
                .eq(ObjectUtil.isNotNull(orderPageVo.getOrderStatus()), TmsCustomerOrderEntity::getOrderStatus, orderPageVo.getOrderStatus())
                .eq(TmsCustomerOrderEntity::getDelFlag, "0")
                // 业务模式：1 揽收，2 中大件，3 卡派
                .eq(ObjectUtil.isNotNull(orderPageVo.getBusinessModel()), TmsCustomerOrderEntity::getBusinessModel, orderPageVo.getBusinessModel())
                // 下单时间、发货时间、到货时间 根据范围搜索
                .between(StrUtil.isNotBlank(orderPageVo.getBeginTime()) && StrUtil.isNotBlank(orderPageVo.getEndTime()),
                        TmsCustomerOrderEntity::getCreateTime, orderPageVo.getBeginTime(), orderPageVo.getEndTime());
        // 固定条件
        wrapper.eq(TmsCustomerOrderEntity::getCustomerId, orderPageVo.getCustomerId());
        wrapper.eq(TmsCustomerOrderEntity::getTransportType, orderPageVo.getTransportType());
        wrapper.eq(TmsCustomerOrderEntity::getCollectWarehouseId, orderPageVo.getCollectWarehouseId());
        wrapper.eq(TmsCustomerOrderEntity::getSubFlag, false);
        wrapper.isNull(TmsCustomerOrderEntity::getPTaskOrder);
        wrapper.ne(TmsCustomerOrderEntity::getWarehouseGrade, 1);
        wrapper.orderByDesc(TmsCustomerOrderEntity::getCreateTime);
        return customerOrderMapper.selectJoinPage(page, TmsCustomerOrderPageVo.class, wrapper);
    }


    //获取无经纬度信息订单
    @Override
    @Transactional
    public void processNoLatestOrder() {
        LambdaQueryWrapper<TmsCustomerOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TmsCustomerOrderEntity::getSubFlag, false)
                .gt(TmsCustomerOrderEntity::getCreateTime, DateUtil.offsetDay(new Date(), -60))
                .isNull(TmsCustomerOrderEntity::getReceiverLatLng);
        List<TmsCustomerOrderEntity> tmsCustomerOrderEntities = customerOrderMapper.selectList(wrapper);
        for (int i = 0; i < tmsCustomerOrderEntities.size(); i++) {
            //获取经纬度
            TmsCustomerOrderEntity customerOrderEntity = tmsCustomerOrderEntities.get(i);
            String shipperLatLng = getLatLngByAddress(customerOrderEntity.getShipperAddress(), customerOrderEntity.getOrigin(), customerOrderEntity.getShipperPostalCode());
            String destLatLng = getLatLngByAddress(customerOrderEntity.getDestAddress(), customerOrderEntity.getDestination(), customerOrderEntity.getDestPostalCode());
            LambdaUpdateWrapper<TmsCustomerOrderEntity> updateWrapper = new LambdaUpdateWrapper<>();
            //更新主子单号
            updateWrapper.likeRight(TmsCustomerOrderEntity::getCustomerOrderNumber, customerOrderEntity.getCustomerOrderNumber())
                    .set(TmsCustomerOrderEntity::getShipperLatLng, shipperLatLng)
                    .set(TmsCustomerOrderEntity::getReceiverLatLng, destLatLng);
            customerOrderMapper.update(null, updateWrapper);
        }

        //获取经纬度的同时根据订单的三字邮编获取区域，将区域的路线号回填订单
        LambdaQueryWrapper<TmsCustomerOrderEntity> newWrapper = new LambdaQueryWrapper<>();
        newWrapper.gt(TmsCustomerOrderEntity::getCreateTime, DateUtil.offsetDay(new Date(), -60))
                //还没有路线号的订单（主子单）
                .isNull(TmsCustomerOrderEntity::getRouteNumber);
        List<TmsCustomerOrderEntity> notRouteNumberOrders = customerOrderMapper.selectList(newWrapper);
        for (TmsCustomerOrderEntity notRouteNumberOrder : notRouteNumberOrders) {
            //取订单此时的收货地三字邮编
            String zipCode = notRouteNumberOrder.getDestPostalCode().trim().substring(0, 3);
            //根据三字邮编获取路线号
            String routeNumber = tmsOverAreaService.getRouteNumberByZipCode(zipCode);
            //回填路线号
            notRouteNumberOrder.setRouteNumber(routeNumber);
        }
        if (CollectionUtil.isNotEmpty(notRouteNumberOrders)) {
            //更新
            this.updateBatchById(notRouteNumberOrders);
        }


        //根据订单的数量判断是否一票多件
        LambdaQueryWrapper<TmsCustomerOrderEntity> oneWrapper = new LambdaQueryWrapper<>();
        oneWrapper.gt(TmsCustomerOrderEntity::getCreateTime, DateUtil.offsetDay(new Date(), -60))
                //一票多件为null时，默认下单了就是null
                .isNull(TmsCustomerOrderEntity::getIsOneTicketMany)
                //主单
                .eq(TmsCustomerOrderEntity::getSubFlag, false);
        List<TmsCustomerOrderEntity> tmsCustomerOrderList = customerOrderMapper.selectList(oneWrapper);
        for (TmsCustomerOrderEntity order : tmsCustomerOrderList) {
            List<TmsCustomerOrderEntity> customerOrders = customerOrderMapper.selectList(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                    .like(TmsCustomerOrderEntity::getEntrustedOrderNumber, order.getEntrustedOrderNumber()));
            //当前根据主单查询订单数量大于等于3（一个主单两个子单以上时）
            if (customerOrders.size() >= 3) {
                //回填路线号
                order.setIsOneTicketMany(1);
            } else {
                order.setIsOneTicketMany(0);
            }
        }
        if (CollectionUtil.isNotEmpty(tmsCustomerOrderList)) {
            //更新
            this.updateBatchById(tmsCustomerOrderList);
        }
    }

    @Override
    public void supplementExchangeOrderInfo() {
        //获取所有pushOrder不为NULL的子单号
        LambdaQueryWrapper<TmsCustomerOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.isNotNull(TmsCustomerOrderEntity::getPushOrder)
                .eq(TmsCustomerOrderEntity::getSubFlag, true)
                .eq(TmsCustomerOrderEntity::getDelFlag, "0");
        List<TmsCustomerOrderEntity> list = this.list(wrapper);
        for (TmsCustomerOrderEntity tmsCustomerOrder : list) {
            //根据子单号获取货物信息
            LambdaQueryWrapper<TmsCargoInfoEntity> cargoWrapper = new LambdaQueryWrapper<>();
            cargoWrapper.eq(TmsCargoInfoEntity::getEntrustedOrderNumber, tmsCustomerOrder.getEntrustedOrderNumber())
                    .eq(TmsCargoInfoEntity::getDelFlag, "0")
                    .last("limit 1");
            TmsCargoInfoEntity cargoInfo = cargoInfoService.getOne(cargoWrapper);
            if (cargoInfo != null){
                //插入换单列表
                TmsThirdPartPostEntity tmsThirdPartPostEntity = new TmsThirdPartPostEntity();
                tmsThirdPartPostEntity.setChannel("UNI");
                tmsThirdPartPostEntity.setLabelUrl(tmsCustomerOrder.getPushLabel());
                tmsThirdPartPostEntity.setNbOrderNo(tmsCustomerOrder.getEntrustedOrderNumber());
                tmsThirdPartPostEntity.setChannelOrderNo(tmsCustomerOrder.getPushOrder());
                tmsThirdPartPostEntity.setCustomerOrderNo(cargoInfo.getBoxNum());
                tmsThirdPartPostEntity.setTime(tmsCustomerOrder.getCreateTime());
                tmsThirdPartPostService.save(tmsThirdPartPostEntity);
            }
        }
    }


    //查询订单对应的司机信息(揽收、任务、干线)
    @Override
    public Map<String, List<TmsLmdDriverPageVo>> getDriverInfo(TmsCustomerOrderEntity tmsCustomerOrder) {
        HashMap<String, List<TmsLmdDriverPageVo>> map = new HashMap<>();
        map.put("ls", transportTaskOrderService.getDriverByOrderNo(tmsCustomerOrder.getPTaskOrder()));
        map.put("ps", transportTaskOrderService.getDriverByOrderNo(tmsCustomerOrder.getDTaskOrder()));
        map.put("gx", tmsLineHaulOrderService.getDriverByOrderNo(tmsCustomerOrder.getEntrustedOrderNumber()));
        return map;

    }


    //获取订单详情
    @Override
    public TmsCustomerOrderPageVo getDetail(Long id) {
        TmsCustomerOrderEntity tmsCustomerOrder = this.getById(id);
        TmsCustomerOrderPageVo tmsCustomerOrderPageVo = new TmsCustomerOrderPageVo();
        BeanUtils.copyProperties(tmsCustomerOrder, tmsCustomerOrderPageVo);
        tmsCustomerOrderPageVo.setTmsLmdDriverEntityMap(getDriverInfo(tmsCustomerOrder));
        tmsCustomerOrderPageVo.setPodList(getSubPod(tmsCustomerOrder.getEntrustedOrderNumber()));
        return tmsCustomerOrderPageVo;
    }


    //获取根据主单获取子单POD信息
    public List<PodVoBySub> getSubPod(String orderNo) {
        ArrayList<PodVoBySub> podVoBySubs = new ArrayList<>();
        LambdaQueryWrapper<TmsCustomerOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNo)
                .eq(TmsCustomerOrderEntity::getSubFlag, true)
                .eq(TmsCustomerOrderEntity::getDelFlag, "0");
        List<TmsCustomerOrderEntity> list = this.list(wrapper);
        for (TmsCustomerOrderEntity tmsCustomerOrder : list){
            PodVoBySub podVoBySub = new PodVoBySub();
            podVoBySub.setTrackNo(tmsCustomerOrder.getEntrustedOrderNumber());
            String deliveryProof = tmsCustomerOrder.getDeliveryProof();
            podVoBySub.setPod(deliveryProof);
            if (StrUtil.isBlank(deliveryProof)){
                podVoBySub.setPod(getCustomerOrderPod(orderNo).getPod());
            }
            podVoBySubs.add(podVoBySub);
        }
        return podVoBySubs;
    }


//    // 官网中大件轨迹获取验证码
//    @Override
//    public R getCaptcha(List<String> orderList) {
//        if (CollUtil.isEmpty(orderList)) {
//            return R.failed("Order number cannot be empty");
//        }
//
//        // 使用第一个订单号作为验证码存储 key（若多个，可以扩展）
//        String orderNo = orderList.get(0);
//        String redisKey = TRACK_CODE_PREFIX + orderNo;
//
//        // 生成 6 位数字验证码
//        String code = String.valueOf(RandomUtil.randomInt(100000, 999999));
//
//        // 保存到 Redis，5 分钟有效
//        redisTemplate.opsForValue().set(redisKey, code, Duration.ofMinutes(5));
//
//        log.info("生成轨迹验证码成功，订单号：{}，验证码：{}", orderNo, code);
//
//        // TODO: PROD环境可以发送短信或邮件，这里先直接返回
//        return LocalizedR.ok("tms.captcha.has.been.generated", code);
//    }
//
//    // 官网中大件轨迹校验验证码
//    @Override
//    public R getCaptcha(String inputCode, List<String> orderList) {
//        if (CollUtil.isEmpty(orderList) || StrUtil.isBlank(inputCode)) {
//            return LocalizedR.failed("tms.captcha.not.null", "");
//        }
//
//        String orderNo = orderList.get(0);
//        String redisKey = TRACK_CODE_PREFIX + orderNo;
//        log.info("中大件轨迹验证码 redisKey：" + redisKey);
//
//        String storedCode = redisTemplate.opsForValue().get(redisKey);
//        if (StrUtil.isBlank(storedCode)) {
//            return LocalizedR.failed("tms.captcha.has.expired", storedCode);
//        }
//
//        if (!storedCode.equals(inputCode)) {
//            return LocalizedR.failed("tms.captcha.error", storedCode);
//        }
//
//        // 验证成功后可以删除验证码，防止重复使用
//        redisTemplate.delete(redisKey);
//
//        return LocalizedR.ok("tms.captcha.verified", storedCode);
//    }

    /**
     * 构建特定格式的轨迹响应数据
     * 当客户token匹配特定值时返回此格式
     *
     * @param orderNo 订单号
     * @param customerOrder 客户订单信息
     * @return 特定格式的轨迹响应
     */
    private R buildSpecialTrackResponse(String orderNo, TmsCustomerOrderEntity customerOrder) {
        try {
            // 查询子单列表
            List<TmsCustomerOrderEntity> subOrders = customerOrderMapper.selectList(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                    .likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, customerOrder.getEntrustedOrderNumber())
                    .eq(TmsCustomerOrderEntity::getSubFlag, true));

            if (CollUtil.isEmpty(subOrders)) {
                // 构建空轨迹的特定格式响应
                Map<String, Object> response = new HashMap<>();
                response.put("code", 1);
                response.put("message", "success");

                List<Map<String, Object>> dataList = new ArrayList<>();
                Map<String, Object> trackData = new HashMap<>();
                trackData.put("trackingNo", orderNo);
                trackData.put("fromDetail", new ArrayList<>());
                dataList.add(trackData);

                response.put("data", dataList);
                return R.ok(dataList);
            }

            // 拿到子单号
            List<String> subOrderNos = subOrders.stream()
                    .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                    .collect(Collectors.toList());

            // 查询子单轨迹
            List<TmsOrderTrackEntity> orderTrackList = orderTrackMapper.selectList(
                    new LambdaQueryWrapper<TmsOrderTrackEntity>()
                            .in(TmsOrderTrackEntity::getOrderNo, subOrderNos)
                            .orderByDesc(TmsOrderTrackEntity::getAddTime) // 按轨迹时间倒序排序
            );

            // 构建特定格式的响应数据
            Map<String, Object> response = new HashMap<>();
            response.put("code", 1);
            response.put("message", "success");

            List<Map<String, Object>> dataList = new ArrayList<>();
            Map<String, Object> trackData = new HashMap<>();
            trackData.put("trackingNo", orderNo);

            // 构建fromDetail轨迹节点列表
            List<Map<String, Object>> fromDetailList = new ArrayList<>();
            for (TmsOrderTrackEntity track : orderTrackList) {
                Map<String, Object> trackNode = new HashMap<>();

                // 格式化时间
                String pathTime = "";
                if (track.getAddTime() != null) {
                    pathTime = track.getAddTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                }

                trackNode.put("pathTime", pathTime);
                trackNode.put("flightNo", ""); // 航班号，暂时为空
                trackNode.put("pathLocation", track.getCity() != null ? track.getCity() : "");
                trackNode.put("timezone", track.getTimeZone());
                trackNode.put("pathCode", track.getStatusCode() != null ? track.getStatusCode().toString() : "");
                trackNode.put("pathInfo", track.getLocationDescription() != null ? track.getLocationDescription() : "");
                trackNode.put("status", track.getOrderStatus() != null ? track.getOrderStatus() : "");

                fromDetailList.add(trackNode);
            }

            trackData.put("fromDetail", fromDetailList);
            dataList.add(trackData);
            response.put("data", dataList);

            return R.ok(dataList);

        } catch (Exception e) {
            log.error("构建特定格式轨迹响应失败，订单号: {}, 错误: {}", orderNo, e.getMessage(), e);
            // 发生异常时返回空轨迹的特定格式响应
            Map<String, Object> response = new HashMap<>();
            response.put("code", 1);
            response.put("message", "success");

            List<Map<String, Object>> dataList = new ArrayList<>();
            Map<String, Object> trackData = new HashMap<>();
            trackData.put("trackingNo", orderNo);
            trackData.put("fromDetail", new ArrayList<>());
            dataList.add(trackData);

            response.put("data", dataList);
            return R.ok(dataList);
        }
    }

}
