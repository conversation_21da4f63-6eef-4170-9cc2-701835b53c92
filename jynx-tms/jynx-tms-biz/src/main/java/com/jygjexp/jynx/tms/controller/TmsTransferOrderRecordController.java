package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsTransferOrderRecordEntity;
import com.jygjexp.jynx.tms.service.TmsTransferOrderRecordService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 转单记录表
 *
 * <AUTHOR>
 * @date 2025-05-22 14:08:30
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsTransferOrderRecord" )
@Tag(description = "tmsTransferOrderRecord" , name = "转单记录表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsTransferOrderRecordController {

    private final  TmsTransferOrderRecordService tmsTransferOrderRecordService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsTransferOrderRecord 转单记录表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsTransferOrderRecord_view')" )
    public R getTmsTransferOrderRecordPage(@ParameterObject Page page, @ParameterObject TmsTransferOrderRecordEntity tmsTransferOrderRecord) {
        LambdaQueryWrapper<TmsTransferOrderRecordEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsTransferOrderRecordService.page(page, wrapper));
    }


    /**
     * 通过id查询转单记录表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsTransferOrderRecord_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsTransferOrderRecordService.getById(id));
    }

    /**
     * 新增转单记录表
     * @param tmsTransferOrderRecord 转单记录表
     * @return R
     */
    @Operation(summary = "新增转单记录表" , description = "新增转单记录表" )
    @SysLog("新增转单记录表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsTransferOrderRecord_add')" )
    public R save(@RequestBody TmsTransferOrderRecordEntity tmsTransferOrderRecord) {
        return R.ok(tmsTransferOrderRecordService.save(tmsTransferOrderRecord));
    }

    /**
     * 修改转单记录表
     * @param tmsTransferOrderRecord 转单记录表
     * @return R
     */
    @Operation(summary = "修改转单记录表" , description = "修改转单记录表" )
    @SysLog("修改转单记录表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsTransferOrderRecord_edit')" )
    public R updateById(@RequestBody TmsTransferOrderRecordEntity tmsTransferOrderRecord) {
        return R.ok(tmsTransferOrderRecordService.updateById(tmsTransferOrderRecord));
    }

    /**
     * 通过id删除转单记录表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除转单记录表" , description = "通过id删除转单记录表" )
    @SysLog("通过id删除转单记录表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsTransferOrderRecord_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsTransferOrderRecordService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsTransferOrderRecord 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsTransferOrderRecord_export')" )
    public List<TmsTransferOrderRecordEntity> export(TmsTransferOrderRecordEntity tmsTransferOrderRecord,Long[] ids) {
        return tmsTransferOrderRecordService.list(Wrappers.lambdaQuery(tmsTransferOrderRecord).in(ArrayUtil.isNotEmpty(ids), TmsTransferOrderRecordEntity::getId, ids));
    }
}