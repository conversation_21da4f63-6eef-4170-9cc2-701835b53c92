package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.constants.TrackTypeConstant;
import com.jygjexp.jynx.tms.entity.TmsCustomerOrderEntity;
import com.jygjexp.jynx.tms.entity.TmsOrderTrackEntity;
import com.jygjexp.jynx.tms.mapper.TmsOrderTrackMapper;
import com.jygjexp.jynx.tms.service.TmsCustomerOrderService;
import com.jygjexp.jynx.tms.service.TmsOrderTrackNewService;
import com.jygjexp.jynx.tms.vo.TmsWebOrderTrackVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: xiongpengfei
 * @Description: 中大件轨迹接口装载方法接口
 * @Date: 2025/8/19 10:07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TmsOrderTrackNewServiceImpl implements TmsOrderTrackNewService {

    private final TmsCustomerOrderService customerOrderService;
    private final TmsOrderTrackMapper orderTrackMapper;

    // 佳邮轨迹查询接口配置
    private static final String JY_API_URL = "http://api.jygjexp.com/v1/api/tracking/query/trackNB";
    private static final String JY_API_KEY = "675bfe2fd67105e9a88e564bf0f0344c";


    // ==================== 迁移自zxoms的官网轨迹查询接口实现 ====================

    /**
     * 批量订单轨迹查询 - 根据不同单号类型实现不同的轨迹查询策略
     * @param pkgNos 包裹号列表，逗号分隔
     * @param zipInput 邮编（可选）
     * @return 批量轨迹查询结果
     */
    @Override
    public R getTracksFromZxoms(String pkgNos, String zipInput) {
        if (StrUtil.isBlank(pkgNos)) {
            return R.failed("500300", "pkgNo can not be empty");
        }

        log.info("TMS批量轨迹查询开始，包裹号：{}", pkgNos);

        int maxSize = 50;
        String[] pkgNoArr = pkgNos.trim().split(",");
        if (pkgNoArr.length > maxSize) {
            return R.failed("500301", "Query up to " + maxSize + " packages at a time");
        }

        JSONArray ja = new JSONArray(); // 最终轨迹结果集合
        Set<String> handledPkgNos = new HashSet<>(); // 防止重复添加
        List<String> pkgNoList = Arrays.stream(pkgNoArr).map(String::trim).filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(pkgNoList)) {
            return R.failed("500300", "No valid package numbers provided");
        }

        // 根据单号类型分类处理
        List<String> nOrderNos = new ArrayList<>(); // N开头：内部单号
        List<String> jyOrderNos = new ArrayList<>(); // JY开头：外部单号
        List<String> gvOrderNos = new ArrayList<>(); // GV开头：外部单号
        List<String> u9999OrderNos = new ArrayList<>(); // U9999开头：外部单号

        for (String pkgNo : pkgNoList) {
            if (pkgNo.startsWith("N")) {
                nOrderNos.add(pkgNo);
            } else if (pkgNo.startsWith("JY")) {
                jyOrderNos.add(pkgNo);
            } else if (pkgNo.startsWith("GV")) {
                gvOrderNos.add(pkgNo);
            } else if (pkgNo.startsWith("U9999")) {
                u9999OrderNos.add(pkgNo);
            } else {
                // 其他类型单号按N开头处理
                nOrderNos.add(pkgNo);
            }
        }

        // 处理N开头单号：调用TmsOrderTrackNew服务
        if (!nOrderNos.isEmpty()) {
            processNOrderNos(nOrderNos, zipInput, ja, handledPkgNos);
        }

        // 处理JY/GV/U9999开头单号：先查本系统，条件不满足则查佳邮接口
        List<String> externalOrderNos = new ArrayList<>();
        externalOrderNos.addAll(jyOrderNos);
        externalOrderNos.addAll(gvOrderNos);
        externalOrderNos.addAll(u9999OrderNos);

        if (!externalOrderNos.isEmpty()) {
            processExternalOrderNos(externalOrderNos, zipInput, ja, handledPkgNos);
        }

        log.info("TMS批量轨迹查询成功，查询包裹数：{}，返回结果数：{}", pkgNoList.size(), ja.size());
        return R.ok(ja);
    }



    /**
     * 处理JY/GV/U9999开头单号：先查本系统，条件不满足则查佳邮接口
     * @param externalOrderNos 外部订单号列表
     * @param zipInput 邮编
     * @param ja 结果集合
     * @param handledPkgNos 已处理的包裹号集合
     */
    private void processExternalOrderNos(List<String> externalOrderNos, String zipInput, JSONArray ja, Set<String> handledPkgNos) {
        // 通过jyOrderNo或customerOrderNumber字段查询本系统是否存在该单号
        List<TmsCustomerOrderEntity> orders = customerOrderService.list(
                new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                        .eq(TmsCustomerOrderEntity::getDelFlag, "0")
                        .eq(TmsCustomerOrderEntity::getSubFlag,false)
                        .and(wrapper -> wrapper
                                .in(TmsCustomerOrderEntity::getJyOrderNo, externalOrderNos)
                                .or()
                                .in(TmsCustomerOrderEntity::getCustomerOrderNumber, externalOrderNos)));

        // 处理邮编过滤逻辑
        // 获取到邮编列表
        List<String> zipList = Arrays.stream(StrUtil.nullToEmpty(zipInput).split(","))
                .map(String::trim)
                .filter(StrUtil::isNotBlank)
                .map(String::toUpperCase)
                .collect(Collectors.toList());

        // 检查是否订单含有目标邮编
        boolean hasZipMatch = CollUtil.isNotEmpty(zipList) && orders.stream().anyMatch(order -> {
            String destZip = order.getDestPostalCode();
            return StrUtil.isNotBlank(destZip) && zipList.contains(destZip.toUpperCase());
        });

        // 记录NB系统轨迹数据充足的订单
        Set<String> sufficientLocalTrackOrders = new HashSet<>();

        // 从查询到的订单中提取委托单号并去重
        List<String> entrustedOrderNumbers = orders.stream()
                .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        // 批量查询轨迹数据，因为jy、gv、u9999是一票一件，所以这个写法是根据一票一件的写法
        Map<String, List<TmsOrderTrackEntity>> trackMap = new HashMap<>();
        for (String entrustedOrderNo : entrustedOrderNumbers) {
            String mainOrderNo = entrustedOrderNo;
            if (mainOrderNo != null && mainOrderNo.length() > 15) {
                mainOrderNo = mainOrderNo.substring(0, mainOrderNo.length() - 3);
            }

            List<TmsOrderTrackEntity> tracks = orderTrackMapper.selectList(
                    new LambdaQueryWrapper<TmsOrderTrackEntity>()
                            .eq(TmsOrderTrackEntity::getDelFlag, "0")
                            .eq(TmsOrderTrackEntity::getOrderNo, mainOrderNo+"001")
                            .eq(TmsOrderTrackEntity::getTrackType, TrackTypeConstant.EXTERNAL)
                            .orderByDesc(TmsOrderTrackEntity::getAddTime));

            if (CollUtil.isNotEmpty(tracks)) {
                trackMap.put(entrustedOrderNo, tracks);
            }
        }

        // 判断本系统轨迹数据
        for (String orderNo : externalOrderNos) {
            // 查找匹配的订单
            TmsCustomerOrderEntity matchedOrder = orders.stream()
                    .filter(order -> orderNo.equals(order.getJyOrderNo()) || orderNo.equals(order.getCustomerOrderNumber()))
                    .findFirst()
                    .orElse(null);

            if (matchedOrder != null) {
                // 邮编过滤
                if (hasZipMatch) {
                    String destZip = StrUtil.blankToDefault(matchedOrder.getDestPostalCode(), "").toUpperCase();
                    if (!zipList.contains(destZip)) {
                        continue;
                    }
                }

                // 获取该订单对应的轨迹数据
                String entrustedOrderNo = matchedOrder.getEntrustedOrderNumber();
                List<TmsOrderTrackEntity> tracks = trackMap.get(entrustedOrderNo);

                // 如果本系统轨迹条数 >= 2条，使用本系统轨迹
                if (CollUtil.isNotEmpty(tracks) && tracks.size() >= 2) {
                    JSONObject orderJo = new JSONObject();
                    orderJo.set("pkgNo", orderNo);
                    orderJo.set("orderStatus", matchedOrder.getOrderStatus());
                    orderJo.set("destination", matchedOrder.getDestination());

                    // 转换轨迹数据格式
                    JSONArray trackJa = tracks.stream()
                            .filter(track -> track.getLocationDescription() != null)
                            .map(track -> {
                                JSONObject jo = new JSONObject();
                                jo.set("code", track.getStatusCode());
                                jo.set("time", DateUtil.format(track.getAddTime(), "yyyy-MM-dd'T'HH:mm:ss"));
                                jo.set("timezone", StrUtil.blankToDefault(track.getTimeZone(), ""));
                                jo.set("status", StrUtil.blankToDefault(track.getOrderStatus(), ""));
                                jo.set("city", StrUtil.blankToDefault(track.getCity(), ""));
                                jo.set("content", StrUtil.blankToDefault(track.getExternalDescription(), ""));
                                return jo;
                            }).collect(Collectors.toCollection(JSONArray::new));

                    orderJo.set("track", trackJa);

                    // 根据邮编判断是否返回签收图
                    if (hasZipMatch && StrUtil.isNotBlank(matchedOrder.getDeliveryProof())) {
                        orderJo.set("images", Arrays.asList(matchedOrder.getDeliveryProof()));
                    }

                    ja.add(orderJo);
                    handledPkgNos.add(orderNo);
                    sufficientLocalTrackOrders.add(orderNo);
                    log.info("外部订单使用TMS本地轨迹：{}", orderNo);
                }
            }
        }

        // 佳邮接口兜底逻辑
        List<String> needJyApiOrders = externalOrderNos.stream()
                .filter(orderNo -> !sufficientLocalTrackOrders.contains(orderNo))
                .collect(Collectors.toList());

        if (!needJyApiOrders.isEmpty()) {
            log.info("外部订单号在本地系统轨迹数据不足，调用佳邮接口查询，订单号：{}", needJyApiOrders);
            queryJyApiTracksWithFallback(needJyApiOrders, ja, handledPkgNos, orders, zipList, hasZipMatch);
        }
    }



    /**
     * 调用佳邮接口查询轨迹（兜底）
     * @param orderNos 订单号列表
     * @param ja 结果集合
     * @param handledPkgNos 已处理的包裹号集合
     * @param localOrders 本地订单数据
     * @param zipList 邮编列表
     * @param hasZipMatch 是否有邮编匹配
     */
    private void queryJyApiTracksWithFallback(List<String> orderNos, JSONArray ja, Set<String> handledPkgNos,
                                              List<TmsCustomerOrderEntity> localOrders, List<String> zipList, boolean hasZipMatch) {
        if (CollUtil.isEmpty(orderNos)) {
            return;
        }

        try {
            // 构建请求
            HttpRequest request = HttpRequest.post(JY_API_URL);
            request.header("apiKey", JY_API_KEY);
            request.body(JSONUtil.toJsonStr(orderNos));

            // 发送请求
            HttpResponse response = request.execute();
            String result = response.body();
            log.info("佳邮接口返回结果：{}", result);

            JSONObject retJo = JSONUtil.parseObj(result);
            if (retJo.getInt("code") == 1) {
                JSONArray dataArray = retJo.getJSONArray("data");
                if (dataArray != null && !dataArray.isEmpty()) {
                    for (Object item : dataArray) {
                        JSONObject tracks = (JSONObject) item;
                        // 根据实际返回格式获取订单号
                        String pkgNo = tracks.getStr("trackingNo");
                        if (StrUtil.isBlank(pkgNo)) {
                            pkgNo = tracks.getStr("logisticsServiceNumber");
                        }

                        // 获取轨迹详情数组
                        JSONArray fromDetailArray = tracks.getJSONArray("fromDetail");

                        // 检查佳邮轨迹是否有足够数据（≥2条）
                        if (fromDetailArray != null && fromDetailArray.size() >= 2) {
                            JSONObject orderJo = new JSONObject();
                            orderJo.set("pkgNo", pkgNo);

                            // 转换轨迹格式以匹配统一返回格式
                            JSONArray trackJa = new JSONArray();
                            for (Object trackItem : fromDetailArray) {
                                JSONObject trackDetail = (JSONObject) trackItem;
                                JSONObject trackJo = new JSONObject();
                                trackJo.set("code", trackDetail.getStr("pathCode"));
                                trackJo.set("time", trackDetail.getStr("pathTime"));
                                trackJo.set("status", tracks.getStr("status"));
                                trackJo.set("city", trackDetail.getStr("pathLocation"));
                                trackJo.set("timezone", trackDetail.getStr("timezone"));
                                trackJo.set("content", trackDetail.getStr("pathInfo"));
                                trackJa.add(trackJo);
                            }
                            orderJo.set("track", trackJa);

                            // 处理POD签收图片 - 使用pods字段
                            JSONArray podsArray = tracks.getJSONArray("pods");
                            if (podsArray != null && !podsArray.isEmpty()) {
                                List<String> imageUrls = new ArrayList<>();
                                for (Object pod : podsArray) {
                                    if (pod instanceof String) {
                                        String imageUrl = (String) pod;
                                        if (StrUtil.isNotBlank(imageUrl)) {
                                            imageUrls.add(imageUrl);
                                        }
                                    }
                                }
                                if (!imageUrls.isEmpty()) {
                                    orderJo.set("images", imageUrls);
                                }
                            }

                            ja.add(orderJo);
                            handledPkgNos.add(pkgNo);
                            log.info("佳邮接口查询成功，订单号：{}，轨迹数量：{}", pkgNo, fromDetailArray.size());
                        } else {
                            // 佳邮轨迹数据不足，使用本系统轨迹作为兜底
                            log.info("佳邮接口轨迹数据不足（<2条），使用本系统轨迹作为兜底，订单号：{}，轨迹数量：{}",
                                    pkgNo, fromDetailArray != null ? fromDetailArray.size() : 0);
                            useLocalTrackAsFallback(pkgNo, localOrders, zipList, hasZipMatch, ja, handledPkgNos);
                        }
                    }
                }
            } else {
                log.error("佳邮接口返回错误：{}", retJo.getStr("message"));
                // 佳邮接口失败，使用本系统轨迹作为兜底
                for (String orderNo : orderNos) {
                    useLocalTrackAsFallback(orderNo, localOrders, zipList, hasZipMatch, ja, handledPkgNos);
                }
            }

        } catch (Exception e) {
            log.error("调用佳邮接口异常，订单号：{}", orderNos, e);
            // 异常情况下使用本系统轨迹作为兜底
            for (String orderNo : orderNos) {
                useLocalTrackAsFallback(orderNo, localOrders, zipList, hasZipMatch, ja, handledPkgNos);
            }
        }
    }

    /**
     * 使用NB系统轨迹作为兜底
     * @param orderNo 订单号
     * @param localOrders 本地订单数据
     * @param zipList 邮编列表
     * @param hasZipMatch 是否有邮编匹配
     * @param ja 结果集合
     * @param handledPkgNos 已处理的包裹号集合
     */
    private void useLocalTrackAsFallback(String orderNo, List<TmsCustomerOrderEntity> localOrders,
                                         List<String> zipList, boolean hasZipMatch, JSONArray ja, Set<String> handledPkgNos) {
        // 查找匹配的订单
        TmsCustomerOrderEntity matchedOrder = localOrders.stream()
                .filter(order -> orderNo.equals(order.getJyOrderNo()) || orderNo.equals(order.getCustomerOrderNumber()))
                .findFirst()
                .orElse(null);

        if (matchedOrder != null) {
            // 邮编过滤
            if (hasZipMatch) {
                String destZip = StrUtil.blankToDefault(matchedOrder.getDestPostalCode(), "").toUpperCase();
                if (!zipList.contains(destZip)) {
                    return;
                }
            }

            // 查询轨迹数据
            String mainOrderNo = matchedOrder.getEntrustedOrderNumber();
            if (mainOrderNo != null && mainOrderNo.length() > 15) {
                mainOrderNo = mainOrderNo.substring(0, mainOrderNo.length() - 3);
            }

            List<TmsOrderTrackEntity> tracks = orderTrackMapper.selectList(
                    new LambdaQueryWrapper<TmsOrderTrackEntity>()
                            .eq(TmsOrderTrackEntity::getDelFlag, "0")
                            .likeRight(TmsOrderTrackEntity::getOrderNo, mainOrderNo+"001")
                            .eq(TmsOrderTrackEntity::getTrackType, TrackTypeConstant.EXTERNAL)
                            .orderByDesc(TmsOrderTrackEntity::getAddTime));

            if (CollUtil.isNotEmpty(tracks)) {
                JSONObject orderJo = new JSONObject();
                orderJo.set("pkgNo", orderNo);
                orderJo.set("orderStatus", matchedOrder.getOrderStatus());
                orderJo.set("destination", matchedOrder.getDestination());

                // 转换轨迹数据格式
                JSONArray trackJa = tracks.stream()
                        .filter(track -> track.getLocationDescription() != null)
                        .map(track -> {
                            JSONObject jo = new JSONObject();
                            jo.set("code", track.getStatusCode());
                            jo.set("time", DateUtil.format(track.getAddTime(), "yyyy-MM-dd'T'HH:mm:ss"));
                            jo.set("timezone",StrUtil.blankToDefault(track.getTimeZone(), ""));
                            jo.set("status", StrUtil.blankToDefault(track.getOrderStatus(), ""));
                            jo.set("city", StrUtil.blankToDefault(track.getCity(), ""));
                            jo.set("content", StrUtil.blankToDefault(track.getExternalDescription(), ""));
                            return jo;
                        }).collect(Collectors.toCollection(JSONArray::new));

                orderJo.set("track", trackJa);

                // 根据邮编判断是否返回签收图
                if (hasZipMatch && StrUtil.isNotBlank(matchedOrder.getDeliveryProof())) {
                    orderJo.set("images", Arrays.asList(matchedOrder.getDeliveryProof()));
                }

                ja.add(orderJo);
                handledPkgNos.add(orderNo);
            }
        }
    }



    // 原zxoms官网查询接口兼容查询nb中大件
    @Override
    public void processNOrderNos(List<String> nOrderNos, String zipInput, JSONArray ja, Set<String> handledPkgNos) {
        try {
            // 构建TmsWebOrderTrackVo参数
            TmsWebOrderTrackVo vo = new TmsWebOrderTrackVo();
            vo.setOrderList(nOrderNos);
            vo.setZip(zipInput);

            // 调用TmsCustomerOrderService.getZdjWebTrackNew()方法
            R result = customerOrderService.getZdjWebTrackNew(vo);

            if (result.getCode() == 0 && result.getData() != null) {
                // 转换getZdjWebTrackNew的返回格式为getTracksFromZxoms的统一格式
                convertZdjWebTrackToUnifiedFormat(result.getData(), ja, handledPkgNos, nOrderNos);
                log.info("N开头单号轨迹查询成功，订单号：{}", nOrderNos);
            } else {
                log.warn("N开头单号轨迹查询失败，订单号：{}，错误信息：{}", nOrderNos, result.getMsg());
            }
        } catch (Exception e) {
            log.error("N开头单号轨迹查询异常，订单号：{}", nOrderNos, e);
        }
    }


    /**
     * 转换统一格式
     * @param data getZdjWebTrackNew返回的数据
     * @param ja 结果集合
     * @param handledPkgNos 已处理的包裹号集合
     * @param orderNos 订单号列表
     */
    private void convertZdjWebTrackToUnifiedFormat(Object data, JSONArray ja, Set<String> handledPkgNos, List<String> orderNos) {
        try {
            if (data instanceof Map) {
                Map<String, Object> response = (Map<String, Object>) data;
                Map<String, Object> result = (Map<String, Object>) response.get("result");

                if (result != null) {
                    for (Map.Entry<String, Object> entry : result.entrySet()) {
                        String mainOrderNo = entry.getKey();
                        Map<String, Object> mainOrderInfo = (Map<String, Object>) entry.getValue();
                        List<Map<String, Object>> subOrders = (List<Map<String, Object>>) mainOrderInfo.get("subOrders");

                        if (CollUtil.isNotEmpty(subOrders)) {
                            for (Map<String, Object> subOrder : subOrders) {
                                String subOrderNo = (String) subOrder.get("subOrderNo");

                                // 检查是否是请求的订单号之一
                                //if (orderNos.contains(subOrderNo) || orderNos.contains(mainOrderNo)) {
                                    JSONObject orderJo = new JSONObject();
                                    orderJo.set("pkgNo", subOrderNo);
                                    orderJo.set("orderStatus", subOrder.get("orderStatus"));
                                    orderJo.set("destination", subOrder.get("destination"));

                                    // 转换轨迹格式
                                    List<Map<String, Object>> trackList = (List<Map<String, Object>>) subOrder.get("trackList");
                                    if (CollUtil.isNotEmpty(trackList)) {
                                        JSONArray trackJa = trackList.stream()
                                                .map(track -> {
                                                    JSONObject jo = new JSONObject();
                                                    jo.set("code", track.get("code"));
                                                    jo.set("time", track.get("trackTime"));
                                                    jo.set("status", track.get("status"));
                                                    jo.set("city", track.get("city"));
                                                    jo.set("content", track.get("trackDesc"));
                                                    jo.set("timezone", track.get("timeZone"));
                                                    return jo;
                                                }).collect(Collectors.toCollection(JSONArray::new));
                                        orderJo.set("track", trackJa);
                                    }

                                    // 处理签收图片
                                    String signImgUrl = (String) subOrder.get("signImgUrl");
                                    if (StrUtil.isNotBlank(signImgUrl)) {
                                        orderJo.set("images", Arrays.asList(signImgUrl));
                                    }

                                    ja.add(orderJo);
                                    handledPkgNos.add(subOrderNo);
                                    if (!subOrderNo.equals(mainOrderNo)) {
                                        handledPkgNos.add(mainOrderNo);
                                    }
                                //}
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("转换getZdjWebTrackNew返回格式异常", e);
        }
    }




}
