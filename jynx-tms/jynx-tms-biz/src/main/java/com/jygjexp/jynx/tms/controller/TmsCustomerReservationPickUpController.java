package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.abo.TmsCustomerReservationPickUpAbo;
import com.jygjexp.jynx.tms.dto.AppScanningDTO;
import com.jygjexp.jynx.tms.dto.PickUpPathPlanDriverDto;
import com.jygjexp.jynx.tms.entity.TmsCustomerReservationPickUpEntity;
import com.jygjexp.jynx.tms.enums.CustomerPickUpOrderStatusEnum;
import com.jygjexp.jynx.tms.model.bo.QueryCondition;
import com.jygjexp.jynx.tms.qbo.TmsCustomerReservationPickUpQbo;
import com.jygjexp.jynx.tms.service.TmsCustomerReservationPickUpService;
import com.jygjexp.jynx.tms.vo.TmsCustomerReservationPickUpVo;
import com.jygjexp.jynx.tms.vo.TmsPathPlanVo;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 客户预约取件
 *
 * <AUTHOR>
 * @date 2025-07-16 10:47:32
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsCustomerReservationPickUp" )
@Tag(description = "tmsCustomerReservationPickUp" , name = "客户预约取件管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsCustomerReservationPickUpController {

    private final  TmsCustomerReservationPickUpService tmsCustomerReservationPickUpService;

    /**
     * 分页查询
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @PostMapping("/page" )
    public R<IPage<TmsCustomerReservationPickUpVo>> getTmsCustomerReservationPickUpPage(@RequestBody QueryCondition<TmsCustomerReservationPickUpQbo> qbo) {
        return R.ok(tmsCustomerReservationPickUpService.pageData(qbo));
    }


    /**
     * 通过id查询客户预约取件
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    public R<TmsCustomerReservationPickUpVo> getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsCustomerReservationPickUpService.getDataById(id));
    }

    /**
     * 根据预约单号获取客户预约取件
     */
    @Operation(summary = "根据预约单号获取客户预约取件" , description = "根据预约单号获取客户预约取件" )
    @GetMapping("/getByReservationNumber/{reservationNumber}" )
    public R<TmsCustomerReservationPickUpVo> getByReservationNumber(@PathVariable("reservationNumber" ) String reservationNumber) {
        return R.ok(tmsCustomerReservationPickUpService.getDataByReservationNumber(reservationNumber));
    }

    /**
     * 新增客户预约取件
     */
    @Operation(summary = "新增客户预约取件" , description = "新增客户预约取件" )
    @PostMapping
    public R save(@RequestBody TmsCustomerReservationPickUpAbo abo) {
        return tmsCustomerReservationPickUpService.saveData(abo);
    }

    /**
     * 修改客户预约取件
     */
    @Operation(summary = "修改客户预约取件" , description = "修改客户预约取件" )
    @PutMapping
    public R updateById(@RequestBody TmsCustomerReservationPickUpEntity tmsCustomerReservationPickUp) {
        return tmsCustomerReservationPickUpService.updateDataById(tmsCustomerReservationPickUp);
    }

    /**
     * 通过id删除客户预约取件
     */
    @Operation(summary = "通过id删除客户预约取件" , description = "通过id删除客户预约取件" )
    @DeleteMapping
    public R<Boolean> removeById(@RequestBody Long[] ids) {
        return R.ok(tmsCustomerReservationPickUpService.removeBatchByIds(CollUtil.toList(ids)));
    }

    /**
     * 路径规划-揽收规划
     */
    @Operation(summary = "路径规划-揽收规划" , description = "路径规划-揽收规划" )
    @PostMapping("/pathPlanList" )
    public R<TmsPathPlanVo> pathPlanList(@RequestBody QueryCondition<TmsCustomerReservationPickUpQbo> qbo){
        return R.ok(tmsCustomerReservationPickUpService.pathPlanList(qbo));
    }

    /**
     * 路径规划-司机分配
     */
    @Operation(summary = "路径规划-司机分配" , description = "路径规划-司机分配" )
    @PostMapping("/pathPlanDriver" )
    public R<Boolean> pathPlanDriver(@RequestBody PickUpPathPlanDriverDto dto){
        tmsCustomerReservationPickUpService.pathPlanDriver(dto);
        return R.ok();
    }

    /**
     * app揽收任务-扫码
     */
    @Operation(summary = "app揽收任务-扫码" , description = "app揽收任务-扫码" )
    @PostMapping("/appScanning" )
    public R<Boolean> appScanning(@RequestBody AppScanningDTO dto){
        return tmsCustomerReservationPickUpService.appScanning(dto) ? R.ok() : R.failed("请扫描正确的包裹面单或者重新扫码");
    }

    /**
     * 取消客户预约单
     */
    @Operation(summary = "取消客户预约单" , description = "取消客户预约单" )
    @GetMapping("/cancel/{id}" )
    public R<Boolean> cancel(@PathVariable("id") Long id){
        TmsCustomerReservationPickUpEntity update = tmsCustomerReservationPickUpService.getById(id);
        update.setStatus(CustomerPickUpOrderStatusEnum.CANCEL.getCode());
        return R.ok(tmsCustomerReservationPickUpService.updateById(update));
    }

    /**
     * app揽收任务-地图标点
     */
    @Operation(summary = "app揽收任务-地图标点" , description = "app揽收任务-地图标点" )
    @PostMapping("/mapLabel" )
    public R<List<TmsCustomerReservationPickUpVo>> mapLabel(@RequestBody QueryCondition<TmsCustomerReservationPickUpQbo> qbo) {
        return R.ok(tmsCustomerReservationPickUpService.mapLabel(qbo));
    }


}