package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsRoutePlanFailRecordEntity;
import com.jygjexp.jynx.tms.service.TmsRoutePlanFailRecordService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 路径规划失败记录表
 *
 * <AUTHOR>
 * @date 2025-03-24 11:43:06
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsRoutePlanFailRecord" )
@Tag(description = "tmsRoutePlanFailRecord" , name = "路径规划失败记录表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsRoutePlanFailRecordController {

    private final  TmsRoutePlanFailRecordService tmsRoutePlanFailRecordService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsRoutePlanFailRecord 路径规划失败记录表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsRoutePlanFailRecord_view')" )
    public R getTmsRoutePlanFailRecordPage(@ParameterObject Page page, @ParameterObject TmsRoutePlanFailRecordEntity tmsRoutePlanFailRecord) {
        LambdaQueryWrapper<TmsRoutePlanFailRecordEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsRoutePlanFailRecordService.page(page, wrapper));
    }


    /**
     * 通过id查询路径规划失败记录表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsRoutePlanFailRecord_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsRoutePlanFailRecordService.getById(id));
    }

    /**
     * 新增路径规划失败记录表
     * @param tmsRoutePlanFailRecord 路径规划失败记录表
     * @return R
     */
    @Operation(summary = "新增路径规划失败记录表" , description = "新增路径规划失败记录表" )
    @SysLog("新增路径规划失败记录表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsRoutePlanFailRecord_add')" )
    public R save(@RequestBody TmsRoutePlanFailRecordEntity tmsRoutePlanFailRecord) {
        return R.ok(tmsRoutePlanFailRecordService.save(tmsRoutePlanFailRecord));
    }

    /**
     * 修改路径规划失败记录表
     * @param tmsRoutePlanFailRecord 路径规划失败记录表
     * @return R
     */
    @Operation(summary = "修改路径规划失败记录表" , description = "修改路径规划失败记录表" )
    @SysLog("修改路径规划失败记录表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsRoutePlanFailRecord_edit')" )
    public R updateById(@RequestBody TmsRoutePlanFailRecordEntity tmsRoutePlanFailRecord) {
        return R.ok(tmsRoutePlanFailRecordService.updateById(tmsRoutePlanFailRecord));
    }

    /**
     * 通过id删除路径规划失败记录表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除路径规划失败记录表" , description = "通过id删除路径规划失败记录表" )
    @SysLog("通过id删除路径规划失败记录表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsRoutePlanFailRecord_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsRoutePlanFailRecordService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsRoutePlanFailRecord 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsRoutePlanFailRecord_export')" )
    public List<TmsRoutePlanFailRecordEntity> export(TmsRoutePlanFailRecordEntity tmsRoutePlanFailRecord,Long[] ids) {
        return tmsRoutePlanFailRecordService.list(Wrappers.lambdaQuery(tmsRoutePlanFailRecord).in(ArrayUtil.isNotEmpty(ids), TmsRoutePlanFailRecordEntity::getId, ids));
    }
}