package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsVehicleDriverRelationEntity;
import com.jygjexp.jynx.tms.service.TmsVehicleDriverRelationService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 车辆司机关联中间表
 *
 * <AUTHOR>
 * @date 2025-05-13 11:31:35
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsVehicleDriverRelation" )
@Tag(description = "tmsVehicleDriverRelation" , name = "车辆司机关联中间表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsVehicleDriverRelationController {

    private final  TmsVehicleDriverRelationService tmsVehicleDriverRelationService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsVehicleDriverRelation 车辆司机关联中间表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsVehicleDriverRelation_view')" )
    public R getTmsVehicleDriverRelationPage(@ParameterObject Page page, @ParameterObject TmsVehicleDriverRelationEntity tmsVehicleDriverRelation) {
        LambdaQueryWrapper<TmsVehicleDriverRelationEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsVehicleDriverRelationService.page(page, wrapper));
    }


    /**
     * 通过id查询车辆司机关联中间表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsVehicleDriverRelation_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsVehicleDriverRelationService.getById(id));
    }

    /**
     * 新增车辆司机关联中间表
     * @param tmsVehicleDriverRelation 车辆司机关联中间表
     * @return R
     */
    @Operation(summary = "新增车辆司机关联中间表" , description = "新增车辆司机关联中间表" )
    @SysLog("新增车辆司机关联中间表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsVehicleDriverRelation_add')" )
    public R save(@RequestBody TmsVehicleDriverRelationEntity tmsVehicleDriverRelation) {
        return R.ok(tmsVehicleDriverRelationService.save(tmsVehicleDriverRelation));
    }

    /**
     * 修改车辆司机关联中间表
     * @param tmsVehicleDriverRelation 车辆司机关联中间表
     * @return R
     */
    @Operation(summary = "修改车辆司机关联中间表" , description = "修改车辆司机关联中间表" )
    @SysLog("修改车辆司机关联中间表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsVehicleDriverRelation_edit')" )
    public R updateById(@RequestBody TmsVehicleDriverRelationEntity tmsVehicleDriverRelation) {
        return R.ok(tmsVehicleDriverRelationService.updateById(tmsVehicleDriverRelation));
    }

    /**
     * 通过id删除车辆司机关联中间表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除车辆司机关联中间表" , description = "通过id删除车辆司机关联中间表" )
    @SysLog("通过id删除车辆司机关联中间表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsVehicleDriverRelation_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsVehicleDriverRelationService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsVehicleDriverRelation 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsVehicleDriverRelation_export')" )
    public List<TmsVehicleDriverRelationEntity> export(TmsVehicleDriverRelationEntity tmsVehicleDriverRelation,Long[] ids) {
        return tmsVehicleDriverRelationService.list(Wrappers.lambdaQuery(tmsVehicleDriverRelation).in(ArrayUtil.isNotEmpty(ids), TmsVehicleDriverRelationEntity::getId, ids));
    }
}