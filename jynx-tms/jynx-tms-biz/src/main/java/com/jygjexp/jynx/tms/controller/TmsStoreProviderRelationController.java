package com.jygjexp.jynx.tms.controller;


import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;


/**
 * 门店快递订单供应商
 *
 * <AUTHOR>
 * @date 2025-07-16 20:24:32
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsStoreProviderRelation" )
@Tag(description = "tmsStoreProviderRelation" , name = "门店快递订单供应商管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsStoreProviderRelationController {

}
