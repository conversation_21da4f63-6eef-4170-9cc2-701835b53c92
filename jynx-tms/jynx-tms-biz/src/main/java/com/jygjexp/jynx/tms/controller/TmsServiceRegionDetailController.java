package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsServiceProviderEntity;
import com.jygjexp.jynx.tms.entity.TmsServiceRegionDetailEntity;
import com.jygjexp.jynx.tms.entity.TmsServiceRegionEntity;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.TmsServiceRegionDetailService;
import com.jygjexp.jynx.tms.vo.TmsServiceReginDetailPageVo;
import com.jygjexp.jynx.tms.vo.excel.TmsServiceReginDetaiUnreachabllExcelVo;
import com.jygjexp.jynx.tms.vo.excel.TmsServiceReginDetailExcelVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.LinkedHashMap;

/**
 * 服务商邮编配置子表（分区明细）
 *
 * <AUTHOR>
 * @date 2025-07-10 14:49:36
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsServiceRegionDetail" )
@Tag(description = "tmsServiceRegionDetail" , name = "服务商邮编配置子表（分区明细）管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsServiceRegionDetailController {

    private final  TmsServiceRegionDetailService tmsServiceRegionDetailService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param vo 服务商邮编配置子表（分区明细）
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsServiceRegionDetail_view')" )
    public R getTmsServiceRegionDetailPage(@ParameterObject Page page, @ParameterObject TmsServiceReginDetailPageVo vo) {
        return R.ok(tmsServiceRegionDetailService.search(page, vo));
    }


    /**
     * 通过id查询服务商邮编配置子表（分区明细）
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsServiceRegionDetail_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsServiceRegionDetailService.getById(id));
    }

    /**
     * 新增服务商邮编配置子表（分区明细）
     * @param tmsServiceRegionDetail 服务商邮编配置子表（分区明细）
     * @return R
     */
    @Operation(summary = "新增服务商邮编配置子表（分区明细）" , description = "新增服务商邮编配置子表（分区明细）" )
    @SysLog("新增服务商邮编配置子表（分区明细）" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsServiceRegionDetail_add')" )
    public R save(@RequestBody TmsServiceRegionDetailEntity tmsServiceRegionDetail) {
        if (!checkName(tmsServiceRegionDetail)) {
            return LocalizedR.failed("tms.serviceProvider.postalCode.dest.exists",tmsServiceRegionDetail.getPostalCodeStart()+"-"+tmsServiceRegionDetail.getPostalCodeEnd());
        }

/*        if (!shipperCheckName(tmsServiceRegionDetail)) {
            return LocalizedR.failed("tms.serviceProvider.postalCode.shipper.exists",tmsServiceRegionDetail.getShipperPostalCodeStart()+"-"+tmsServiceRegionDetail.getShipperPostalCodeEnd());
        }*/
        return R.ok(tmsServiceRegionDetailService.save(tmsServiceRegionDetail));
    }

    /**
     * 修改服务商邮编配置子表（分区明细）
     * @param tmsServiceRegionDetail 服务商邮编配置子表（分区明细）
     * @return R
     */
    @Operation(summary = "修改服务商邮编配置子表（分区明细）" , description = "修改服务商邮编配置子表（分区明细）" )
    @SysLog("修改服务商邮编配置子表（分区明细）" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsServiceRegionDetail_edit')" )
    public R updateById(@RequestBody TmsServiceRegionDetailEntity tmsServiceRegionDetail) {
        if (!checkName(tmsServiceRegionDetail)) {
            return LocalizedR.failed("tms.serviceProvider.postalCode.dest.exists",tmsServiceRegionDetail.getPostalCodeStart()+"-"+tmsServiceRegionDetail.getPostalCodeEnd());
        }

/*        if (!shipperCheckName(tmsServiceRegionDetail)) {
            return LocalizedR.failed("tms.serviceProvider.postalCode.shipper.exists",tmsServiceRegionDetail.getShipperPostalCodeStart()+"-"+tmsServiceRegionDetail.getShipperPostalCodeEnd());
        }*/
        return R.ok(tmsServiceRegionDetailService.updateById(tmsServiceRegionDetail));
    }

    /**
     * 通过id删除服务商邮编配置子表（分区明细）
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除服务商邮编配置子表（分区明细）" , description = "通过id删除服务商邮编配置子表（分区明细）" )
    @SysLog("通过id删除服务商邮编配置子表（分区明细）" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsServiceRegionDetail_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsServiceRegionDetailService.removeBatchByIds(CollUtil.toList(ids)));
    }

    // 判断目的地邮编分区邮编起始-结束区间是否重复
    public Boolean checkName(TmsServiceRegionDetailEntity tmsServiceRegionDetail) {
        LambdaQueryWrapper<TmsServiceRegionDetailEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper
                .eq(TmsServiceRegionDetailEntity::getPostalCodeStart, tmsServiceRegionDetail.getPostalCodeStart())
                .eq(TmsServiceRegionDetailEntity::getPostalCodeEnd, tmsServiceRegionDetail.getPostalCodeEnd())
                .eq(TmsServiceRegionDetailEntity::getShipperPostalCodeStart, tmsServiceRegionDetail.getShipperPostalCodeStart())
                .eq(TmsServiceRegionDetailEntity::getShipperPostalCodeEnd, tmsServiceRegionDetail.getShipperPostalCodeEnd())
                .eq(TmsServiceRegionDetailEntity::getRegionId,tmsServiceRegionDetail.getRegionId())
                .last("limit 1");
        queryWrapper.ne(ObjectUtil.isNotNull(tmsServiceRegionDetail.getId()),TmsServiceRegionDetailEntity::getId, tmsServiceRegionDetail.getId());
        TmsServiceRegionDetailEntity byName = tmsServiceRegionDetailService.getOne(queryWrapper,false);
        if (Objects.nonNull(byName)) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }


    /**
     * 查询可达分区下的所有邮编分区
     * @param reachableRegionId  可达分区id
     * @return
     */
    @Operation(summary = "查询可达分区下的所有邮编分区" , description = "查询可达分区下的所有邮编分区" )
    @GetMapping("/reachableRegion/detail")
    public R reachableRegion(@RequestParam("reachableRegionId") Long reachableRegionId) {
        List<TmsServiceRegionDetailEntity> fullList = tmsServiceRegionDetailService.list(
                new LambdaQueryWrapper<TmsServiceRegionDetailEntity>()
                        .select(TmsServiceRegionDetailEntity::getPostalName, TmsServiceRegionDetailEntity::getId)
                        .eq(TmsServiceRegionDetailEntity::getRegionId, reachableRegionId)
                        .orderByAsc(TmsServiceRegionDetailEntity::getCreateTime)
        );
        // 用 Map 对 postalName 去重，保留第一次出现的那条记录
        Map<String, TmsServiceRegionDetailEntity> uniqueMap = fullList.stream()
                .filter(e -> StrUtil.isNotBlank(e.getPostalName()))
                .collect(Collectors.toMap(
                        TmsServiceRegionDetailEntity::getPostalName,
                        Function.identity(),
                        (existing, replacement) -> existing, // 忽略重复
                        LinkedHashMap::new
                ));
        return R.ok(new ArrayList<>(uniqueMap.values()));
    }

    /**
     * 导入可达分区邮编配置
     * @param file
     * @return
     */
    @Operation(summary = "导入可达分区邮编配置" , description = "导入可达分区邮编配置" )
    @SysLog("导入可达分区邮编配置" )
    @PostMapping("/reachableRegion/import")
    @PreAuthorize("@pms.hasPermission('tms_reachableRegion_import')")
    public R reachableRegionImport(@RequestParam("file") MultipartFile file, @RequestParam("regionId") Long regionId) {
        return tmsServiceRegionDetailService.reachableRegionImport(file,regionId);
    }

    /**
     * 导入不可达分区邮编配置
     * @param file
     * @return
     */
    @Operation(summary = "导入不可达分区邮编配置" , description = "导入不可达分区邮编配置" )
    @SysLog("导入不可达分区邮编配置" )
    @PostMapping("/unreachableRegion/import")
    @PreAuthorize("@pms.hasPermission('tms_unreachableRegion_import')")
    public R unreachableRegionImport(@RequestParam("file") MultipartFile file,@RequestParam("regionId") Long regionId) {
        return tmsServiceRegionDetailService.unreachableRegionImport(file,regionId);
    }


    /**
     * 导出可达分区excel 表格
     * @param tmsServiceRegionDetail 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流    unreachableRegionId   reachableRegionId
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_serviceRegion_export')" )
    public List<TmsServiceReginDetailExcelVo> export(TmsServiceReginDetailPageVo tmsServiceRegionDetail, Long[] ids) {
        return tmsServiceRegionDetailService.reachableRegionExport(tmsServiceRegionDetail, ids);
    }

    /**
     * 导出不可达分区excel 表格
     * @param tmsServiceRegionDetail 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流    unreachableRegionId   reachableRegionId
     */
    @ResponseExcel
    @GetMapping("/unreachableRegion/export")
    @PreAuthorize("@pms.hasPermission('tms_unreachableRegion_export')" )
    public List<TmsServiceReginDetaiUnreachabllExcelVo> unreachableRegionExport(TmsServiceReginDetailPageVo tmsServiceRegionDetail, Long[] ids) {
        return tmsServiceRegionDetailService.unreachableRegionExport(tmsServiceRegionDetail, ids);
    }
}