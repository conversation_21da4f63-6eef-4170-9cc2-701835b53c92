package com.jygjexp.jynx.tms.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.entity.TmsInventoryManagementEntity;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.TmsInventoryManagementService;
import com.jygjexp.jynx.tms.vo.TmsCustomerOrderPageVo;
import com.jygjexp.jynx.tms.vo.TmsInventoryManagementPageVo;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 库存管理
 *
 * <AUTHOR>
 * @date 2025-04-06 21:46:02
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsInventoryManagement" )
@Tag(description = "tmsInventoryManagement" , name = "库存管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsInventoryManagementController {

    private final  TmsInventoryManagementService tmsInventoryManagementService;

    /**
     * 分页查询库存列表
     * @param page 分页对象
     * @param entity 库存管理
     * @return
     */
    @Operation(summary = "分页查询库存列表" , description = "分页查询库存列表" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsInventoryManagement_view')" )
    public R getTmsInventoryManagementPage(@ParameterObject Page page, @ParameterObject TmsInventoryManagementPageVo entity) {
        return R.ok(tmsInventoryManagementService.search(page, entity));
    }


    @Operation(summary = "通过id查询库存详情" , description = "通过id查询库存详情" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsInventoryManagement_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsInventoryManagementService.selectById(id));
    }

    @Operation(summary = "仓库跟踪单列表" , description = "仓库跟踪单列表" )
    @GetMapping("/listWarehouseOrderPage" )
    public R listWarehouseOrderPage(@ParameterObject Page page, @ParameterObject TmsCustomerOrderPageVo vo) {
        return R.ok(tmsInventoryManagementService.getWarehouseOrderPage(page, vo));
    }

    @Operation(summary = "跟踪单库存详情", description = "跟踪单库存详情")
    @GetMapping("/getZDJOrderDetail")
    public R getZDJOrderDetail(@Parameter(description = "跟踪单号") @NotBlank String entrustedOrderNumber, @NotNull Long warehouseId) {
        return tmsInventoryManagementService.getZDJOrderDetail(entrustedOrderNumber, warehouseId);
    }

}