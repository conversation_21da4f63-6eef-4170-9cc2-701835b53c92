package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.dto.TmsAppExceptionUploadDto;
import com.jygjexp.jynx.tms.entity.TmsExceptionManagementEntity;
import com.jygjexp.jynx.tms.service.TmsExceptionManagementService;
import com.jygjexp.jynx.tms.vo.TmsExceptionManagementPageVo;
import com.jygjexp.jynx.tms.vo.app.TmsAppMessageVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * TMS异常管理表
 *
 * <AUTHOR>
 * @date 2025-03-04 15:31:27
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsException" )
@Tag(description = "tmsException" , name = "TMS异常管理表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsExceptionManagementController {

    private final  TmsExceptionManagementService tmsExceptionManagementService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param vo TMS异常管理表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsException_view')" )
    public R getTmsExceptionManagementPage(@ParameterObject Page page, @ParameterObject TmsExceptionManagementPageVo vo) {
        return R.ok(tmsExceptionManagementService.search(page, vo));
    }


    /**
     * 通过id查询TMS异常管理表
     * @param exceptionId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{exceptionId}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsException_view')" )
    public R getById(@PathVariable("exceptionId" ) Long exceptionId) {
        return R.ok(tmsExceptionManagementService.getById(exceptionId));
    }

    /**
     * 新增TMS异常管理表
     * @param tmsExceptionManagement TMS异常管理表
     * @return R
     */
    @Operation(summary = "新增TMS异常管理表" , description = "新增TMS异常管理表" )
    @SysLog("新增TMS异常管理表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsException_add')" )
    public R save(@RequestBody TmsExceptionManagementEntity tmsExceptionManagement) {
        return R.ok(tmsExceptionManagementService.save(tmsExceptionManagement));
    }

    /**
     * 修改TMS异常管理表
     * @param tmsExceptionManagement TMS异常管理表
     * @return R
     */
    @Operation(summary = "修改TMS异常管理表" , description = "修改TMS异常管理表" )
    @SysLog("修改TMS异常管理表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsException_edit')" )
    public R updateById(@RequestBody TmsExceptionManagementEntity tmsExceptionManagement) {
        return R.ok(tmsExceptionManagementService.updateById(tmsExceptionManagement));
    }

    /**
     * 通过id删除TMS异常管理表
     * @param ids exceptionId列表
     * @return R
     */
    @Operation(summary = "通过id删除TMS异常管理表" , description = "通过id删除TMS异常管理表" )
    @SysLog("通过id删除TMS异常管理表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsException_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsExceptionManagementService.removeBatchByIds(CollUtil.toList(ids)));
    }

//    /**
//     * 卡派异常处理
//     * @param exceptionId 异常id
//     * @param handlingPlan 处理方案
//     * @param handlingDescription 处理说明
//     * @return R
//     */
//    @Operation(summary = "异常处理" , description = "异常处理" )
//    @SysLog("异常处理" )
//    @PostMapping("/handling")
//    @PreAuthorize("@pms.hasPermission('tms_tmsException_handling')" )
//    public R handling(@RequestParam Long exceptionId,@RequestParam Integer handlingPlan,@RequestParam String handlingDescription) {
//        return tmsExceptionManagementService.handling(exceptionId,handlingPlan,handlingDescription);
//    }

    /**
     * 中大件异常处理
     * @param exceptionId 异常id
     * @param handlingPlan 处理方案
     * @param handlingDescription 处理说明
     * @return R
     */
    @Operation(summary = "中大件异常处理" , description = "中大件异常处理" )
    @SysLog("中大件异常处理" )
    @PostMapping("/handling")
    @PreAuthorize("@pms.hasPermission('tms_tmsException_Lmdhandling')" )
    public R handling(@RequestParam Long exceptionId,@RequestParam Integer handlingPlan,@RequestParam String handlingDescription) {
        return tmsExceptionManagementService.handling(exceptionId,handlingPlan,handlingDescription);
    }

    /**
     * 异常上报
     * @param tmsExceptionManagement 异常对象
     * @return R
     */
    @Operation(summary = "异常上报" , description = "异常上报" )
    @SysLog("异常上报" )
    @PostMapping("/reporting")
    public R reporting(@RequestBody TmsExceptionManagementEntity tmsExceptionManagement) {
        return tmsExceptionManagementService.reporting(tmsExceptionManagement);
    }


    /**
     * 轮询消息
     * @param userId 司机id
     * @return R
     */
    @Operation(summary = "轮询消息" , description = "轮询消息" )
    @GetMapping("/getMessages/{userId}")
    public R getMessages(@PathVariable("userId") Long userId) {
        return tmsExceptionManagementService.getMessages(userId);
    }

    /**
     * 批量处理消息
     * @param vo
     * @return R
     */
    @Operation(summary = "批量处理消息" , description = "批量处理消息" )
    @PostMapping("/disposeMessages")
    public R disposeMessages(@RequestBody TmsAppMessageVo vo) {
        return tmsExceptionManagementService.disposeMessages(vo);
    }

    /**
     * 根据调度单号查询异常详情信息
     * @param orderNo 调度单号
     * @return R
     */
    @Operation(summary = "根据调度单号查询异常详情信息" , description = "根据调度单号查询异常详情信息" )
    @GetMapping("/getListByDispatchOrderNo/{orderNo}")
    public R getListByDispatchOrderNo(@PathVariable("orderNo") String orderNo) {
        return tmsExceptionManagementService.getListByDispatchOrderNo(orderNo);
    }

    /**
     * 导出excel 表格
     * @param tmsExceptionManagement 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsException_export')" )
    public List<TmsExceptionManagementEntity> export(TmsExceptionManagementEntity tmsExceptionManagement,Long[] ids) {
        return tmsExceptionManagementService.list(Wrappers.lambdaQuery(tmsExceptionManagement).in(ArrayUtil.isNotEmpty(ids), TmsExceptionManagementEntity::getExceptionId, ids));
    }
}