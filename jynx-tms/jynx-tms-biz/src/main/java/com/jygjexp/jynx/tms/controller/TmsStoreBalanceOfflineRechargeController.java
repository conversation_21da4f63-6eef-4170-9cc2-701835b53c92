package com.jygjexp.jynx.tms.controller;

import com.baomidou.lock.annotation.Lock4j;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import com.jygjexp.jynx.tms.dto.TmsStoreBalanceOfflineRechargeExportDto;
import com.jygjexp.jynx.tms.service.TmsStoreBalanceOfflineRechargeService;
import com.jygjexp.jynx.tms.vo.TmsStoreBalanceOfflineRechargePageVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 手工调账
 *
 * <AUTHOR>
 * @date 2025-07-30 03:25:12
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/store/balance/offline/recharge")
@Tag(description = "tmsStoreBalanceOfflineRecharge", name = "手工调账管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsStoreBalanceOfflineRechargeController {

    private final TmsStoreBalanceOfflineRechargeService tmsStoreBalanceOfflineRechargeService;

    /**
     * 分页查询
     *
     * @param vo 分页对象
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @PostMapping("/search")
    @PreAuthorize("@pms.hasPermission('OfflineRecharge_view')")
    public R search(@RequestBody @Valid TmsStoreBalanceOfflineRechargePageVo vo) {
        return R.ok(tmsStoreBalanceOfflineRechargeService.search(vo));
    }

    /**
     * 通过id查询手工调账
     *
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询", description = "通过id查询")
    @GetMapping("/{id}")
    @PreAuthorize("@pms.hasPermission('OfflineRecharge_view')")
    public R getById(@PathVariable("id") Long id) {
        return R.ok(tmsStoreBalanceOfflineRechargeService.getById(id));
    }

    /**
     * 审批
     *
     * @param id
     * @return
     */
    @Lock4j(keys = {"#id"}, expire = 5000, acquireTimeout = 3000)
    @Operation(summary = "审批")
    @PutMapping("/{id}/verify")
    @PreAuthorize("@pms.hasPermission('OfflineRecharge_verfiy')")
    public R<Boolean> verify(@PathVariable Long id,
                             @ParameterObject @Parameter(description = "审批备注") String verifyRemarks) {
        return R.ok(tmsStoreBalanceOfflineRechargeService.verify(id, verifyRemarks));
    }

    /**
     * 驳回
     *
     * @param id
     * @param verifyRemarks
     * @return
     */
    @Lock4j(keys = {"#id"}, expire = 5000, acquireTimeout = 3000)
    @Operation(summary = "驳回")
    @PutMapping("/{id}/reject")
    @PreAuthorize("@pms.hasPermission('OfflineRecharge_reject')")
    public R<Boolean> reject(@PathVariable Long id,
                             @ParameterObject @Valid @NotBlank @Parameter(description = "审批备注") String verifyRemarks) {
        return R.ok(tmsStoreBalanceOfflineRechargeService.reject(id, verifyRemarks));
    }

    /**
     * 导出excel 表格
     *
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @PostMapping("/export")
    @PreAuthorize("@pms.hasPermission('OfflineRecharge_export')")
    public List<TmsStoreBalanceOfflineRechargeExportDto> export(@RequestBody TmsStoreBalanceOfflineRechargePageVo vo) {
        return tmsStoreBalanceOfflineRechargeService.export(vo);
    }
}