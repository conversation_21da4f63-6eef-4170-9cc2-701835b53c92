package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.tms.entity.TmsCarrierAuditRecordsEntity;
import com.jygjexp.jynx.tms.service.TmsCarrierAuditRecordsService;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Objects;

/**
 * 卡派-承运商审核记录
 *
 * <AUTHOR>
 * @date 2025-03-14 16:29:42
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsCarrierAuditRecords" )
@Tag(description = "tmsCarrierAuditRecords" , name = "卡派-承运商审核记录管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsCarrierAuditRecordsController {

    private final  TmsCarrierAuditRecordsService tmsCarrierAuditRecordsService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsCarrierAuditRecords 卡派-承运商审核记录
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsCarrierAuditRecords_view')" )
    public R getTmsCarrierAuditRecordsPage(@ParameterObject Page page, @ParameterObject TmsCarrierAuditRecordsEntity tmsCarrierAuditRecords) {
        LambdaQueryWrapper<TmsCarrierAuditRecordsEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsCarrierAuditRecordsService.page(page, wrapper));
    }


    /**
     * 通过id查询卡派-承运商审核记录
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsCarrierAuditRecords_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsCarrierAuditRecordsService.getById(id));
    }

    /**
     * 新增卡派-承运商审核记录
     * @param tmsCarrierAuditRecords 卡派-承运商审核记录
     * @return R
     */
    @Operation(summary = "新增卡派-承运商审核记录" , description = "新增卡派-承运商审核记录" )
    @SysLog("新增卡派-承运商审核记录" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsCarrierAuditRecords_add')" )
    public R save(@RequestBody TmsCarrierAuditRecordsEntity tmsCarrierAuditRecords) {
        return R.ok(tmsCarrierAuditRecordsService.save(tmsCarrierAuditRecords));
    }

    /**
     * 修改卡派-承运商审核记录
     * @param tmsCarrierAuditRecords 卡派-承运商审核记录
     * @return R
     */
    @Operation(summary = "修改卡派-承运商审核记录" , description = "修改卡派-承运商审核记录" )
    @SysLog("修改卡派-承运商审核记录" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsCarrierAuditRecords_edit')" )
    public R updateById(@RequestBody TmsCarrierAuditRecordsEntity tmsCarrierAuditRecords) {
        return R.ok(tmsCarrierAuditRecordsService.updateById(tmsCarrierAuditRecords));
    }

    /**
     * 通过id删除卡派-承运商审核记录
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除卡派-承运商审核记录" , description = "通过id删除卡派-承运商审核记录" )
    @SysLog("通过id删除卡派-承运商审核记录" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsCarrierAuditRecords_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsCarrierAuditRecordsService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsCarrierAuditRecords 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsCarrierAuditRecords_export')" )
    public List<TmsCarrierAuditRecordsEntity> export(TmsCarrierAuditRecordsEntity tmsCarrierAuditRecords,Long[] ids) {
        return tmsCarrierAuditRecordsService.list(Wrappers.lambdaQuery(tmsCarrierAuditRecords).in(ArrayUtil.isNotEmpty(ids), TmsCarrierAuditRecordsEntity::getId, ids));
    }

    @Operation(summary = "按客户单号查询审核记录", description = "按客户单号查询审核记录")
    @PostMapping("/listByOrderNumber")
    public R<List<TmsCarrierAuditRecordsEntity>> listByOrderNumber(@Parameter(description = "客户单号") @NotBlank String customerOrderNumber,
                                                                   @Parameter(description = "委托单号") @NotBlank String entrustedOrderNumber) {
        return R.ok(tmsCarrierAuditRecordsService.listByOrderNumber(customerOrderNumber, entrustedOrderNumber));
    }

}