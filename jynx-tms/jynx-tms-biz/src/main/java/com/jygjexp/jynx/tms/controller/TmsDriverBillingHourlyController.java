package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsDriverBillingHourlyEntity;
import com.jygjexp.jynx.tms.service.TmsDriverBillingHourlyService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 司机计费模板-时薪配置表
 *
 * <AUTHOR>
 * @date 2025-07-21 18:58:43
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsDriverBillingHourly" )
@Tag(description = "tmsDriverBillingHourly" , name = "司机计费模板-时薪配置表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsDriverBillingHourlyController {

    private final  TmsDriverBillingHourlyService tmsDriverBillingHourlyService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsDriverBillingHourly 司机计费模板-时薪配置表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverBillingHourly_view')" )
    public R getTmsDriverBillingHourlyPage(@ParameterObject Page page, @ParameterObject TmsDriverBillingHourlyEntity tmsDriverBillingHourly) {
        LambdaQueryWrapper<TmsDriverBillingHourlyEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsDriverBillingHourlyService.page(page, wrapper));
    }


    /**
     * 通过id查询司机计费模板-时薪配置表
     * @param hourId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{hourId}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverBillingHourly_view')" )
    public R getById(@PathVariable("hourId" ) Long hourId) {
        return R.ok(tmsDriverBillingHourlyService.getById(hourId));
    }

    /**
     * 新增司机计费模板-时薪配置表
     * @param tmsDriverBillingHourly 司机计费模板-时薪配置表
     * @return R
     */
    @Operation(summary = "新增司机计费模板-时薪配置表" , description = "新增司机计费模板-时薪配置表" )
    @SysLog("新增司机计费模板-时薪配置表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverBillingHourly_add')" )
    public R save(@RequestBody TmsDriverBillingHourlyEntity tmsDriverBillingHourly) {
        return R.ok(tmsDriverBillingHourlyService.save(tmsDriverBillingHourly));
    }

    /**
     * 修改司机计费模板-时薪配置表
     * @param tmsDriverBillingHourly 司机计费模板-时薪配置表
     * @return R
     */
    @Operation(summary = "修改司机计费模板-时薪配置表" , description = "修改司机计费模板-时薪配置表" )
    @SysLog("修改司机计费模板-时薪配置表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverBillingHourly_edit')" )
    public R updateById(@RequestBody TmsDriverBillingHourlyEntity tmsDriverBillingHourly) {
        return R.ok(tmsDriverBillingHourlyService.updateById(tmsDriverBillingHourly));
    }

    /**
     * 通过id删除司机计费模板-时薪配置表
     * @param ids hourId列表
     * @return R
     */
    @Operation(summary = "通过id删除司机计费模板-时薪配置表" , description = "通过id删除司机计费模板-时薪配置表" )
    @SysLog("通过id删除司机计费模板-时薪配置表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverBillingHourly_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsDriverBillingHourlyService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsDriverBillingHourly 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverBillingHourly_export')" )
    public List<TmsDriverBillingHourlyEntity> export(TmsDriverBillingHourlyEntity tmsDriverBillingHourly,Long[] ids) {
        return tmsDriverBillingHourlyService.list(Wrappers.lambdaQuery(tmsDriverBillingHourly).in(ArrayUtil.isNotEmpty(ids), TmsDriverBillingHourlyEntity::getHourId, ids));
    }
}