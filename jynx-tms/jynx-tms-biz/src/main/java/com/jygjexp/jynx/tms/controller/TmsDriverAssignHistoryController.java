package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsDriverAssignHistoryEntity;
import com.jygjexp.jynx.tms.service.TmsDriverAssignHistoryService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 司机分配历史表
 *
 * <AUTHOR>
 * @date 2025-07-03 09:36:54
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsDriverAssignHistory" )
@Tag(description = "tmsDriverAssignHistory" , name = "司机分配历史表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsDriverAssignHistoryController {

    private final  TmsDriverAssignHistoryService tmsDriverAssignHistoryService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsDriverAssignHistory 司机分配历史表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverAssignHistory_view')" )
    public R getTmsDriverAssignHistoryPage(@ParameterObject Page page, @ParameterObject TmsDriverAssignHistoryEntity tmsDriverAssignHistory) {
        LambdaQueryWrapper<TmsDriverAssignHistoryEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsDriverAssignHistoryService.page(page, wrapper));
    }


    /**
     * 通过id查询司机分配历史表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverAssignHistory_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsDriverAssignHistoryService.getById(id));
    }

    /**
     * 新增司机分配历史表
     * @param tmsDriverAssignHistory 司机分配历史表
     * @return R
     */
    @Operation(summary = "新增司机分配历史表" , description = "新增司机分配历史表" )
    @SysLog("新增司机分配历史表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverAssignHistory_add')" )
    public R save(@RequestBody TmsDriverAssignHistoryEntity tmsDriverAssignHistory) {
        return R.ok(tmsDriverAssignHistoryService.save(tmsDriverAssignHistory));
    }

    /**
     * 修改司机分配历史表
     * @param tmsDriverAssignHistory 司机分配历史表
     * @return R
     */
    @Operation(summary = "修改司机分配历史表" , description = "修改司机分配历史表" )
    @SysLog("修改司机分配历史表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverAssignHistory_edit')" )
    public R updateById(@RequestBody TmsDriverAssignHistoryEntity tmsDriverAssignHistory) {
        return R.ok(tmsDriverAssignHistoryService.updateById(tmsDriverAssignHistory));
    }

    /**
     * 通过id删除司机分配历史表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除司机分配历史表" , description = "通过id删除司机分配历史表" )
    @SysLog("通过id删除司机分配历史表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverAssignHistory_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsDriverAssignHistoryService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsDriverAssignHistory 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverAssignHistory_export')" )
    public List<TmsDriverAssignHistoryEntity> export(TmsDriverAssignHistoryEntity tmsDriverAssignHistory,Long[] ids) {
        return tmsDriverAssignHistoryService.list(Wrappers.lambdaQuery(tmsDriverAssignHistory).in(ArrayUtil.isNotEmpty(ids), TmsDriverAssignHistoryEntity::getId, ids));
    }
}