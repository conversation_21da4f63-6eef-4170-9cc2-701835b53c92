package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsDriverBillingTemplateEntity;
import com.jygjexp.jynx.tms.entity.TmsFeeRuleEntity;
import com.jygjexp.jynx.tms.service.TmsDriverBillingTemplateService;
import com.jygjexp.jynx.tms.vo.TmsDriverBillingTemplateDetailVo;
import com.jygjexp.jynx.tms.vo.TmsDriverBillingTemplatePageVo;
import com.jygjexp.jynx.tms.vo.TmsDriverBillingTemplateRequestVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

/**
 * 司机计费模板主表
 *
 * <AUTHOR>
 * @date 2025-07-21 18:58:15
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsDriverBillingTemplate" )
@Tag(description = "tmsDriverBillingTemplate" , name = "司机计费模板主表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsDriverBillingTemplateController {

    private final  TmsDriverBillingTemplateService tmsDriverBillingTemplateService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsDriverBillingTemplate 司机计费模板主表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverBillingTemplate_view')" )
    public R getTmsDriverBillingTemplatePage(@ParameterObject Page page, @ParameterObject TmsDriverBillingTemplatePageVo tmsDriverBillingTemplate) {
        return R.ok(tmsDriverBillingTemplateService.search(page, tmsDriverBillingTemplate));
    }


    /**
     * 通过id查询司机计费模板详情（包含配置信息）
     * @param id 模板ID
     * @return R
     */
    @Operation(summary = "查询司机计费模板详情" , description = "查询司机计费模板详情（包含配置信息）" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverBillingTemplate_view')" )
    public R<TmsDriverBillingTemplateDetailVo> getTemplateDetail(@PathVariable("id") Long id) {
        return tmsDriverBillingTemplateService.getTemplateDetail(id);
    }

    /**
     * 新增司机计费模板（包含配置信息）
     * @param requestVo 司机计费模板请求参数
     * @return R
     */
    @Operation(summary = "新增司机计费模板" , description = "新增司机计费模板（包含配置信息）" )
    @SysLog("新增司机计费模板" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverBillingTemplate_add')" )
    public R saveTemplate(@Valid @RequestBody TmsDriverBillingTemplateRequestVo requestVo) {
        return tmsDriverBillingTemplateService.saveTemplate(requestVo);
    }

    /**
     * 修改司机计费模板（包含配置信息）
     * @param requestVo 司机计费模板请求参数
     * @return R
     */
    @Operation(summary = "修改司机计费模板" , description = "修改司机计费模板（包含配置信息）" )
    @SysLog("修改司机计费模板" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverBillingTemplate_edit')" )
    public R updateTemplate(@Valid @RequestBody TmsDriverBillingTemplateRequestVo requestVo) {
        return tmsDriverBillingTemplateService.updateTemplate(requestVo);
    }

    /**
     * 删除司机计费模板（级联删除配置信息）
     * @param id 模板ID
     * @return R
     */
    @Operation(summary = "删除司机计费模板" , description = "删除司机计费模板（级联删除配置信息）" )
    @SysLog("删除司机计费模板" )
    @DeleteMapping("/{id}")
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverBillingTemplate_del')" )
    public R deleteTemplate(@PathVariable("id") Long id) {
        return tmsDriverBillingTemplateService.deleteTemplate(id);
    }

    /**
     * 批量删除司机计费模板（级联删除配置信息）
     * @param ids 模板ID列表
     * @return R
     */
    @Operation(summary = "批量删除司机计费模板" , description = "批量删除司机计费模板（级联删除配置信息）" )
    @SysLog("批量删除司机计费模板" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverBillingTemplate_del')" )
    public R deleteTemplates(@RequestBody Long[] ids) {
        return tmsDriverBillingTemplateService.deleteTemplates(ids);
    }

    /**
     * 编辑启用停用
     * @param vo 司机计费模版
     * @return R
     */
    @Operation(summary = "编辑启用停用" , description = "编辑启用停用" )
    @PutMapping("/updateIsValidById")
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverBillingTemplate_isValid')" )
    public R updateIsValidById(@RequestBody TmsDriverBillingTemplateEntity vo) {
        return R.ok(tmsDriverBillingTemplateService.updateById(vo));
    }

    /**
     * 导出excel 表格
     * @param tmsDriverBillingTemplate 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
/*    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverBillingTemplate_export')" )
    public List<TmsDriverBillingTemplateEntity> export(TmsDriverBillingTemplateEntity tmsDriverBillingTemplate,Long[] ids) {
        return tmsDriverBillingTemplateService.list(Wrappers.lambdaQuery(tmsDriverBillingTemplate).in(ArrayUtil.isNotEmpty(ids), TmsDriverBillingTemplateEntity::getId, ids));
    }*/
}