package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsBasicFreightPkgDetailEntity;
import com.jygjexp.jynx.tms.service.TmsBasicFreightPkgDetailService;
import com.jygjexp.jynx.tms.vo.TmsBasicFreightPkgDetailAddVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 包裹基础运费表-模版明细
 *
 * <AUTHOR>
 * @date 2025-03-07 14:50:41
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsBasicFreightPkgDetail" )
@Tag(description = "tmsBasicFreightPkgDetail" , name = "包裹基础运费表-模版明细管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsBasicFreightPkgDetailController {

    private final  TmsBasicFreightPkgDetailService tmsBasicFreightPkgDetailService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsBasicFreightPkgDetail 包裹基础运费表-模版明细
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_pkgDetail_view')" )
    public R getTmsBasicFreightPkgDetailPage(@ParameterObject Page page, @ParameterObject TmsBasicFreightPkgDetailEntity tmsBasicFreightPkgDetail) {
        LambdaQueryWrapper<TmsBasicFreightPkgDetailEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ObjectUtil.isNotNull(tmsBasicFreightPkgDetail.getPkgFreId()), TmsBasicFreightPkgDetailEntity::getPkgFreId, tmsBasicFreightPkgDetail.getPkgFreId());
        return R.ok(tmsBasicFreightPkgDetailService.page(page, wrapper));
    }


    /**
     * 通过id查询包裹基础运费表-模版明细
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_pkgDetail_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsBasicFreightPkgDetailService.getById(id));
    }

    /**
     * 新增包裹基础运费表-模版明细
     * @param addVo 包裹基础运费表-模版明细
     * @return R
     */
    @Operation(summary = "新增包裹基础运费表-模版明细" , description = "新增包裹基础运费表-模版明细" )
    @SysLog("新增包裹基础运费表-模版明细" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_pkgDetail_add')" )
    public R save(@RequestBody List<TmsBasicFreightPkgDetailAddVo> addVo) {
        return tmsBasicFreightPkgDetailService.addDetail(addVo);
    }

    /**
     * 修改包裹基础运费表-模版明细
     * @param tmsBasicFreightPkgDetail 包裹基础运费表-模版明细
     * @return R
     */
    @Operation(summary = "修改包裹基础运费表-模版明细" , description = "修改包裹基础运费表-模版明细" )
    @SysLog("修改包裹基础运费表-模版明细" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_pkgDetail_edit')" )
    public R updateById(@RequestBody TmsBasicFreightPkgDetailEntity tmsBasicFreightPkgDetail) {
        return R.ok(tmsBasicFreightPkgDetailService.updateById(tmsBasicFreightPkgDetail));
    }

    /**
     * 通过id删除包裹基础运费表-模版明细
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除包裹基础运费表-模版明细" , description = "通过id删除包裹基础运费表-模版明细" )
    @SysLog("通过id删除包裹基础运费表-模版明细" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_pkgDetail_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsBasicFreightPkgDetailService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsBasicFreightPkgDetail 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_pkgDetail_export')" )
    public List<TmsBasicFreightPkgDetailEntity> export(TmsBasicFreightPkgDetailEntity tmsBasicFreightPkgDetail,Long[] ids) {
        return tmsBasicFreightPkgDetailService.list(Wrappers.lambdaQuery(tmsBasicFreightPkgDetail).in(ArrayUtil.isNotEmpty(ids), TmsBasicFreightPkgDetailEntity::getId, ids));
    }
}