package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.dto.TmsOrderBatchDto;
import com.jygjexp.jynx.tms.entity.TmsManualSortingRecordEntity;
import com.jygjexp.jynx.tms.service.TmsManualSortingRecordService;
import com.jygjexp.jynx.tms.service.TmsOrderBatchService;
import com.jygjexp.jynx.tms.vo.TmsManualSortingRecordPageVo;
import com.jygjexp.jynx.tms.vo.excel.TmsManualSortingRecordExcelVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 中大件人工分拣记录
 *
 * <AUTHOR>
 * @date 2025-04-23 17:59:39
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsManualSortingRecord" )
@Tag(description = "tmsManualSortingRecord" , name = "分拣管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsManualSortingRecordController {

    private final  TmsManualSortingRecordService tmsManualSortingRecordService;
    private final TmsOrderBatchService tmsOrderBatchService;

    /**
     * 分拣管理列表分页
     * @param page 分页对象
     * @param vo 人工分拣记录
     * @return
     */
    @Operation(summary = "分拣管理列表分页" , description = "分拣管理列表分页" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_manualSortingRecord_view')" )
    public R getTmsManualSortingRecordPage(@ParameterObject Page page, @ParameterObject TmsManualSortingRecordPageVo vo) {
        return R.ok(tmsManualSortingRecordService.search(page, vo));
    }


    /**
     * 通过id查询人工分拣记录
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_manualSortingRecord_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsManualSortingRecordService.getById(id));
    }

    /**
     * 新增人工分拣记录
     * @param tmsManualSortingRecord 人工分拣记录
     * @return R
     */
    @Operation(summary = "新增人工分拣记录" , description = "新增人工分拣记录" )
    @SysLog("新增人工分拣记录" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_manualSortingRecord_add')" )
    public R save(@RequestBody TmsManualSortingRecordEntity tmsManualSortingRecord) {
        return R.ok(tmsManualSortingRecordService.save(tmsManualSortingRecord));
    }

    /**
     * 修改人工分拣记录
     * @param tmsManualSortingRecord 人工分拣记录
     * @return R
     */
    @Operation(summary = "修改人工分拣记录" , description = "修改人工分拣记录" )
    @SysLog("修改人工分拣记录" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_manualSortingRecord_edit')" )
    public R updateById(@RequestBody TmsManualSortingRecordEntity tmsManualSortingRecord) {
        return R.ok(tmsManualSortingRecordService.updateById(tmsManualSortingRecord));
    }

    /**
     * 通过id删除人工分拣记录
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除人工分拣记录" , description = "通过id删除人工分拣记录" )
    @SysLog("通过id删除人工分拣记录" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_manualSortingRecord_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsManualSortingRecordService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsManualSortingRecord 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_manualSortingRecord_export')" )
    public List<TmsManualSortingRecordExcelVo> export(TmsManualSortingRecordPageVo tmsManualSortingRecord, Long[] ids) {
        return tmsManualSortingRecordService.getManualSortingRecordExcel(tmsManualSortingRecord, ids);
    }

    /**
     * 创建批次
     * @param dto
     * @return
     */
    @Operation(summary = "创建批次" , description = "创建批次" )
    @SysLog("创建批次" )
    @PostMapping("/addOrderBatch")
    @PreAuthorize("@pms.hasPermission('tms_orderBatch_add')" )
    public R addOrderBatch(@RequestBody TmsOrderBatchDto dto) {
        return R.ok(tmsOrderBatchService.addOrderBatch(dto));
    }

    /**
     * 分拣订单选择批次
     * @param dto
     * @return
     */
    @Operation(summary = "分拣订单选择批次" , description = "分拣订单选择批次" )
    @SysLog("分拣订单选择批次" )
    @PostMapping("/selectOrderBatch")
    @PreAuthorize("@pms.hasPermission('tms_orderBatch_select')" )
    public R selectOrderBatch(@RequestBody TmsOrderBatchDto dto) {
        return tmsOrderBatchService.selectOrderBatch(dto);
    }

}