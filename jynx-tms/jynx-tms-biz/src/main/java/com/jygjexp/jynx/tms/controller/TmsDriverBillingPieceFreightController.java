package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsDriverBillingPieceFreightEntity;
import com.jygjexp.jynx.tms.service.TmsDriverBillingPieceFreightService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 司机计件模板-运费配置子表
 *
 * <AUTHOR>
 * @date 2025-07-21 18:59:30
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsDriverBillingPieceFreight" )
@Tag(description = "tmsDriverBillingPieceFreight" , name = "司机计件模板-运费配置子表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsDriverBillingPieceFreightController {

    private final  TmsDriverBillingPieceFreightService tmsDriverBillingPieceFreightService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsDriverBillingPieceFreight 司机计件模板-运费配置子表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverBillingPieceFreight_view')" )
    public R getTmsDriverBillingPieceFreightPage(@ParameterObject Page page, @ParameterObject TmsDriverBillingPieceFreightEntity tmsDriverBillingPieceFreight) {
        LambdaQueryWrapper<TmsDriverBillingPieceFreightEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsDriverBillingPieceFreightService.page(page, wrapper));
    }


    /**
     * 通过id查询司机计件模板-运费配置子表
     * @param freightId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{freightId}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverBillingPieceFreight_view')" )
    public R getById(@PathVariable("freightId" ) Long freightId) {
        return R.ok(tmsDriverBillingPieceFreightService.getById(freightId));
    }

    /**
     * 新增司机计件模板-运费配置子表
     * @param tmsDriverBillingPieceFreight 司机计件模板-运费配置子表
     * @return R
     */
    @Operation(summary = "新增司机计件模板-运费配置子表" , description = "新增司机计件模板-运费配置子表" )
    @SysLog("新增司机计件模板-运费配置子表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverBillingPieceFreight_add')" )
    public R save(@RequestBody TmsDriverBillingPieceFreightEntity tmsDriverBillingPieceFreight) {
        return R.ok(tmsDriverBillingPieceFreightService.save(tmsDriverBillingPieceFreight));
    }

    /**
     * 修改司机计件模板-运费配置子表
     * @param tmsDriverBillingPieceFreight 司机计件模板-运费配置子表
     * @return R
     */
    @Operation(summary = "修改司机计件模板-运费配置子表" , description = "修改司机计件模板-运费配置子表" )
    @SysLog("修改司机计件模板-运费配置子表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverBillingPieceFreight_edit')" )
    public R updateById(@RequestBody TmsDriverBillingPieceFreightEntity tmsDriverBillingPieceFreight) {
        return R.ok(tmsDriverBillingPieceFreightService.updateById(tmsDriverBillingPieceFreight));
    }

    /**
     * 通过id删除司机计件模板-运费配置子表
     * @param ids freightId列表
     * @return R
     */
    @Operation(summary = "通过id删除司机计件模板-运费配置子表" , description = "通过id删除司机计件模板-运费配置子表" )
    @SysLog("通过id删除司机计件模板-运费配置子表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverBillingPieceFreight_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsDriverBillingPieceFreightService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsDriverBillingPieceFreight 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverBillingPieceFreight_export')" )
    public List<TmsDriverBillingPieceFreightEntity> export(TmsDriverBillingPieceFreightEntity tmsDriverBillingPieceFreight,Long[] ids) {
        return tmsDriverBillingPieceFreightService.list(Wrappers.lambdaQuery(tmsDriverBillingPieceFreight).in(ArrayUtil.isNotEmpty(ids), TmsDriverBillingPieceFreightEntity::getFreightId, ids));
    }
}