package com.jygjexp.jynx.tms.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.dto.TemplateChangeRecordQueryDTO;
import com.jygjexp.jynx.tms.service.TmsSortingTemplateChangeRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 分拣模板变更记录
 *
 * <AUTHOR>
 * @date 2025-07-23 10:41:37
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsSortingTemplateChangeRecord" )
@Tag(description = "tmsSortingTemplateChangeRecord" , name = "分拣模板变更记录管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsSortingTemplateChangeRecordController {

    private final  TmsSortingTemplateChangeRecordService service;

    /**
     * 分页查询
     * @param page 分页对象
     * @param queryDTO 分拣模板变更记录 权限暂注
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    // @PreAuthorize("@pms.hasPermission('tms_templateChangeRecord_view')" )
    public R selectPage(@ParameterObject Page page, @ParameterObject TemplateChangeRecordQueryDTO queryDTO) {
        return R.ok(service.selectPage(page, queryDTO));
    }
}
