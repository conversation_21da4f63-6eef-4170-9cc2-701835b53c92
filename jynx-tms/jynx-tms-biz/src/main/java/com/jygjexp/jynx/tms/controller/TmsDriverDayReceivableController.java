package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.tms.entity.TmsDriverDayReceivableEntity;
import com.jygjexp.jynx.tms.service.TmsDriverDayReceivableService;
import com.jygjexp.jynx.tms.vo.TmsDriverDayReceivablePageVo;
import com.jygjexp.jynx.tms.vo.TmsDriverPieceCalculationRequestVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 司机计件每日应收费用表
 *
 * <AUTHOR>
 * @date 2025-07-22 17:00:53
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsDriverDayReceivable" )
@Tag(description = "司机计件每日应收费用表管理" , name = "司机计件每日应收费用表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsDriverDayReceivableController {

    private final TmsDriverDayReceivableService tmsDriverDayReceivableService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmeDriverDayReceivable 司机每日应收费用表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverDayReceivable_view')" )
    public R getTmeDriverDayReceivablePage(@ParameterObject Page page, @ParameterObject TmsDriverDayReceivablePageVo tmeDriverDayReceivable) {
        return R.ok(tmsDriverDayReceivableService.search(page, tmeDriverDayReceivable));
    }


    /**
     * 通过id查询司机每日应收费用表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverDayReceivable_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsDriverDayReceivableService.getById(id));
    }

    /**
     * 新增司机每日应收费用表
     * @param tmeDriverDayReceivable 司机每日应收费用表
     * @return R
     */
    @Operation(summary = "新增司机每日应收费用表" , description = "新增司机每日应收费用表" )
    @SysLog("新增司机每日应收费用表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverDayReceivable_add')" )
    public R save(@RequestBody TmsDriverDayReceivableEntity tmeDriverDayReceivable) {
        return R.ok(tmsDriverDayReceivableService.save(tmeDriverDayReceivable));
    }

    /**
     * 修改司机每日应收费用表
     * @param tmeDriverDayReceivable 司机每日应收费用表
     * @return R
     */
    @Operation(summary = "修改司机每日应收费用表" , description = "修改司机每日应收费用表" )
    @SysLog("修改司机每日应收费用表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverDayReceivable_edit')" )
    public R updateById(@RequestBody TmsDriverDayReceivableEntity tmeDriverDayReceivable) {
        return R.ok(tmsDriverDayReceivableService.updateById(tmeDriverDayReceivable));
    }

    /**
     * 通过id删除司机每日应收费用表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除司机每日应收费用表" , description = "通过id删除司机每日应收费用表" )
    @SysLog("通过id删除司机每日应收费用表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverDayReceivable_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsDriverDayReceivableService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmeDriverDayReceivable 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverDayReceivable_export')" )
    public List<TmsDriverDayReceivableEntity> export(TmsDriverDayReceivableEntity tmeDriverDayReceivable, Long[] ids) {
        return tmsDriverDayReceivableService.list(Wrappers.lambdaQuery(tmeDriverDayReceivable).in(ArrayUtil.isNotEmpty(ids), TmsDriverDayReceivableEntity::getId, ids));
    }

    /**
     * 计算司机计件模式下的每日应收费用
     * @param requestVo 计算请求参数
     * @return 计算结果
     */
    @Inner(value = false)
    @Operation(summary = "计算司机计件模式下的每日应收费用", description = "计算司机计件模式下的每日应收费用")
    @SysLog("计算司机计件模式下的每日应收费用")
    @PostMapping("/calculatePieceFee")
    public R calculatePieceFee(@Valid @RequestBody TmsDriverPieceCalculationRequestVo requestVo) {
        return tmsDriverDayReceivableService.calculateDriverPieceFee(requestVo);
    }
}