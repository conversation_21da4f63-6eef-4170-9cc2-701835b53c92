package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsStoreOrderExceptionEntity;
import com.jygjexp.jynx.tms.service.TmsStoreMessageTraceService;
import com.jygjexp.jynx.tms.service.TmsStoreOrderExceptionService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 快递订单异常处理表
 *
 * <AUTHOR>
 * @date 2025-07-21 11:47:35
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsStoreOrderException" )
@Tag(description = "tmsStoreOrderException" , name = "快递订单异常处理表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsStoreOrderExceptionController {

    private final TmsStoreOrderExceptionService tmsStoreOrderExceptionService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsStoreOrderException 快递订单异常处理表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreOrderException_view')" )
    public R getTmsStoreOrderExceptionPage(@ParameterObject Page page, @ParameterObject TmsStoreOrderExceptionEntity tmsStoreOrderException) {
        LambdaQueryWrapper<TmsStoreOrderExceptionEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsStoreOrderExceptionService.page(page, wrapper));
    }


    /**
     * 通过id查询快递订单异常处理表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreOrderException_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsStoreOrderExceptionService.getById(id));
    }

    /**
     * 新增快递订单异常处理表
     * @param tmsStoreOrderException 快递订单异常处理表
     * @return R
     */
    @Operation(summary = "新增快递订单异常处理表" , description = "新增快递订单异常处理表" )
    @SysLog("新增快递订单异常处理表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreOrderException_add')" )
    public R save(@RequestBody TmsStoreOrderExceptionEntity tmsStoreOrderException) {
        tmsStoreOrderExceptionService.saveStoreMessageTrace(tmsStoreOrderException);
        return R.ok(tmsStoreOrderExceptionService.save(tmsStoreOrderException));
    }

    /**
     * 修改快递订单异常处理表
     * @param tmsStoreOrderException 快递订单异常处理表
     * @return R
     */
    @Operation(summary = "修改快递订单异常处理表" , description = "修改快递订单异常处理表" )
    @SysLog("修改快递订单异常处理表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreOrderException_edit')" )
    public R updateById(@RequestBody TmsStoreOrderExceptionEntity tmsStoreOrderException) {
        return R.ok(tmsStoreOrderExceptionService.updateById(tmsStoreOrderException));
    }

    /**
     * 通过id删除快递订单异常处理表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除快递订单异常处理表" , description = "通过id删除快递订单异常处理表" )
    @SysLog("通过id删除快递订单异常处理表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreOrderException_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsStoreOrderExceptionService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsStoreOrderException 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreOrderException_export')" )
    public List<TmsStoreOrderExceptionEntity> export(TmsStoreOrderExceptionEntity tmsStoreOrderException,Long[] ids) {
        return tmsStoreOrderExceptionService.list(Wrappers.lambdaQuery(tmsStoreOrderException).in(ArrayUtil.isNotEmpty(ids), TmsStoreOrderExceptionEntity::getId, ids));
    }
}