package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsVehicleRouteTransitionEntity;
import com.jygjexp.jynx.tms.service.TmsVehicleRouteTransitionService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 车辆路线过渡段信息
 *
 * <AUTHOR>
 * @date 2025-03-17 15:40:58
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsVehicleRouteTransition" )
@Tag(description = "tmsVehicleRouteTransition" , name = "车辆路线过渡段信息管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsVehicleRouteTransitionController {

    private final  TmsVehicleRouteTransitionService tmsVehicleRouteTransitionService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsVehicleRouteTransition 车辆路线过渡段信息
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsVehicleRouteTransition_view')" )
    public R getTmsVehicleRouteTransitionPage(@ParameterObject Page page, @ParameterObject TmsVehicleRouteTransitionEntity tmsVehicleRouteTransition) {
        LambdaQueryWrapper<TmsVehicleRouteTransitionEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsVehicleRouteTransitionService.page(page, wrapper));
    }


    /**
     * 通过id查询车辆路线过渡段信息
     * @param vehicleRouteTransitionId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{vehicleRouteTransitionId}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsVehicleRouteTransition_view')" )
    public R getById(@PathVariable("vehicleRouteTransitionId" ) Long vehicleRouteTransitionId) {
        return R.ok(tmsVehicleRouteTransitionService.getById(vehicleRouteTransitionId));
    }

    /**
     * 新增车辆路线过渡段信息
     * @param tmsVehicleRouteTransition 车辆路线过渡段信息
     * @return R
     */
    @Operation(summary = "新增车辆路线过渡段信息" , description = "新增车辆路线过渡段信息" )
    @SysLog("新增车辆路线过渡段信息" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsVehicleRouteTransition_add')" )
    public R save(@RequestBody TmsVehicleRouteTransitionEntity tmsVehicleRouteTransition) {
        return R.ok(tmsVehicleRouteTransitionService.save(tmsVehicleRouteTransition));
    }

    /**
     * 修改车辆路线过渡段信息
     * @param tmsVehicleRouteTransition 车辆路线过渡段信息
     * @return R
     */
    @Operation(summary = "修改车辆路线过渡段信息" , description = "修改车辆路线过渡段信息" )
    @SysLog("修改车辆路线过渡段信息" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsVehicleRouteTransition_edit')" )
    public R updateById(@RequestBody TmsVehicleRouteTransitionEntity tmsVehicleRouteTransition) {
        return R.ok(tmsVehicleRouteTransitionService.updateById(tmsVehicleRouteTransition));
    }

    /**
     * 通过id删除车辆路线过渡段信息
     * @param ids vehicleRouteTransitionId列表
     * @return R
     */
    @Operation(summary = "通过id删除车辆路线过渡段信息" , description = "通过id删除车辆路线过渡段信息" )
    @SysLog("通过id删除车辆路线过渡段信息" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsVehicleRouteTransition_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsVehicleRouteTransitionService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsVehicleRouteTransition 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsVehicleRouteTransition_export')" )
    public List<TmsVehicleRouteTransitionEntity> export(TmsVehicleRouteTransitionEntity tmsVehicleRouteTransition,Long[] ids) {
        return tmsVehicleRouteTransitionService.list(Wrappers.lambdaQuery(tmsVehicleRouteTransition).in(ArrayUtil.isNotEmpty(ids), TmsVehicleRouteTransitionEntity::getVehicleRouteTransitionId, ids));
    }
}