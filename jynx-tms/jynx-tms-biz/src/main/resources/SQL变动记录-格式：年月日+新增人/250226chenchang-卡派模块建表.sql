    -- 备份仓库表
    DROP TABLE IF EXISTS tms_warehouse;
    CREATE TABLE `tms_warehouse`
    (
        `warehouse_id`   int                                                           NOT NULL AUTO_INCREMENT COMMENT '主键ID',
        `warehouse_code` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '仓库编号',
        `warehouse_name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '仓库名称',
        `address`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '地址',
        `contacts`       varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '联系人',
        `phone`          varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '联系电话',
        `is_valid`       tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
        `position_count` varchar(255)                                                  NOT NULL DEFAULT '0' COMMENT '库位数',
        `tenant_id`      bigint                                                        NOT NULL DEFAULT '1' COMMENT '租户号',
        `revision`       bigint                                                        NOT NULL DEFAULT '1' COMMENT '乐观锁',
        `remark`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
        `create_by`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '创建人',
        `create_time`    datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
        `update_by`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '更新人',
        `update_time`    datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
        `del_flag`       char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci               DEFAULT '0' COMMENT '删除标志',
        `country_name`   varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '国家',
        `province_name`  varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '省',
        `city_name`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '城市',
        `timezone`       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '所在时区',
        `business_hours` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '营业时间',
        `lat` double DEFAULT '0' COMMENT '纬度',
        `lng` double DEFAULT '0' COMMENT '经度',
        `postal_code`    varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '邮编',
        `country_id`     int                                                           NOT NULL COMMENT '国家id',
        `province_id`    int                                                           NOT NULL COMMENT '省id',
        `city_id`        int                                                           NOT NULL COMMENT '市/地区id',
        PRIMARY KEY (`warehouse_id`),
        UNIQUE KEY `warehouse_code_UNIQUE` (`warehouse_code`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='卡派仓库';

    -- 备份托盘表
    DROP TABLE IF EXISTS tms_tray;
    CREATE TABLE `tms_tray`
    (
        `id`                  bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
        `warehouse_id`        bigint unsigned NOT NULL DEFAULT '0' COMMENT '仓库id',
        `position_id`         bigint                                                        NOT NULL DEFAULT '0' COMMENT '库位 托盘位置',
        `history_position_id` bigint                                                        NOT NULL DEFAULT '0' COMMENT '库位托盘位置(历史记录)',
        `order_id_list`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单列表',
        `title`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
        `code`                varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '代码',
        `bag_count`           int                                                           NOT NULL DEFAULT '0' COMMENT '箱数',
        `status`              int                                                           NOT NULL DEFAULT '1' COMMENT '状态',
        `tenant_id`           bigint                                                        NOT NULL DEFAULT '1' COMMENT '租户号',
        `revision`            bigint                                                        NOT NULL DEFAULT '1' COMMENT '乐观锁',
        `remark`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
        `create_by`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '创建人',
        `create_time`         datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
        `update_by`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '更新人',
        `update_time`         datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
        `del_flag`            char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci               DEFAULT '0' COMMENT '删除标志',
        PRIMARY KEY (`id`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='卡派 托盘';

    -- 备份库位表
    CREATE TABLE `tms_position`
    (
        `id`              bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
        `warehouse_id`    bigint unsigned NOT NULL DEFAULT '0' COMMENT '仓库ID',
        `tray_id`         bigint                                                        NOT NULL DEFAULT '0' COMMENT '托盘ID',
        `title`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
        `code`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '代码',
        `position_column` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '位置列',
        `position_row`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '位置行',
        `status`          int                                                           NOT NULL DEFAULT '1' COMMENT '状态',
        `tenant_id`       bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户号',
        `revision`        int                                                           NOT NULL DEFAULT '0' COMMENT '乐观锁',
        `remark`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
        `create_by`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建人',
        `create_time`     datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
        `update_by`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '更新人',
        `update_time`     datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
        `del_flag`        char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL DEFAULT '0' COMMENT '删除标记',
        PRIMARY KEY (`id`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='卡派库位';

    -- 备份托盘调整表
    CREATE TABLE `tms_tray_adjust`
    (
        `id`                 bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
        `warehouse_id`       bigint unsigned NOT NULL DEFAULT '0' COMMENT '仓库id',
        `position_id_list`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '库位',
        `tray_id_list`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '托盘',
        `order_item_id_list` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单箱',
        `status`             int                                                           NOT NULL DEFAULT '0' COMMENT '状态',
        `execution_time`     datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '执行时间',
        `tenant_id`          bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户号',
        `revision`           int                                                           NOT NULL DEFAULT '0' COMMENT '乐观锁',
        `remark`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
        `create_by`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建人',
        `create_time`        datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
        `update_by`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '更新人',
        `update_time`        datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
        `del_flag`           char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL DEFAULT '0' COMMENT '删除标记',
        PRIMARY KEY (`id`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='卡派托盘调整';

    -- 备份提货单表
    CREATE TABLE `tms_waybill`
    (
        `id`                        bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
        `customer_id`               bigint unsigned NOT NULL DEFAULT '0' COMMENT '客户id',
        `warehouse_id`              bigint unsigned NOT NULL DEFAULT '0' COMMENT '仓库id',
        `service_provider_id`       bigint unsigned NOT NULL DEFAULT '0' COMMENT '服务商id',
        `business_category`         int                                                           NOT NULL DEFAULT '0' COMMENT '业务类型 1 空运 2 海运',
        `service_category_id_list`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '服务列表',
        `container_id`              bigint unsigned NOT NULL DEFAULT '0' COMMENT '货柜id',
        `business_number`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '业务编号',
        `waybill_number`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '提货单号',
        `transport_title`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '飞机名/船名',
        `transport_number`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '航班号/船次',
        `transport_company`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '航司/船司',
        `start_port`                varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '起始港',
        `end_port`                  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '目的港',
        `estimated_time_of_arrival` datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '预计到港时间',
        `container_size`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '板型/箱型',
        `container_number`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '板号/柜号',
        `item_count`                int                                                           NOT NULL DEFAULT '0' COMMENT '箱数',
        `operator_id`               bigint unsigned NOT NULL DEFAULT '0' COMMENT '操作人员',
        `business_id`               bigint unsigned NOT NULL DEFAULT '0' COMMENT '业务人员',
        `customer_service_id`       bigint unsigned NOT NULL DEFAULT '0' COMMENT '客服人员',
        `contract_id`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '合约号',
        `gross_weight`              decimal(18, 3)                                                NOT NULL DEFAULT '0.000' COMMENT '毛重',
        `gross_volume`              decimal(18, 2)                                                NOT NULL DEFAULT '0.00' COMMENT '体积',
        `tenant_id`                 bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户号',
        `revision`                  int                                                           NOT NULL DEFAULT '0' COMMENT '乐观锁',
        `remark`                    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
        `create_by`                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建人',
        `create_time`               datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
        `update_by`                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '更新人',
        `update_time`               datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
        `del_flag`                  char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL DEFAULT '0' COMMENT '删除标记',
        PRIMARY KEY (`id`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='卡派-提货单';

    -- 备份出仓单表
    CREATE TABLE `tms_outbound_order`
    (
        `id`                     bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
        `prep_outbound_order_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '预出仓单id',
        `out_warehouse_code`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '出仓编码',
        `warehouse_id`           bigint unsigned NOT NULL DEFAULT '0' COMMENT '仓库id',
        `consignee_id`           bigint unsigned NOT NULL DEFAULT '0' COMMENT '收件人id',
        `container_number`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '板号/柜号',
        `shipment_appointment`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收货码',
        `order_id_list`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单id',
        `fba_appointment_no`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'FBA预约编号',
        `tray_id_list`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '托盘列表',
        `position_id_list`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '库位列表',
        `item_id_list`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '箱袋列表',
        `total_weight`           decimal(18, 3)                                                NOT NULL DEFAULT '0.000' COMMENT '总重量',
        `total_volume`           decimal(18, 2)                                                NOT NULL DEFAULT '0.00' COMMENT '总体积',
        `out_time`               datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '预出仓时间',
        `status`                 int                                                           NOT NULL DEFAULT '0' COMMENT '状态',
        `car_number`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '车牌号',
        `truck_company`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '卡车公司',
        `driver`                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '司机',
        `estimated_arrival_time` datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '预计到达时间',
        `tenant_id`              bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户号',
        `revision`               int                                                           NOT NULL DEFAULT '0' COMMENT '乐观锁',
        `remark`                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
        `create_by`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建人',
        `create_time`            datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
        `update_by`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '更新人',
        `update_time`            datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
        `del_flag`               char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL DEFAULT '0' COMMENT '删除标记',
        PRIMARY KEY (`id`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='卡派 出仓单';

-- 创建客户(货主)表
CREATE TABLE `tms_customer`
(
    `id`                bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `customer_name`     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '客户名称',
    `customer_name_cn`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '客户名称 中文',
    `customer_code`     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '客户编码',
    `category`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '客户类型',
    `sector`            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '所属行业',
    `customer_level`    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '客户级别',
    `customer_label`    varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '客户标签',
    `region`            varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '区域',
    `company_address`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公司地址',
    `postal_code`       varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '地址邮编',
    `business_license`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '营业执照',
    `contact_person`    varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '联系人姓名',
    `phone`             varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '手机号',
    `department`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '部门',
    `position`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '职位',
    `email`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '邮箱',
    `warehouse_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '仓库地址',
    `count`             int                                                                    DEFAULT '0' COMMENT '单量',
    `token`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT 'token',
    `is_valid`          tinyint(1) NOT NULL DEFAULT '1' COMMENT '启用状态：0 禁用，1 启用',
    `tenant_id`         bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户号',
    `revision`          int                                                           NOT NULL DEFAULT '0' COMMENT '乐观锁',
    `remark`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '备注',
    `create_by`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '创建人',
    `create_time`       datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
    `update_by`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '更新人',
    `update_time`       datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
    `del_flag`          char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL DEFAULT '0' COMMENT '删除标记：0 未删除，1 已删除',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uk_customer_code` (`customer_code`) USING BTREE COMMENT '客户编码唯一索引',
    KEY                 `idx_phone` (`phone`) USING BTREE COMMENT '手机号索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='卡派-客户信息表';

    -- 备份货柜表
    CREATE TABLE `tms_container`
    (
        `id`                        bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
        `customer_id`               bigint unsigned NOT NULL DEFAULT '0' COMMENT '客户id',
        `warehouse_id`              bigint unsigned NOT NULL DEFAULT '0' COMMENT '仓库id',
        `service_provider_id`       bigint unsigned NOT NULL DEFAULT '0' COMMENT '服务商id',
        `business_category`         int                                                           NOT NULL DEFAULT '0' COMMENT '业务类型 1 空运 2 海运',
        `service_category_id_list`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '服务列表',
        `business_number`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '业务编号',
        `waybill_number_list`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '提货单号/主单号',
        `transport_title`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '飞机名/船名',
        `transport_number`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '航班号/船次',
        `transport_company`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '航司/船司',
        `start_port`                varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '起始港',
        `end_port`                  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '目的港',
        `estimated_time_of_arrival` datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '预计到港时间',
        `container_size`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '板型/箱型',
        `container_number`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '板号/柜号',
        `total_item_count`          int                                                           NOT NULL DEFAULT '0' COMMENT '总箱数',
        `operator_id`               bigint unsigned NOT NULL DEFAULT '0' COMMENT '操作人员',
        `business_id`               bigint unsigned NOT NULL DEFAULT '0' COMMENT '业务人员',
        `customer_service_id`       bigint unsigned NOT NULL DEFAULT '0' COMMENT '客服人员',
        `gross_weight`              decimal(18, 3)                                                NOT NULL DEFAULT '0.000' COMMENT '毛重',
        `gross_volume`              decimal(18, 2)                                                NOT NULL DEFAULT '0.00' COMMENT '体积',
        `tenant_id`                 bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户号',
        `revision`                  int                                                           NOT NULL DEFAULT '0' COMMENT '乐观锁',
        `remark`                    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
        `create_by`                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建人',
        `create_time`               datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
        `update_by`                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '更新人',
        `update_time`               datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
        `del_flag`                  char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL DEFAULT '0' COMMENT '删除标记',
        PRIMARY KEY (`id`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='卡派货柜';

    -- 备份出仓计划单（预出库单）表
    CREATE TABLE `tms_prep_outbound_order`
    (
        `id`                     bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
        `warehouse_id`           bigint unsigned NOT NULL DEFAULT '0' COMMENT '仓库id',
        `position_id_list`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '库位列表',
        `tray_id_list`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '托盘列表',
        `history_tray_id_list`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '托盘列表(历史记录)',
        `order_id_list`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单id',
        `fba_appointment_no`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'FBA预约编号',
        `reference_code`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'refrence_code',
        `total_weight`           decimal(18, 3)                                                NOT NULL DEFAULT '0.000' COMMENT '总重量',
        `total_volume`           decimal(18, 2)                                                NOT NULL DEFAULT '0.00' COMMENT '总体积',
        `out_time`               datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '预出仓时间',
        `status`                 int                                                           NOT NULL DEFAULT '0' COMMENT '状态',
        `execution_by`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '执行人',
        `execution_time`         datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '执行时间',
        `car_number`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '车牌号',
        `truck_company`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '卡车公司',
        `driver`                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '司机',
        `appointment_time`       datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '预约时间',
        `estimated_arrival_time` datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '预计到达时间',
        `tenant_id`              bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户号',
        `revision`               int                                                           NOT NULL DEFAULT '0' COMMENT '乐观锁',
        `remark`                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
        `create_by`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建人',
        `create_time`            datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
        `update_by`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '更新人',
        `update_time`            datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
        `del_flag`               char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL DEFAULT '0' COMMENT '删除标记',
        PRIMARY KEY (`id`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='卡派出仓计划单';

-- 创建订单申报信息表
CREATE TABLE `tms_delivery_order_declare`
(
    `id`                    bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `order_id`              bigint unsigned NOT NULL DEFAULT '0' COMMENT '订单id',
    `item_id`               bigint unsigned NOT NULL DEFAULT '0',
    `forecast_title`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
    `forecast_title_cn`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称 中文',
    `forecast_quantity`     int                                                           NOT NULL DEFAULT '0' COMMENT '数量',
    `forecast_unit_price`   decimal(18, 2)                                                NOT NULL DEFAULT '0.00' COMMENT '申报价格(单价),单位 USD,必填',
    `forecast_unit_weight`  decimal(18, 3)                                                NOT NULL DEFAULT '0.000' COMMENT '申报重量(单重)，单位 kg',
    `forecast_customs_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '预报海关编码',
    `actual_title`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '实际名称',
    `actual_title_cn`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '实际名称 中文',
    `actual_quantity`       int                                                           NOT NULL DEFAULT '0' COMMENT '实际数量',
    `actual_unit_price`     decimal(18, 2)                                                NOT NULL DEFAULT '0.00' COMMENT '申报价格(单价),单位 USD,必填',
    `actual_unit_weight`    decimal(18, 3)                                                NOT NULL DEFAULT '0.000' COMMENT '申报重量(单重)，单位 kg',
    `actual_customs_code`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '实际海关编码',
    `unit_code`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'PCE' COMMENT '申报单位',
    `product_url`           text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '产品url',
    `image_url`             text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '图片url',
    `brand`                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '品牌',
    `model`                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '型号',
    `material`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '材质',
    `purpose`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用途',
    `currency_code`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'USD' COMMENT '申报币种，默认:USD',
    `sku`                   text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'SKU',
    `tenant_id`             bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户号',
    `revision`              int                                                           NOT NULL DEFAULT '0' COMMENT '乐观锁',
    `remark`                varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
    `create_by`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建人;',
    `create_time`           datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间;',
    `update_by`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '更新人;',
    `update_time`           datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间;',
    `del_flag`              char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL DEFAULT '0' COMMENT '删除标记;',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `id_unique` (`tenant_id`,`update_time`,`del_flag`,`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='卡派订单申报信息';

-- 创建订单签收照片表
CREATE TABLE `tms_order_sign_image`
(
    `id`           bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `order_id`     int                                                           NOT NULL COMMENT '订单ID',
    `order_no`     varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '订单编号',
    `pkg_no`       varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '包裹编号',
    `pkg_image`    varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '包裹图',
    `put_image`    varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '放置图',
    `order_status` int                                                                    DEFAULT NULL COMMENT '订单状态',
    `driver_name`  varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '司机名称',
    `driver_id`    bigint                                                        NOT NULL DEFAULT '0' COMMENT '司机ID',
    `staff_id`     bigint                                                        NOT NULL DEFAULT '0' COMMENT '员工ID',
    `lat`          decimal(10, 6)                                                NOT NULL DEFAULT '0.000000' COMMENT '纬度',
    `lng`          decimal(10, 6)                                                NOT NULL DEFAULT '0.000000' COMMENT '经度',
    `distance`     decimal(10, 2)                                                NOT NULL DEFAULT '-1.00' COMMENT '离收件距离',
    `tenant_id`    bigint                                                        NOT NULL DEFAULT '1' COMMENT '租户号',
    `revision`     bigint                                                        NOT NULL DEFAULT '1' COMMENT '乐观锁',
    `remark`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
    `create_by`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '创建人',
    `create_time`  datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
    `update_by`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '更新人',
    `update_time`  datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
    `del_flag`     char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci               DEFAULT '0' COMMENT '删除标志',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='卡派 签收照片';

-- 创建车辆信息表
CREATE TABLE `tms_vehicle_info`
(
    `id`                     bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `driver_id`              bigint                                                       NOT NULL COMMENT '车主ID',
    `carrier_id`             bigint                                                       NOT NULL COMMENT '承运商ID',
    `contact_phone`          varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '联系电话',
    `license_plate`          varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '车牌号',
    `vehicle_type`           int                                                          NOT NULL COMMENT '车辆类型',
    `vehicle_color`          varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '车辆颜色',
    `vehicle_image_url`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         DEFAULT NULL COMMENT '车辆图片(存URL路径)',
    `load_capacity`          decimal(10, 2)                                                        DEFAULT NULL COMMENT '载重(吨)',
    `volume`                 decimal(10, 3)                                                        DEFAULT NULL COMMENT '容积(升)',
    `purchase_date`          date                                                                  DEFAULT NULL COMMENT '车辆购买日期',
    `registration_date`      date                                                                  DEFAULT NULL COMMENT '车辆登记日期',
    `insurance_start_date`   date                                                                  DEFAULT NULL COMMENT '车辆保险有限期(开始时间)',
    `insurance_end_date`     date                                                                  DEFAULT NULL COMMENT '车辆保险有限期(结束时间)',
    `insurance_document_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         DEFAULT NULL COMMENT '车辆保险单文件(支持图片和文件(word、PDF最大支持60MB))',
    `length`                 decimal(10, 3)                                                        DEFAULT NULL COMMENT '长(m)',
    `width`                  decimal(10, 3)                                                        DEFAULT NULL COMMENT '宽(m)',
    `height`                 decimal(10, 3)                                                        DEFAULT NULL COMMENT '高(m)',
    `cargo_type`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         DEFAULT NULL COMMENT '可配货物类型',
    `is_valid`               tinyint(1) NOT NULL DEFAULT '1' COMMENT '启用状态：0 禁用，1 启用',
    `tenant_id`              bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户号',
    `revision`               int                                                          NOT NULL DEFAULT '0' COMMENT '乐观锁',
    `remark`                 varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         DEFAULT NULL COMMENT '备注',
    `create_by`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         DEFAULT NULL COMMENT '创建人',
    `create_time`            datetime                                                     NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
    `update_by`              varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         DEFAULT NULL COMMENT '更新人',
    `update_time`            datetime                                                     NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
    `del_flag`               char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci     NOT NULL DEFAULT '0' COMMENT '删除标记：0 未删除，1 已删除',
    `ownership_type`         tinyint                                                               DEFAULT NULL COMMENT '车辆所属权：1个人；2公司',
    `warehouse_length`       decimal(10, 3)                                                        DEFAULT NULL COMMENT '货仓长',
    `warehouse_width`        decimal(10, 3)                                                        DEFAULT NULL COMMENT '货仓宽',
    `warehouse_height`       decimal(10, 3)                                                        DEFAULT NULL COMMENT '货仓高',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                      `idx_carrier_id` (`carrier_id`) USING BTREE,
    KEY                      `idx_is_valid` (`is_valid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='车辆信息';

-- 创建注册码表
CREATE TABLE `tms_poll_code`
(
    `id`            bigint                                                        NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `poll_code`     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '注册码',
    `carrier_id`    bigint                                                        NOT NULL COMMENT '承运商ID',
    `validity_days` int                                                           NOT NULL DEFAULT '7' COMMENT '有效期（天），示例值：7天',
    `admin_user_id` bigint                                                        NOT NULL COMMENT '管理员用户ID',
    `is_valid`      tinyint                                                       NOT NULL DEFAULT '0' COMMENT '启用状态：0-禁用 1-启用',
    `tenant_id`     bigint                                                        NOT NULL DEFAULT '1' COMMENT '租户号',
    `revision`      bigint                                                        NOT NULL DEFAULT '1' COMMENT '乐观锁',
    `remark`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
    `create_by`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '创建人',
    `create_time`   datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
    `update_by`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '更新人',
    `update_time`   datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
    `del_flag`      char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci               DEFAULT '0' COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_poll_code` (`poll_code`),
    KEY             `idx_carrier_id` (`carrier_id`),
    KEY             `idx_validity` (`validity_days`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='卡派注册码';

-- 创建邮编表
CREATE TABLE `tms_postal_code`
(
    `id`           bigint                                                        NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `postal_code`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '邮政编码',
    `country_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '国家/地区',
    `states_name`  varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '省/州',
    `city_name`    varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '城市',
    `tax_rate`     decimal(5, 2)                                                 NOT NULL DEFAULT '0.00' COMMENT '税率（百分比，如7.25表示7.25%）',
    `is_valid`     tinyint                                                       NOT NULL DEFAULT '1' COMMENT '启用状态：0：禁用、1：启用',
    `tenant_id`    bigint                                                        NOT NULL DEFAULT '1' COMMENT '租户号',
    `revision`     bigint                                                        NOT NULL DEFAULT '1' COMMENT '乐观锁',
    `remark`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
    `create_by`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '创建人',
    `create_time`  datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
    `update_by`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '更新人',
    `update_time`  datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
    `del_flag`     char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci               DEFAULT '0' COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_postal_code` (`postal_code`),
    KEY            `idx_country` (`country_name`),
    KEY            `idx_states` (`states_name`),
    KEY            `idx_city` (`city_name`),
    KEY            `idx_location` (`country_name`,`states_name`,`city_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='卡派邮编';

-- 创建地址簿表
CREATE TABLE `tms_addressbook`
(
    `id`               bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `contacts`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '联系人',
    `contact_phone`    varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '联系电话',
    `country_name`     varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '国家/地区',
    `states_name`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '省/州',
    `city_name`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '城市',
    `postal_code`      varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '邮政编码',
    `address_type`     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '0' COMMENT '地址类型',
    `detailed_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '详细地址',
    `is_valid`         tinyint(1) NOT NULL DEFAULT '1' COMMENT '启用状态：0 禁用，1 启用',
    `tenant_id`        bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户号',
    `revision`         int                                                           NOT NULL DEFAULT '0' COMMENT '乐观锁',
    `remark`           varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '备注',
    `create_by`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '创建人',
    `create_time`      datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
    `update_by`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '更新人',
    `update_time`      datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
    `del_flag`         char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL DEFAULT '0' COMMENT '删除标记：0 未删除，1 已删除',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_postal_code` (`postal_code`) USING BTREE,
    KEY                `idx_address_type` (`address_type`) USING BTREE,
    KEY                `idx_is_valid` (`is_valid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='卡派地址簿';

-- 创建委托订单表
CREATE TABLE `tms_entrusted_order`
(
    `id`                            bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `entrusted_order_number`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '委托单号',
    `customer_order_number`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '客户单号',
    `is_sub_order_no`               tinyint                                                                DEFAULT NULL COMMENT '是否子单号',
    `shipment_no`                   varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '运输单号',
    `customer_id`                   bigint                                                        NOT NULL COMMENT '客户ID',
    `order_status`                  int                                                           NOT NULL COMMENT '订单状态(待审核、待分配、已驳回、待提货、运输中、已完成、未完成)',
    `audit_status`                  tinyint                                                                DEFAULT '0' COMMENT '审核状态：0：待审核，1：审核通过，2审核拒绝',
    `shipper_name`                  varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '发货人姓名',
    `shipper_phone`                 varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '发货人电话',
    `origin`                        varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '始发地',
    `shipper_postal_code`           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '发货邮编',
    `shipper_address`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '发货详细地址',
    `estimated_shipping_time_start` datetime                                                               DEFAULT NULL COMMENT '预计发货时间开始',
    `estimated_shipping_time_end`   datetime                                                               DEFAULT NULL COMMENT '预计发货时间结束',
    `is_tailgate_pickup`            tinyint(1) DEFAULT NULL COMMENT '是否尾板提货：0 否，1 是',
    `receiver_name`                 varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '到货人姓名',
    `receiver_phone`                varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '到货人电话',
    `destination`                   varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '目的地',
    `dest_postal_code`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '到货邮编',
    `dest_address`                  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '到货详细地址',
    `estimated_arrival_time_start`  datetime                                                               DEFAULT NULL COMMENT '预计到货时间开始',
    `estimated_arrival_time_end`    datetime                                                               DEFAULT NULL COMMENT '预计到货时间结束',
    `is_tailgate_unloaded`          tinyint(1) DEFAULT '0' COMMENT '是否尾板卸货：0 否，1 是',
    `carrier_id`                    bigint                                                                 DEFAULT NULL COMMENT '所属承运商',
    `order_type`                    tinyint                                                       NOT NULL COMMENT '订单类型：1=托盘，2=包裹',
    `transport_type`                tinyint                                                       NOT NULL COMMENT '运输类型：1=整车运输，2=零担运输',
    `cargo_type`                    tinyint                                                       NOT NULL COMMENT '货物类型：1=普通货物，2=危险货物',
    `address_type`                  tinyint                                                       NOT NULL COMMENT '地址类型：1=住宅-不需要尾板，2=住宅-需要尾板，3=商业地址-需要尾板，4=住宅-不需要尾板',
    `business_model`                tinyint                                                       NOT NULL COMMENT '业务模式：1 揽收，2 中大件，3 卡派',
    `cargo_quantity`                int                                                                    DEFAULT NULL COMMENT '货品数量',
    `total_weight`                  decimal(10, 2)                                                         DEFAULT '0.00' COMMENT '总重量(kg)',
    `total_volume`                  decimal(10, 2)                                                         DEFAULT '0.00' COMMENT '总体积(m³)',
    `total_freight`                 decimal(10, 2)                                                         DEFAULT '0.00' COMMENT '运费合计：订单的总运费，包含基础运费、燃油费、税费、附加费等费用信息，系统自动计算',
    `operator`                      varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '操作人',
    `tenant_id`                     bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户号',
    `revision`                      int                                                           NOT NULL DEFAULT '0' COMMENT '乐观锁',
    `remark`                        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '备注',
    `create_by`                     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '创建人',
    `create_time`                   datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
    `update_by`                     varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '更新人',
    `update_time`                   datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
    `del_flag`                      char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL DEFAULT '0' COMMENT '删除标记：0 未删除，1 已删除',
    `pickup_proof`                  text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '提货证明（最多6张图片，逗号分割）',
    `delivery_proof`                text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '送货证明（最多6张图片，JSON格式存储URL数组）',
    `shipper_lat_lng`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '发货地经纬度',
    `receiver_lat_lng`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '收货地经纬度',
    `refuse_time`                   datetime                                                               DEFAULT NULL COMMENT '拒签日期',
    `refuse_reasons`                varchar(255) COLLATE utf8mb4_general_ci                                DEFAULT NULL COMMENT '拒签原因',
    `is_scan`                       tinyint                                                                DEFAULT '0' COMMENT '是否扫描：0：未扫描/1:已扫描',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_entrusted_order_number` (`entrusted_order_number`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='卡派委托订单';

-- 创建货物信息表
CREATE TABLE `tms_cargo_info`
(
    `id`                     bigint                                                        NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `bag_num`                varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '箱号-系统自动生成-由001依次递增',
    `length`                 decimal(10, 3)                                                NOT NULL COMMENT '长',
    `width`                  decimal(10, 3)                                                NOT NULL COMMENT '宽',
    `height`                 decimal(10, 3)                                                NOT NULL COMMENT '高',
    `weight`                 decimal(10, 3)                                                         DEFAULT NULL COMMENT '重量(kg)',
    `cargo_quantity`         int                                                                    DEFAULT NULL COMMENT '货物数量',
    `entrusted_order_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '委托单号',
    `customer_order_number`  varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '客户单号',
    `cargo_description`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '货物信息',
    `tenant_id`              bigint                                                        NOT NULL DEFAULT '1' COMMENT '租户号',
    `revision`               bigint                                                        NOT NULL DEFAULT '1' COMMENT '乐观锁',
    `remark`                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
    `create_by`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '创建人',
    `create_time`            datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
    `update_by`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '更新人',
    `update_time`            datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
    `del_flag`               char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci               DEFAULT '0' COMMENT '删除标志',
    `box_max_weight`         decimal(10, 3)                                                         DEFAULT NULL COMMENT '单箱最大重量',
    `pkg_max_weight`         decimal(10, 3)                                                         DEFAULT NULL COMMENT '单包裹最大重量',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                      `idx_entrusted_order_number` (`customer_order_number`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='卡派货物信息';

-- 创建订单货品关联表
CREATE TABLE `tms_order_cargo`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `order_id`    bigint unsigned NOT NULL COMMENT '订单ID',
    `cargo_id`    bigint unsigned NOT NULL COMMENT '货品ID',
    `tenant_id`   bigint                                                        NOT NULL DEFAULT '1' COMMENT '租户号',
    `revision`    bigint                                                        NOT NULL DEFAULT '1' COMMENT '乐观锁',
    `remark`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
    `create_by`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '创建人',
    `create_time` datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
    `update_by`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '更新人',
    `update_time` datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
    `del_flag`    char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci               DEFAULT '0' COMMENT '删除标志',
    PRIMARY KEY (`id`),
    KEY           `idx_order_id` (`order_id`),
    KEY           `idx_cargo_id` (`cargo_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='卡派-订单货品关联表';

-- 创建附加服务表
CREATE TABLE `tms_additional_services`
(
    `id`                       bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `customer_order_number`    varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL,
    `entrusted_order_number`   varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '委托单号',
    `additional_service_type`  varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '附加服务类型',
    `additional_service_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '附加服务取值',
    `additional_service_time`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '附加服务时间',
    `is_urban`                 tinyint(1) DEFAULT NULL COMMENT '是否城市/郊区：0 郊区，1 城市',
    `tenant_id`                bigint                                                        NOT NULL DEFAULT '1' COMMENT '租户号',
    `revision`                 bigint                                                        NOT NULL DEFAULT '1' COMMENT '乐观锁',
    `remark`                   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
    `create_by`                varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '创建人',
    `create_time`              datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
    `update_by`                varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '更新人',
    `update_time`              datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
    `del_flag`                 char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci               DEFAULT '0' COMMENT '删除标记：0 未删除，1 已删除',
    PRIMARY KEY (`id`),
    KEY                        `idx_entrusted_order_number` (`entrusted_order_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='卡派-附加服务';

-- 创建站点表
CREATE TABLE `tms_site`
(
    `id`                     bigint                                                        NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `city_name`              varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '城市',
    `region_id`              bigint                                                        NOT NULL COMMENT '所属区域',
    `site_type`              tinyint                                                       NOT NULL COMMENT '站点类型：0：一级，1：二级',
    `site_name`              varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '站点名称',
    `principal`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '负责人',
    `phone`                  varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '电话',
    `site_area`              decimal(10, 2)                                                         DEFAULT NULL COMMENT '站点面积（km²）',
    `area_geometry`          json                                                                   DEFAULT NULL COMMENT '划分的区域',
    `site_address`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '站点地址',
    `is_distribution_center` tinyint                                                                DEFAULT NULL COMMENT '是否配送中心',
    `is_warehouse`           tinyint                                                                DEFAULT NULL COMMENT '是否仓库',
    `is_pick_up_point`       tinyint                                                                DEFAULT NULL COMMENT '是否取货点',
    `is_valid`               tinyint                                                       NOT NULL DEFAULT '0' COMMENT '启用状态：0-禁用 1-启用',
    `tenant_id`              bigint                                                        NOT NULL DEFAULT '1' COMMENT '租户号',
    `revision`               bigint                                                        NOT NULL DEFAULT '1' COMMENT '乐观锁',
    `remark`                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
    `create_by`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '创建人',
    `create_time`            datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
    `update_by`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '更新人',
    `update_time`            datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
    `del_flag`               char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci               DEFAULT '0' COMMENT '删除标志',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                      `idx_region_id` (`region_id`) USING BTREE,
    KEY                      `idx_site_name` (`site_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='卡派站点';

-- 创建客户订单表
CREATE TABLE `tms_customer_order`
(
    `id`                            bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `customer_order_number`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '客户单号',
    `customer_id`                   bigint                                                        NOT NULL COMMENT '客户ID',
    `order_status`                  int                                                           NOT NULL COMMENT '订单状态(23001=待指派、23002=已取消、23003=已指派、23004=待审批、23005=待分配、23006=已驳回、23007=已指派、23008=待运输、23009=待收货、23010=运输中、23011=已完成)',
    `audit_status`                  tinyint                                                                DEFAULT '0' COMMENT '审核状态：0：待审核，1：审核通过，2审核拒绝',
    `shipper_name`                  varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '发货人姓名',
    `shipper_phone`                 varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '发货人电话',
    `origin`                        varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '始发地',
    `shipper_postal_code`           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '发货邮编',
    `shipper_address`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '发货详细地址',
    `estimated_shipping_time_start` datetime                                                               DEFAULT NULL COMMENT '预计发货时间开始',
    `estimated_shipping_time_end`   datetime                                                               DEFAULT NULL COMMENT '预计发货时间结束',
    `is_tailgate_pickup`            tinyint(1) DEFAULT NULL COMMENT '是否尾板提货：0 否，1 是',
    `receiver_name`                 varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '到货人姓名',
    `receiver_phone`                varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '到货人电话',
    `destination`                   varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '目的地',
    `dest_postal_code`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '到货邮编',
    `dest_address`                  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '到货详细地址',
    `estimated_arrival_time_start`  datetime                                                               DEFAULT NULL COMMENT '预计到货时间开始',
    `estimated_arrival_time_end`    datetime                                                               DEFAULT NULL COMMENT '预计到货时间结束',
    `is_tailgate_unloaded`          tinyint(1) DEFAULT '0' COMMENT '是否尾板卸货：0 否，1 是',
    `carrier_id`                    bigint                                                                 DEFAULT NULL COMMENT '所属承运商',
    `order_type`                    tinyint                                                       NOT NULL COMMENT '订单类型：1=托盘，2=包裹',
    `transport_type`                tinyint                                                       NOT NULL COMMENT '运输类型：1=整车运输，2=零担运输',
    `cargo_type`                    tinyint                                                       NOT NULL COMMENT '货物类型：1=普通货物，2=危险货物',
    `address_type`                  tinyint                                                       NOT NULL COMMENT '地址类型：1=住宅-不需要尾板，2=住宅-需要尾板，3=商业地址-需要尾板',
    `business_model`                tinyint                                                       NOT NULL COMMENT '业务模式：1 揽收，2 中大件，3 卡派',
    `cargo_quantity`                int                                                                    DEFAULT NULL COMMENT '货品数量',
    `total_weight`                  decimal(10, 2)                                                         DEFAULT '0.00' COMMENT '总重量(kg)',
    `total_volume`                  decimal(10, 2)                                                         DEFAULT '0.00' COMMENT '总体积(m³)',
    `total_freight`                 decimal(10, 2)                                                         DEFAULT '0.00' COMMENT '运费合计：订单的总运费，包含基础运费、燃油费、税费、附加费等费用信息，系统自动计算',
    `operator`                      varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '操作人',
    `tenant_id`                     bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户号',
    `revision`                      int                                                           NOT NULL DEFAULT '0' COMMENT '乐观锁',
    `remark`                        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '备注',
    `create_by`                     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '创建人',
    `create_time`                   datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
    `update_by`                     varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '更新人',
    `update_time`                   datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
    `del_flag`                      char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL DEFAULT '0' COMMENT '删除标记：0 未删除，1 已删除',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_customer_order_number` (`customer_order_number`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='卡派客户订单';

-- 创建承运商审核记录表
CREATE TABLE `tms_carrier_audit_records`
(
    `id`                    bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `customer_order_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '客户单号',
    `entrusted_order_number`varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '委托单号',
    `carrier_code`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '承运商编码',
    `carrier_name`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '承运商名称',
    `carrier_type`          tinyint                                                       NOT NULL DEFAULT '1' COMMENT '承运商类型。1：自营、2：外包',
    `name`                  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '联系人姓名',
    `phone`                 varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '手机号',
    `audit_status`          tinyint                                                                DEFAULT '0' COMMENT '审核状态：0：待审核，1：审核通过，2审核拒绝',
    `tenant_id`             bigint                                                        NOT NULL DEFAULT '1' COMMENT '租户号',
    `revision`              bigint                                                        NOT NULL DEFAULT '1' COMMENT '乐观锁',
    `remark`                varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
    `create_by`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '创建人',
    `create_time`           datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
    `update_by`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '更新人',
    `update_time`           datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
    `del_flag`              char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci               DEFAULT '0' COMMENT '删除标记：0 未删除，1 已删除',
    PRIMARY KEY (`id`),
    KEY                     `idx_carrier_code_type` (`carrier_code`,`carrier_type`),
    KEY                     `idx_customer_order_number` (`customer_order_number`),
    KEY                     `idx_carrier_code` (`carrier_code`),
    KEY                     `idx_carrier_name` (`carrier_name`),
    KEY                     `idx_phone` (`phone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='卡派-承运商审核记录';

-- 创建入库记录表
CREATE TABLE `tms_storage_record`
(
    `id`                            bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `warehouse_name`                varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '仓库名称',
    `warehouse_type`                tinyint                                                       NOT NULL COMMENT '仓库类型：0：一级，1：二级，3：三级，对应站点类型',
    `storage_order_number`          varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '入库单号',
    `entrusted_order_number`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '委托单号',
    `business_model`                tinyint                                                       NOT NULL COMMENT '业务模式：1 揽收，2 中大件，3 卡派',
    `shipper_name`                  varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '发货人姓名',
    `shipper_phone`                 varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '发货人电话',
    `origin`                        varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '始发地',
    `shipper_postal_code`           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '发货邮编',
    `shipper_address`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '发货详细地址',
    `estimated_shipping_time_start` datetime                                                               DEFAULT NULL COMMENT '预计发货时间开始',
    `estimated_shipping_time_end`   datetime                                                               DEFAULT NULL COMMENT '预计发货时间结束',
    `is_tailgate_pickup`            tinyint(1) DEFAULT NULL COMMENT '是否尾板提货：0 否，1 是',
    `receiver_name`                 varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '到货人姓名',
    `receiver_phone`                varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '到货人电话',
    `destination`                   varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '目的地',
    `dest_postal_code`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '到货邮编',
    `dest_address`                  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '到货详细地址',
    `estimated_arrival_time_start`  datetime                                                               DEFAULT NULL COMMENT '预计到货时间开始',
    `estimated_arrival_time_end`    datetime                                                               DEFAULT NULL COMMENT '预计到货时间结束',
    `is_tailgate_unloaded`          tinyint(1) DEFAULT '0' COMMENT '是否尾板卸货：0 否，1 是',
    `carrier_id`                    bigint                                                                 DEFAULT NULL COMMENT '所属承运商',
    `order_type`                    tinyint                                                       NOT NULL COMMENT '订单类型：1=托盘，2=包裹',
    `transport_type`                tinyint                                                       NOT NULL COMMENT '运输类型：1=整车运输，2=零担运输',
    `cargo_type`                    tinyint                                                       NOT NULL COMMENT '货物类型：1=普通货物，2=危险货物',
    `address_type`                  tinyint                                                       NOT NULL COMMENT '地址类型：1=住宅-不需要尾板，2=住宅-需要尾板，3=商业地址-需要尾板',
    `cargo_quantity`                int                                                                    DEFAULT NULL COMMENT '货品数量',
    `total_weight`                  decimal(10, 6)                                                         DEFAULT '0.000000' COMMENT '总重量(kg)',
    `total_volume`                  decimal(10, 6)                                                         DEFAULT '0.000000' COMMENT '总体积(m³)',
    `operator`                      varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '操作人',
    `tenant_id`                     bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户号',
    `revision`                      int                                                           NOT NULL DEFAULT '0' COMMENT '乐观锁',
    `remark`                        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '备注',
    `create_by`                     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '创建人',
    `create_time`                   datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
    `update_by`                     varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '更新人',
    `update_time`                   datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
    `del_flag`                      char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL DEFAULT '0' COMMENT '删除标记：0 未删除，1 已删除',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                             `idx_storage_order_number` (`storage_order_number`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='卡派入库记录';

-- 创建出库记录表
CREATE TABLE `tms_outbound_record`
(
    `id`                            bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `warehouse_name`                varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '仓库名称',
    `warehouse_type`                tinyint                                                       NOT NULL COMMENT '仓库类型：0：一级，1：二级，3：三级，对应站点类型',
    `outbound_order_number`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '出库单号',
    `entrusted_order_number`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '委托单号',
    `business_model`                tinyint                                                       NOT NULL COMMENT '业务模式：1 揽收，2 中大件，3 卡派',
    `shipper_name`                  varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '发货人姓名',
    `shipper_phone`                 varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '发货人电话',
    `origin`                        varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '始发地',
    `shipper_postal_code`           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '发货邮编',
    `shipper_address`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '发货详细地址',
    `estimated_shipping_time_start` datetime                                                               DEFAULT NULL COMMENT '预计发货时间开始',
    `estimated_shipping_time_end`   datetime                                                               DEFAULT NULL COMMENT '预计发货时间结束',
    `is_tailgate_pickup`            tinyint(1) DEFAULT NULL COMMENT '是否尾板提货：0 否，1 是',
    `receiver_name`                 varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '到货人姓名',
    `receiver_phone`                varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '到货人电话',
    `destination`                   varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '目的地',
    `dest_postal_code`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '到货邮编',
    `dest_address`                  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '到货详细地址',
    `estimated_arrival_time_start`  datetime                                                               DEFAULT NULL COMMENT '预计到货时间开始',
    `estimated_arrival_time_end`    datetime                                                               DEFAULT NULL COMMENT '预计到货时间结束',
    `is_tailgate_unloaded`          tinyint(1) DEFAULT '0' COMMENT '是否尾板卸货：0 否，1 是',
    `carrier_id`                    bigint                                                                 DEFAULT NULL COMMENT '所属承运商',
    `order_type`                    tinyint                                                       NOT NULL COMMENT '订单类型：1=托盘，2=包裹',
    `transport_type`                tinyint                                                       NOT NULL COMMENT '运输类型：1=整车运输，2=零担运输',
    `cargo_type`                    tinyint                                                       NOT NULL COMMENT '货物类型：1=普通货物，2=危险货物',
    `address_type`                  tinyint                                                       NOT NULL COMMENT '地址类型：1=住宅-不需要尾板，2=住宅-需要尾板，3=商业地址-需要尾板',
    `cargo_quantity`                int                                                                    DEFAULT NULL COMMENT '货品数量',
    `total_weight`                  decimal(10, 6)                                                         DEFAULT '0.000000' COMMENT '总重量(kg)',
    `total_volume`                  decimal(10, 6)                                                         DEFAULT '0.000000' COMMENT '总体积(m³)',
    `operator`                      varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '操作人',
    `tenant_id`                     bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户号',
    `revision`                      int                                                           NOT NULL DEFAULT '0' COMMENT '乐观锁',
    `remark`                        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '备注',
    `create_by`                     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '创建人',
    `create_time`                   datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
    `update_by`                     varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '更新人',
    `update_time`                   datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
    `del_flag`                      char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL DEFAULT '0' COMMENT '删除标记：0 未删除，1 已删除',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                             `idx_outbound_order_number` (`outbound_order_number`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='卡派出库记录';

-- 创建库存管理表
CREATE TABLE `tms_inventory_management`
(
    `id`             bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `site_id`        bigint                                                                DEFAULT NULL COMMENT '站点ID',
    `warehouse_name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '仓库名称',
    `warehouse_type` tinyint                                                      NOT NULL COMMENT '仓库类型：0：一级仓，1：二级仓，3：驿站',
    `order_num`      int                                                                   DEFAULT '0' COMMENT '票数',
    `cargo_quantity` int                                                                   DEFAULT NULL COMMENT '货物数量',
    `total_weight`   decimal(10, 6)                                                        DEFAULT '0.000000' COMMENT '总重量(kg)',
    `total_volume`   decimal(10, 6)                                                        DEFAULT '0.000000' COMMENT '总体积(m³)',
    `tray_num`       int                                                                   DEFAULT NULL COMMENT '托盘数量',
    `pkg_num`        int                                                                   DEFAULT NULL COMMENT '包裹数量',
    `tenant_id`      bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户号',
    `revision`       int                                                          NOT NULL DEFAULT '0' COMMENT '乐观锁',
    `remark`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         DEFAULT NULL COMMENT '备注',
    `create_by`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         DEFAULT NULL COMMENT '创建人',
    `create_time`    datetime                                                     NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
    `update_by`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         DEFAULT NULL COMMENT '更新人',
    `update_time`    datetime                                                     NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
    `del_flag`       char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci     NOT NULL DEFAULT '0' COMMENT '删除标记：0 未删除，1 已删除',
    PRIMARY KEY (`id`) USING BTREE,
    KEY              `idx_warehouse_name` (`warehouse_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='库存管理';

-- 创建运输任务单表
CREATE TABLE `tms_transport_task_order`
(
    `id`                            bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `task_type`                     tinyint                                                                DEFAULT NULL COMMENT '任务类型：1 揽收；2 派送',
    `task_order_no`                 varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '任务单号：1 揽收；2 干线；3 派送',
    `customer_order_number`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '客户单号',
    `entrusted_order_number`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '跟踪单号',
    `driver_id`                     bigint                                                        NOT NULL COMMENT '司机ID',
    `license_plate`                 varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '车牌号',
    `order_type`                    tinyint                                                       NOT NULL COMMENT '订单类型：1=托盘，2=包裹',
    `cargo_quantity`                int                                                                    DEFAULT NULL COMMENT '货物数量',
    `task_status`                   int                                                           NOT NULL COMMENT '任务状态(33001=待指派、33002=已取消、33003=已指派、33004=待运输)',
    `shipper_name`                  varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '发货人姓名',
    `shipper_phone`                 varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '发货人电话',
    `origin`                        varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '始发地',
    `shipper_postal_code`           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '发货邮编',
    `shipper_address`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '发货详细地址',
    `estimated_shipping_time_start` datetime                                                               DEFAULT NULL COMMENT '预计发货时间开始',
    `estimated_shipping_time_end`   datetime                                                               DEFAULT NULL COMMENT '预计发货时间结束',
    `is_tailgate_pickup`            tinyint(1) DEFAULT NULL COMMENT '是否尾板提货：0 否，1 是',
    `receiver_name`                 varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '到货人姓名',
    `receiver_phone`                varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '到货人电话',
    `destination`                   varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '目的地',
    `dest_postal_code`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '到货邮编',
    `dest_address`                  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '到货详细地址',
    `estimated_arrival_time_start`  datetime                                                               DEFAULT NULL COMMENT '预计到货时间开始',
    `estimated_arrival_time_end`    datetime                                                               DEFAULT NULL COMMENT '预计到货时间结束',
    `is_tailgate_unloaded`          tinyint(1) DEFAULT '0' COMMENT '是否尾板卸货：0 否，1 是',
    `carrier_id`                    bigint                                                                 DEFAULT NULL COMMENT '承运商ID',
    `customer_id`                   bigint                                                                 DEFAULT NULL COMMENT '委托客户ID',
    `site_id`                       bigint                                                                 DEFAULT NULL COMMENT '仓库ID',
    `transport_type`                tinyint                                                       NOT NULL COMMENT '运输类型：1=整车运输，2=零担运输',
    `cargo_type`                    tinyint                                                       NOT NULL COMMENT '货物类型：1=普通货物，2=危险货物',
    `address_type`                  tinyint                                                       NOT NULL COMMENT '地址类型：1=住宅-不需要尾板，2=住宅-需要尾板，3=商业地址-需要尾板',
    `business_model`                tinyint                                                       NOT NULL COMMENT '业务模式：1 揽收，2 中大件，3 卡派',
    `total_weight`                  decimal(10, 6)                                                         DEFAULT '0.000000' COMMENT '总重量(kg)',
    `total_volume`                  decimal(10, 6)                                                         DEFAULT '0.000000' COMMENT '总体积(m³)',
    `tenant_id`                     bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户号',
    `revision`                      int                                                           NOT NULL DEFAULT '0' COMMENT '乐观锁',
    `remark`                        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '备注',
    `create_by`                     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '创建人',
    `create_time`                   datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
    `update_by`                     varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '更新人',
    `update_time`                   datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
    `del_flag`                      char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL DEFAULT '0' COMMENT '删除标记：0 未删除，1 已删除',
    `is_delete`                     tinyint(1) DEFAULT '0' COMMENT '是否删除',
    `shipper_lat_lng`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '发货地经纬度',
    `receiver_lat_lng`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '收货地经纬度',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_task_order_no` (`task_order_no`) USING BTREE,
    KEY                             `idx_customer_order_number` (`customer_order_number`) USING BTREE,
    KEY                             `idx_entrusted_order_number` (`entrusted_order_number`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='运输任务单';

-- 创建干线任务单表
CREATE TABLE `tms_line_haul_order`
(
    `id`                     bigint                                                        NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `line_haul_no`           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '干线单号',
    `transport_type`         tinyint                                                       NOT NULL COMMENT '运输类型：1=整车运输，2=零担运输',
    `task_status`            tinyint                                                       NOT NULL DEFAULT '0' COMMENT '任务状态（0-待提货，1-运输中，2-已完成，3-异常）',
    `driver_id`              bigint unsigned DEFAULT NULL COMMENT '司机ID，关联司机表',
    `contact_phone`          varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '司机联系方式',
    `license_plate`          varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '车牌号',
    `origin`                 varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '始发地',
    `destination`            varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '目的地',
    `planned_departure_time` datetime                                                               DEFAULT NULL COMMENT '计划出发时间',
    `total_quantity`         int                                                           NOT NULL COMMENT '货物数量',
    `total_volume`           decimal(10, 4)                                                NOT NULL COMMENT '总体积(m³)',
    `total_weight`           decimal(10, 2)                                                NOT NULL COMMENT '总重量(kg)',
    `tenant_id`              bigint                                                        NOT NULL DEFAULT '1' COMMENT '租户号',
    `revision`               bigint                                                        NOT NULL DEFAULT '1' COMMENT '乐观锁',
    `remark`                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT '' COMMENT '备注',
    `create_by`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '1' COMMENT '创建人',
    `create_time`            datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
    `update_by`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '1' COMMENT '更新人',
    `update_time`            datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
    `del_flag`               char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci               DEFAULT '0' COMMENT '删除标志',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `line_haul_no` (`line_haul_no`) USING BTREE,
    KEY                      `idx_line_haul_no` (`line_haul_no`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='干线任务单';
