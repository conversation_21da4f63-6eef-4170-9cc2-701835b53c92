<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsOrderSignImageMapper">

    <resultMap id="tmsOrderSignImageMap" type="com.jygjexp.jynx.tms.entity.TmsOrderSignImageEntity">
        <id property="id" column="id"/>
        <result property="orderId" column="order_id"/>
        <result property="orderNo" column="order_no"/>
        <result property="pkgNo" column="pkg_no"/>
        <result property="pkgImage" column="pkg_image"/>
        <result property="putImage" column="put_image"/>
        <result property="orderStatus" column="order_status"/>
        <result property="driverName" column="driver_name"/>
        <result property="driverId" column="driver_id"/>
        <result property="staffId" column="staff_id"/>
        <result property="lat" column="lat"/>
        <result property="lng" column="lng"/>
        <result property="distance" column="distance"/>
        <result property="revision" column="revision"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>
</mapper>