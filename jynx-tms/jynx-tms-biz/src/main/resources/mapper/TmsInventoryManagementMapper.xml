<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsInventoryManagementMapper">

    <resultMap id="tmsInventoryManagementMap" type="com.jygjexp.jynx.tms.entity.TmsInventoryManagementEntity">
        <id property="id" column="id"/>
        <result property="siteId" column="site_id"/>
        <result property="warehouseName" column="warehouse_name"/>
        <result property="warehouseType" column="warehouse_type"/>
        <result property="orderNum" column="order_num"/>
        <result property="cargoQuantity" column="cargo_quantity"/>
        <result property="totalWeight" column="total_weight"/>
        <result property="totalVolume" column="total_volume"/>
        <result property="trayNum" column="tray_num"/>
        <result property="pkgNum" column="pkg_num"/>
        <result property="revision" column="revision"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>
</mapper>