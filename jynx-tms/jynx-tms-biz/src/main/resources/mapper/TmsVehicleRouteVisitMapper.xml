<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsVehicleRouteVisitMapper">

  <resultMap id="tmsVehicleRouteVisitMap" type="com.jygjexp.jynx.tms.entity.TmsVehicleRouteVisitEntity">
        <id property="vehicleRouteVisitId" column="vehicle_route_visit_id"/>
        <result property="vehicleRouteId" column="vehicle_route_id"/>
        <result property="isPickup" column="is_pickup"/>
        <result property="arrivalTime" column="arrival_time"/>
        <result property="detour" column="detour"/>
        <result property="shipmentLabel" column="shipment_label"/>
        <result property="loadWeight" column="load_weight"/>
  </resultMap>
</mapper>