<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsSortingRecordMapper">

  <resultMap id="tmsSortingRecordMap" type="com.jygjexp.jynx.tms.entity.TmsSortingRecordEntity">
        <id property="id" column="id"/>
        <result property="templateId" column="template_id"/>
        <result property="templateName" column="template_name"/>
        <result property="grid" column="grid"/>
        <result property="orderNo" column="order_no"/>
        <result property="scanTime" column="scan_time"/>
        <result property="sortingStatus" column="sorting_status"/>
        <result property="routeNumber" column="route_number"/>
        <result property="city" column="city"/>
        <result property="receivePlace" column="receive_place"/>
        <result property="weight" column="weight"/>
        <result property="volume" column="volume"/>
        <result property="machineNumber" column="machine_number"/>
        <result property="merchant" column="merchant"/>
        <result property="type" column="type"/>
        <result property="status" column="status"/>
        <result property="siteId" column="site_id"/>
        <result property="revision" column="revision"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
  </resultMap>

  <select id="getDeliveryRecordByScanTimeGroup" resultType="com.jygjexp.jynx.tms.dto.DeliveryRecordGroupDTO">
    SELECT
    SUBSTRING(order_no, 1, LENGTH(order_no) - 3) as order_no,
    MAX(scan_time) scan_time
    FROM tms_sorting_record
    WHERE type = 2 AND sorting_status = 1
    AND scan_time BETWEEN #{scanTimeStart} AND #{scanTimeEnd}
    GROUP BY SUBSTRING(order_no, 1, LENGTH(order_no) - 3)
  </select>

    <select id="getDeliveryRecordByMain" resultType="com.jygjexp.jynx.tms.entity.TmsSortingRecordEntity">
        SELECT t.*
        FROM tms_sorting_record t
        INNER JOIN (
            SELECT
            order_no,
            MAX(scan_time) as max_scan_time
            FROM tms_sorting_record
            WHERE type = 2 AND sorting_status = 1
            AND order_no LIKE concat(#{orderNo},"%")
            GROUP BY order_no
        ) g ON t.order_no = g.order_no AND t.scan_time = g.max_scan_time
        WHERE t.type = 2 AND t.sorting_status = 1
        AND t.order_no LIKE concat(#{orderNo},"%")
    </select>
</mapper>
