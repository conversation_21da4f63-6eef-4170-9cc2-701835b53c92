<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsCarrierMapper">

  <resultMap id="tmsCarrierMap" type="com.jygjexp.jynx.tms.entity.TmsCarrierEntity">
        <id property="carrierId" column="cir_id"/>
        <result property="carrierCode" column="carrier_code"/>
        <result property="carrierName" column="carrier_name"/>
        <result property="carrierType" column="carrier_type"/>
        <result property="businessNumber" column="business_number"/>
        <result property="taxRegistrationNumber" column="tax_registration_number"/>
        <result property="legalName" column="legal_name"/>
        <result property="region" column="region"/>
        <result property="companyAddress" column="company_address"/>
        <result property="postalCode" column="postal_code"/>
        <result property="businessLicenseImage" column="business_license_image"/>
        <result property="name" column="name"/>
        <result property="phone" column="phone"/>
        <result property="dept" column="dept"/>
        <result property="position" column="position"/>
        <result property="carrierEmail" column="carrier_email"/>
        <result property="invoiceTitle" column="invoice_title"/>
        <result property="taxNumber" column="tax_number"/>
        <result property="bankDeposit" column="bank_deposit"/>
        <result property="bankName" column="bank_name"/>
        <result property="bankNumber" column="bank_number"/>
        <result property="isSignedCooperation" column="is_signed_cooperation"/>
        <result property="contractCopy" column="contract_copy"/>
        <result property="openingTime" column="opening_time"/>
        <result property="closingTime" column="closing_time"/>
        <result property="isValid" column="is_valid"/>
        <result property="revision" column="revision"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
  </resultMap>
</mapper>