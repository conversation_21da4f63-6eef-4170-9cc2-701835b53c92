<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsExceptionManagementMapper">

  <resultMap id="tmsExceptionManagementMap" type="com.jygjexp.jynx.tms.entity.TmsExceptionManagementEntity">
        <id property="exceptionId" column="exception_id"/>
        <result property="exceptionOrderNo" column="exception_order_no"/>
        <result property="exceptionStatus" column="exception_status"/>
        <result property="dispatchOrderNo" column="dispatch_order_no"/>
        <result property="licensePlate" column="license_plate"/>
        <result property="exceptionTime" column="exception_time"/>
        <result property="exceptionType" column="exception_type"/>
        <result property="exceptionLocation" column="exception_location"/>
        <result property="exceptionDescription" column="exception_description"/>
        <result property="imageUrls" column="image_urls"/>
        <result property="handlingPlan" column="handling_plan"/>
        <result property="handlingDescription" column="handling_description"/>
        <result property="revision" column="revision"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
  </resultMap>
</mapper>