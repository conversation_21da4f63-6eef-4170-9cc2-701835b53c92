<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsShelfMapper">

  <resultMap id="tmsShelfMap" type="com.jygjexp.jynx.tms.entity.TmsShelfEntity">
        <id property="shelfId" column="shelf_id"/>
        <result property="shelfCode" column="shelf_code"/>
        <result property="warehouseCode" column="warehouse_code"/>
        <result property="isValid" column="is_valid"/>
        <result property="pkgNumber" column="pkg_number"/>
        <result property="remark" column="remark"/>
        <result property="revision" column="revision"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
  </resultMap>
</mapper>