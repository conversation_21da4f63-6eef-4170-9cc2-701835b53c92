<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsManualSortingRecordMapper">

    <resultMap id="tmsManualSortingRecordMap" type="com.jygjexp.jynx.tms.entity.TmsManualSortingRecordEntity">
        <id property="id" column="id"/>
        <result property="warehouseId" column="warehouse_id"/>
        <result property="sortingGrid" column="sorting_grid"/>
        <result property="entrustedOrderNo" column="entrusted_order_no"/>
        <result property="sortingTime" column="sorting_time"/>
        <result property="isScan" column="is_scan"/>
        <result property="revision" column="revision"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

    <select id="selectLatestRecordIdsByEntrustedOrderNo" resultType="java.lang.Long">
        SELECT MAX(id) as id
        FROM tms_manual_sorting_record
        <where>
            <if test="entrustedOrderNo != null">
                AND entrusted_order_no LIKE CONCAT('%', #{entrustedOrderNo}, '%')
            </if>
            <if test="sortingGridCode != null">
                AND sorting_grid_code LIKE CONCAT('%', #{sortingGridCode}, '%')
            </if>
            <if test="warehouseId != null">
                AND warehouse_id = #{warehouseId}
            </if>
            <if test="sortingStartTime != null and sortingEndTime != null">
                AND sorting_time BETWEEN #{sortingStartTime} AND #{sortingEndTime}
            </if>
        </where>
        GROUP BY entrusted_order_no
        ORDER BY MAX(create_time) DESC
    </select>


</mapper>