<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsBasicFreightMapper">

  <resultMap id="tmsBasicFreightMap" type="com.jygjexp.jynx.tms.entity.TmsBasicFreightEntity">
        <id property="id" column="id"/>
        <result property="origin" column="origin"/>
        <result property="destination" column="destination"/>
        <result property="min" column="min"/>
        <result property="ltl" column="ltl"/>
        <result property="profitMargin" column="profit_margin"/>
        <result property="cwt1000" column="cwt_1000"/>
        <result property="cwt2000" column="cwt_2000"/>
        <result property="cwt5000" column="cwt_5000"/>
        <result property="cwt10000" column="cwt_10000"/>
        <result property="revision" column="revision"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
  </resultMap>
</mapper>