<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsDeliveryOrderDeclareMapper">

    <resultMap id="tmsDeliveryOrderDeclareMap" type="com.jygjexp.jynx.tms.entity.TmsDeliveryOrderDeclareEntity">
        <id property="id" column="id"/>
        <result property="orderId" column="order_id"/>
        <result property="itemId" column="item_id"/>
        <result property="forecastTitle" column="forecast_title"/>
        <result property="forecastTitleCn" column="forecast_title_cn"/>
        <result property="forecastQuantity" column="forecast_quantity"/>
        <result property="forecastUnitPrice" column="forecast_unit_price"/>
        <result property="forecastUnitWeight" column="forecast_unit_weight"/>
        <result property="forecastCustomsCode" column="forecast_customs_code"/>
        <result property="actualTitle" column="actual_title"/>
        <result property="actualTitleCn" column="actual_title_cn"/>
        <result property="actualQuantity" column="actual_quantity"/>
        <result property="actualUnitPrice" column="actual_unit_price"/>
        <result property="actualUnitWeight" column="actual_unit_weight"/>
        <result property="actualCustomsCode" column="actual_customs_code"/>
        <result property="unitCode" column="unit_code"/>
        <result property="productUrl" column="product_url"/>
        <result property="imageUrl" column="image_url"/>
        <result property="brand" column="brand"/>
        <result property="model" column="model"/>
        <result property="material" column="material"/>
        <result property="purpose" column="purpose"/>
        <result property="currencyCode" column="currency_code"/>
        <result property="sku" column="sku"/>
        <result property="revision" column="revision"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>
</mapper>