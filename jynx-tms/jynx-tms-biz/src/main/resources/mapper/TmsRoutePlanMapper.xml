<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsRoutePlanMapper">

  <resultMap id="tmsRoutePlanMap" type="com.jygjexp.jynx.tms.entity.TmsRoutePlanEntity">
        <id property="routePlanId" column="route_plan_id"/>
        <result property="shipmentId" column="shipment_id"/>
        <result property="skippedShipments" column="skipped_shipments"/>
        <result property="performedShipmentCount" column="performed_shipment_count"/>
        <result property="travelDuration" column="travel_duration"/>
        <result property="waitDuration" column="wait_duration"/>
        <result property="delayDuration" column="delay_duration"/>
        <result property="breakDuration" column="break_duration"/>
        <result property="visitDuration" column="visit_duration"/>
        <result property="totalDuration" column="total_duration"/>
        <result property="travelDistanceMeters" column="travel_distance_meters"/>
        <result property="totalLoad" column="total_load"/>
        <result property="skippedMandatoryShipmentCount" column="skipped_mandatory_shipment_count"/>
        <result property="usedVehicleCount" column="used_vehicle_count"/>
        <result property="earliestVehicleStartTime" column="earliest_vehicle_start_time"/>
        <result property="latestVehicleEndTime" column="latest_vehicle_end_time"/>
        <result property="totalCost" column="total_cost"/>
        <result property="costs" column="costs"/>
  </resultMap>
</mapper>