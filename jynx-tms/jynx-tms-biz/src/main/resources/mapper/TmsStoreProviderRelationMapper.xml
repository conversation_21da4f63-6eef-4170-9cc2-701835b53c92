<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsStoreProviderRelationMapper">

    <resultMap id="tmsStoreProviderRelationMap" type="com.jygjexp.jynx.tms.entity.TmsStoreProviderRelationEntity">
        <id property="id" column="id"/>
        <result property="storeOrderId" column="store_order_id"/>
        <result property="providerCode" column="provider_code"/>
        <result property="providerName" column="provider_name"/>
        <result property="providerServiceWay" column="provider_service_way"/>
        <result property="providerTransportTime" column="provider_transport_time"/>
        <result property="freightAmount" column="freight_amount"/>
        <result property="insuranceAmount" column="insurance_amount"/>
        <result property="podAmount" column="pod_amount"/>
        <result property="taxAmount" column="tax_amount"/>
        <result property="revision" column="revision"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>
</mapper>
