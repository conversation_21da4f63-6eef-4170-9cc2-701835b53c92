<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsStoreCreditCardMapper">

    <resultMap id="tmsStoreCreditCardMap" type="com.jygjexp.jynx.tms.entity.TmsStoreCreditCardEntity">
        <id property="id" column="id"/>
        <result property="storeCustomerId" column="store_customer_id"/>
        <result property="cardNumber" column="card_number"/>
        <result property="expiryDate" column="expiry_date"/>
        <result property="cvc" column="cvc"/>
        <result property="holderName" column="holder_name"/>
        <result property="defaultFlag" column="default_flag"/>
        <result property="revision" column="revision"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>
</mapper>
