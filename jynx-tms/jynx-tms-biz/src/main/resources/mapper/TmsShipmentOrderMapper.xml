<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsShipmentOrderMapper">

  <resultMap id="tmsShipmentOrderMap" type="com.jygjexp.jynx.tms.entity.TmsShipmentOrderEntity">
        <id property="shipmentId" column="shipment_id"/>
        <result property="shipmentNo" column="shipment_no"/>
        <result property="shipmentStatus" column="shipment_status"/>
        <result property="driverId" column="driver_id"/>
        <result property="licensePlate" column="license_plate"/>
        <result property="contactPhone" column="contact_phone"/>
        <result property="vehicleType" column="vehicle_type"/>
        <result property="totalVolume" column="total_volume"/>
        <result property="totalWeight" column="total_weight"/>
        <result property="totalQuantity" column="total_quantity"/>
        <result property="revision" column="revision"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
  </resultMap>
</mapper>