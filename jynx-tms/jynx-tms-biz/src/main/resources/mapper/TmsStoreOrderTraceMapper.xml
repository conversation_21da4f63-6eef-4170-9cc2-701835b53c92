<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsStoreOrderTraceMapper">

  <resultMap id="tmsStoreOrderTraceMap" type="com.jygjexp.jynx.tms.entity.TmsStoreOrderTraceEntity">
        <id property="id" column="id"/>
        <result property="subEntrustedOrder" column="sub_entrusted_order"/>
        <result property="mainEntrustedOrder" column="main_entrusted_order"/>
        <result property="orderStatus" column="order_status"/>
        <result property="orderStatusContext" column="order_status_context"/>
        <result property="externalExtend" column="external_extend"/>
        <result property="revision" column="revision"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
  </resultMap>
</mapper>