<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsVehicleRouteMapper">

  <resultMap id="tmsVehicleRouteMap" type="com.jygjexp.jynx.tms.entity.TmsVehicleRouteEntity">
        <id property="vehicleRouteId" column="vehicle_route_id"/>
        <result property="routePlanId" column="route_plan_id"/>
        <result property="vehicleLabel" column="vehicle_label"/>
        <result property="vehicleStartTime" column="vehicle_start_time"/>
        <result property="vehicleEndTime" column="vehicle_end_time"/>
        <result property="visitOrder" column="visit_order"/>
        <result property="routePolylinePoints" column="route_polyline_points"/>
        <result property="performedShipmentCount" column="performed_shipment_count"/>
        <result property="travelDuration" column="travel_duration"/>
        <result property="waitDuration" column="wait_duration"/>
        <result property="delayDuration" column="delay_duration"/>
        <result property="breakDuration" column="break_duration"/>
        <result property="visitDuration" column="visit_duration"/>
        <result property="totalDuration" column="total_duration"/>
        <result property="travelDistanceMeters" column="travel_distance_meters"/>
        <result property="totalLoad" column="total_load"/>
        <result property="routeTotalCosts" column="route_total_costs"/>
  </resultMap>
</mapper>