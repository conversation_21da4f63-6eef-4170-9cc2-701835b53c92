<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsBasicFreightPkgDetailMapper">

  <resultMap id="tmsBasicFreightPkgDetailMap" type="com.jygjexp.jynx.tms.entity.TmsBasicFreightPkgDetailEntity">
        <id property="id" column="id"/>
        <result property="region" column="region"/>
        <result property="startWeight" column="start_weight"/>
        <result property="endWeight" column="end_weight"/>
        <result property="pkgCost" column="pkg_cost"/>
        <result property="pkgFreId" column="pkg_fre_id"/>
        <result property="revision" column="revision"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
  </resultMap>
</mapper>