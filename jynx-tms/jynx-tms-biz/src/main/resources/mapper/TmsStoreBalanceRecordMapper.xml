<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsStoreBalanceRecordMapper">

    <resultMap id="tmsStoreBalanceRecordMap" type="com.jygjexp.jynx.tms.entity.TmsStoreBalanceRecordEntity">
        <id property="id" column="id"/>
        <result property="storeBalanceId" column="store_balance_id"/>
        <result property="storeCustomerId" column="store_customer_id"/>
        <result property="businessKey" column="business_key"/>
        <result property="businessNumber" column="business_number"/>
        <result property="beforeAmount" column="before_amount"/>
        <result property="changeAmount" column="change_amount"/>
        <result property="afterAmount" column="after_amount"/>
        <result property="type" column="type"/>
        <result property="subType" column="sub_type"/>
        <result property="status" column="status"/>
        <result property="revision" column="revision"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>
</mapper>
