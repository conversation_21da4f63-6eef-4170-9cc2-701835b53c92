<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsCustomerMapper">

    <resultMap id="tmsCustomerMap" type="com.jygjexp.jynx.tms.entity.TmsCustomerEntity">
        <id property="id" column="id"/>
        <result property="customerName" column="customer_name"/>
        <result property="customerNameCn" column="customer_name_cn"/>
        <result property="customerCode" column="customer_code"/>
        <result property="category" column="category"/>
        <result property="sector" column="sector"/>
        <result property="customerLevel" column="customer_level"/>
        <result property="customerLabel" column="customer_label"/>
        <result property="region" column="region"/>
        <result property="companyAddress" column="company_address"/>
        <result property="postalCode" column="postal_code"/>
        <result property="businessLicense" column="business_license"/>
        <result property="contactPerson" column="contact_person"/>
        <result property="phone" column="phone"/>
        <result property="department" column="department"/>
        <result property="position" column="position"/>
        <result property="email" column="email"/>
        <result property="warehouseAddress" column="warehouse_address"/>
        <result property="count" column="count"/>
        <result property="token" column="token"/>
        <result property="isValid" column="is_valid"/>
        <result property="revision" column="revision"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>
</mapper>