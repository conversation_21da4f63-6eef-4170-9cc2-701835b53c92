<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsRoutePlanFailRecordMapper">

  <resultMap id="tmsRoutePlanFailRecordMap" type="com.jygjexp.jynx.tms.entity.TmsRoutePlanFailRecordEntity">
        <id property="id" column="id"/>
        <result property="failKey" column="fail_key"/>
        <result property="date" column="date"/>
        <result property="failOrderString" column="fail_order_string"/>
        <result property="isHandle" column="is_handle"/>
        <result property="status" column="status"/>
        <result property="siteId" column="site_id"/>
        <result property="revision" column="revision"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
  </resultMap>
</mapper>