<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsOrderTrackMapper">

  <resultMap id="tmsOrderTrackMap" type="com.jygjexp.jynx.tms.entity.TmsOrderTrackEntity">
        <id property="trackId" column="track_id"/>
        <result property="customerOrderNo" column="customer_order_no"/>
        <result property="orderStatus" column="order_status"/>
        <result property="city" column="city"/>
        <result property="site" column="site"/>
        <result property="locationDescription" column="location_description"/>
        <result property="carrierName" column="carrier_id"/>
        <result property="trackType" column="track_type"/>
        <result property="revision" column="revision"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
  </resultMap>
</mapper>