<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsPickupMapper">

  <resultMap id="tmsPickupMap" type="com.jygjexp.jynx.tms.entity.TmsPickupEntity">
        <id property="pickupId" column="pickup_id"/>
        <result property="pickupCode" column="pickup_code"/>
        <result property="pkgNo" column="pkg_no"/>
        <result property="warehouseCode" column="warehouse_code"/>
        <result property="shelfCode" column="shelf_code"/>
        <result property="pickupStatus" column="pickup_status"/>
        <result property="shelfDay" column="shelf_day"/>
        <result property="pickupTime" column="pickup_time"/>
        <result property="pickupName" column="pickup_name"/>
        <result property="pickupPhone" column="pickup_phone"/>
        <result property="shelfTime" column="shelf_time"/>
        <result property="remark" column="remark"/>
        <result property="revision" column="revision"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
  </resultMap>
</mapper>