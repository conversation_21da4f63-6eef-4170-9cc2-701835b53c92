<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsPostalCodeMapper">

    <resultMap id="tmsPostalCodeMap" type="com.jygjexp.jynx.tms.entity.TmsPostalCodeEntity">
        <id property="id" column="id"/>
        <result property="postalCode" column="postal_code"/>
        <result property="countryName" column="country_name"/>
        <result property="statesName" column="states_name"/>
        <result property="cityName" column="city_name"/>
        <result property="taxRate" column="tax_rate"/>
        <result property="isValid" column="is_valid"/>
        <result property="revision" column="revision"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>
</mapper>