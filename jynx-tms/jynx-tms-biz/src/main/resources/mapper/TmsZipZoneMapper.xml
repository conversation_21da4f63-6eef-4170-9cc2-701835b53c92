<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsZipZoneMapper">

  <resultMap id="tmsZipZoneMap" type="com.jygjexp.jynx.tms.entity.TmsZipZoneEntity">
        <id property="id" column="id"/>
        <result property="regionId" column="region_id"/>
        <result property="routeNumber" column="route_number"/>
        <result property="overlayPostcode" column="overlay_postcode"/>
        <result property="overlayNumber" column="overlay_number"/>
        <result property="revision" column="revision"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
  </resultMap>
</mapper>