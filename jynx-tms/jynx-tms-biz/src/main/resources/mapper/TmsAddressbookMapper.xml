<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsAddressbookMapper">

    <resultMap id="tmsAddressbookMap" type="com.jygjexp.jynx.tms.entity.TmsAddressbookEntity">
        <id property="id" column="id"/>
        <result property="contacts" column="contacts"/>
        <result property="contactPhone" column="contact_phone"/>
        <result property="countryName" column="country_name"/>
        <result property="statesName" column="states_name"/>
        <result property="cityName" column="city_name"/>
        <result property="postalCode" column="postal_code"/>
        <result property="addressType" column="address_type"/>
        <result property="detailedAddress" column="detailed_address"/>
        <result property="isValid" column="is_valid"/>
        <result property="revision" column="revision"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>
</mapper>