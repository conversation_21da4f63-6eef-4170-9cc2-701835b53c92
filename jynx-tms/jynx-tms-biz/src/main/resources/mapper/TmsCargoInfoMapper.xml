<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsCargoInfoMapper">

    <resultMap id="tmsCargoInfoMap" type="com.jygjexp.jynx.tms.entity.TmsCargoInfoEntity">
        <id property="id" column="id"/>
        <result property="bagNum" column="bag_num"/>
        <result property="length" column="length"/>
        <result property="width" column="width"/>
        <result property="height" column="height"/>
        <result property="weight" column="weight"/>
        <result property="cargoQuantity" column="cargo_quantity"/>
        <result property="entrustedOrderNumber" column="entrusted_order_number"/>
        <result property="customerOrderNumber" column="customer_order_number"/>
        <result property="cargoDescription" column="cargo_description"/>
        <result property="revision" column="revision"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="boxMaxWeight" column="box_max_weight"/>
        <result property="pkgMaxWeight" column="pkg_max_weight"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>
</mapper>