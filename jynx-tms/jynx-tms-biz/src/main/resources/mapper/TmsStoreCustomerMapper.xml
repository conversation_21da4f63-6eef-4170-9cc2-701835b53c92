<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsStoreCustomerMapper">

    <resultMap id="tmsStoreCustomerMap" type="com.jygjexp.jynx.tms.entity.TmsStoreCustomerEntity">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="storeId" column="store_id"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="level" column="level"/>
        <result property="type" column="type"/>
        <result property="status" column="status"/>
        <result property="extend" column="extend"/>
        <result property="revision" column="revision"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>
</mapper>
