<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsDriverMapper">

  <resultMap id="tmsDriverMap" type="com.jygjexp.jynx.tms.entity.TmsDriverEntity">
        <id property="driverId" column="driver_id"/>
        <result property="driverName" column="driver_name"/>
        <result property="phone" column="phone"/>
        <result property="idNumber" column="id_number"/>
        <result property="sex" column="sex"/>
        <result property="licenseType" column="license_type"/>
        <result property="licenseTimeStart" column="license_time_start"/>
        <result property="carrierId" column="carrier_id"/>
        <result property="homeAddress" column="home_address"/>
        <result property="emergencyName" column="emergency_name"/>
        <result property="emergencyPhone" column="emergency_phone"/>
        <result property="idCardFront" column="id_card_front"/>
        <result property="idCardBack" column="id_card_back"/>
        <result property="drivingLicenseFront" column="driving_license_front"/>
        <result property="drivingLicenseBack" column="driving_license_back"/>
        <result property="otherQualification" column="other_qualification"/>
        <result property="isValid" column="is_valid"/>
        <result property="revision" column="revision"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
  </resultMap>
</mapper>