<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsSortingTemplateMapper">

  <resultMap id="tmsSortingTemplateMap" type="com.jygjexp.jynx.tms.entity.TmsSortingTemplateEntity">
        <id property="id" column="id"/>
        <result property="templateName" column="template_name"/>
        <result property="templateCode" column="template_code"/>
        <result property="businessType" column="business_type"/>
        <result property="sortingRuleType" column="sorting_rule_type"/>
        <result property="status" column="status"/>
        <result property="siteId" column="site_id"/>
        <result property="revision" column="revision"/>
        <result property="remark" column="remark"/>
        <result property="gridId" column="grid_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
  </resultMap>
      <insert id="add" useGeneratedKeys="true" keyProperty="id">
            insert
            into tms_sorting_template (id,template_name, template_code, business_type, sorting_rule_type, site_id,
                                       revision, remark, grid_id, create_by, create_time, update_by, update_time,
                                       del_flag, tenant_id)
            values (#{id},#{templateName}, #{templateCode}, #{businessType}, #{sortingRuleType}, #{siteId},
                    #{revision}, #{remark}, #{gridId}, #{createBy}, #{createTime}, #{updateBy}, #{updateTime},
                    #{delFlag}, #{tenantId})
      </insert>
</mapper>