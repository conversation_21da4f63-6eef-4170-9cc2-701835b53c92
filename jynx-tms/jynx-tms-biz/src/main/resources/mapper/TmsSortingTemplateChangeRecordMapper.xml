<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsSortingTemplateChangeRecordMapper">

    <resultMap id="tmsSortingTemplateChangeRecordMap" type="com.jygjexp.jynx.tms.entity.TmsSortingTemplateChangeRecordEntity">
        <id property="id" column="id"/>
        <result property="templateId" column="template_id"/>
        <result property="templateName" column="template_name"/>
        <result property="templateCode" column="template_code"/>
        <result property="templateBusinessType" column="template_business_type"/>
        <result property="operationType" column="operation_type"/>
        <result property="beforeContent" column="before_content"/>
        <result property="afterContent" column="after_content"/>
        <result property="operateTime" column="operate_time"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>
</mapper>
