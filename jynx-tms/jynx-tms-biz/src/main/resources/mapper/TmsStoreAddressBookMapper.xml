<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsStoreAddressBookMapper">

    <resultMap id="tmsStoreAddressBookMap" type="com.jygjexp.jynx.tms.entity.TmsStoreAddressBookEntity">
        <id property="id" column="id"/>
        <result property="storeCustomerId" column="store_customer_id"/>
        <result property="contacts" column="contacts"/>
        <result property="phone" column="phone"/>
        <result property="country" column="country"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="postalCode" column="postal_code"/>
        <result property="type" column="type"/>
        <result property="subType" column="sub_type"/>
        <result property="address" column="address"/>
        <result property="revision" column="revision"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>
</mapper>
