<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsVehicleInfoMapper">

    <resultMap id="tmsVehicleInfoMap" type="com.jygjexp.jynx.tms.entity.TmsVehicleInfoEntity">
        <id property="id" column="id"/>
        <result property="driverId" column="driver_id"/>
        <result property="carrierId" column="carrier_id"/>
        <result property="contactPhone" column="contact_phone"/>
        <result property="licensePlate" column="license_plate"/>
        <result property="vehicleType" column="vehicle_type"/>
        <result property="vehicleColor" column="vehicle_color"/>
        <result property="vehicleImageUrl" column="vehicle_image_url"/>
        <result property="loadCapacity" column="load_capacity"/>
        <result property="volume" column="volume"/>
        <result property="purchaseDate" column="purchase_date"/>
        <result property="registrationDate" column="registration_date"/>
        <result property="insuranceStartDate" column="insurance_start_date"/>
        <result property="insuranceEndDate" column="insurance_end_date"/>
        <result property="insuranceDocumentUrl" column="insurance_document_url"/>
        <result property="length" column="length"/>
        <result property="width" column="width"/>
        <result property="height" column="height"/>
        <result property="cargoType" column="cargo_type"/>
        <result property="isValid" column="is_valid"/>
        <result property="ownershipType" column="ownership_type"/>
        <result property="warehouseLength" column="warehouse_length"/>
        <result property="warehouseWidth" column="warehouse_width"/>
        <result property="warehouseHeight" column="warehouse_height"/>
        <result property="revision" column="revision"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>
</mapper>