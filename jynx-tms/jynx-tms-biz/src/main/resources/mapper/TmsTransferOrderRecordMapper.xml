<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsTransferOrderRecordMapper">

  <resultMap id="tmsTransferOrderRecordMap" type="com.jygjexp.jynx.tms.entity.TmsTransferOrderRecordEntity">
        <id property="id" column="id"/>
        <result property="orderNo" column="order_no"/>
        <result property="oldDriverId" column="old_driver_id"/>
        <result property="oldDriverName" column="old_driver_name"/>
        <result property="currentDriverId" column="current_driver_id"/>
        <result property="currentDriverName" column="current_driver_name"/>
        <result property="type" column="type"/>
        <result property="reason" column="reason"/>
        <result property="status" column="status"/>
        <result property="siteId" column="site_id"/>
        <result property="revision" column="revision"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
  </resultMap>
</mapper>