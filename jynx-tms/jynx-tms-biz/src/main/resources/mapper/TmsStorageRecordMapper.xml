<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsStorageRecordMapper">

    <resultMap id="tmsStorageRecordMap" type="com.jygjexp.jynx.tms.entity.TmsStorageRecordEntity">
        <id property="id" column="id"/>
        <result property="warehouseId" column="warehouse_id"/>
        <result property="warehouseType" column="warehouse_type"/>
        <result property="storageOrderNumber" column="storage_order_number"/>
        <result property="cargoQuantity" column="cargo_quantity"/>
        <result property="totalWeight" column="total_weight"/>
        <result property="totalVolume" column="total_volume"/>
        <result property="revision" column="revision"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>
</mapper>