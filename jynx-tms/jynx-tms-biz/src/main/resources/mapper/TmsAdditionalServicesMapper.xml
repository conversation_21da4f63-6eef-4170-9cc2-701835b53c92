<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsAdditionalServicesMapper">

    <resultMap id="tmsAdditionalServicesMap" type="com.jygjexp.jynx.tms.entity.TmsAdditionalServicesEntity">
        <id property="id" column="id"/>
        <result property="orderId" column="order_id"/>
        <result property="customerOrderNumber" column="customer_order_number"/>
        <result property="entrustedOrderNumber" column="entrusted_order_number"/>
        <result property="additionalServiceType" column="additional_service_type"/>
        <result property="additionalServiceValue" column="additional_service_value"/>
        <result property="additionalServiceTime" column="additional_service_time"/>
        <result property="isUrban" column="is_urban"/>
        <result property="revision" column="revision"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>
</mapper>