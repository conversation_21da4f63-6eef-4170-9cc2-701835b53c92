<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsPreRoutePlanMapper">

  <resultMap id="tmsPreRoutePlanMap" type="com.jygjexp.jynx.tms.entity.TmsPreRoutePlanEntity">
        <id property="id" column="id"/>
        <result property="routeName" column="route_name"/>
        <result property="routeType" column="route_type"/>
        <result property="orderCount" column="order_count"/>
        <result property="driverCount" column="driver_count"/>
        <result property="status" column="status"/>
        <result property="siteId" column="site_id"/>
        <result property="revision" column="revision"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
  </resultMap>
</mapper>