<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsEntrustedOrderMapper">

    <resultMap id="tmsEntrustedOrderMap" type="com.jygjexp.jynx.tms.entity.TmsEntrustedOrderEntity">
        <id property="id" column="id"/>
        <result property="entrustedOrderNumber" column="entrusted_order_number"/>
        <result property="customerOrderNumber" column="customer_order_number"/>
        <result property="isSubOrderNo" column="is_sub_order_no"/>
        <result property="shipmentNo" column="shipment_no"/>
        <result property="customerId" column="customer_id"/>
        <result property="orderStatus" column="order_status"/>
        <result property="auditStatus" column="audit_status"/>
        <result property="shipperName" column="shipper_name"/>
        <result property="shipperPhone" column="shipper_phone"/>
        <result property="shipperCountry" column="shipper_country"/>
        <result property="origin" column="origin"/>
        <result property="shipperPostalCode" column="shipper_postal_code"/>
        <result property="shipperAddress" column="shipper_address"/>
        <result property="estimatedShippingTimeStart" column="estimated_shipping_time_start"/>
        <result property="estimatedShippingTimeEnd" column="estimated_shipping_time_end"/>
        <result property="receiverName" column="receiver_name"/>
        <result property="receiverPhone" column="receiver_phone"/>
        <result property="destination" column="destination"/>
        <result property="destPostalCode" column="dest_postal_code"/>
        <result property="destAddress" column="dest_address"/>
        <result property="estimatedArrivalTimeStart" column="estimated_arrival_time_start"/>
        <result property="estimatedArrivalTimeEnd" column="estimated_arrival_time_end"/>
        <result property="isTailgatePickup" column="is_tailgate_pickup"/>
        <result property="isTailgateUnloaded" column="is_tailgate_unloaded"/>
        <result property="orderType" column="order_type"/>
        <result property="transportType" column="transport_type"/>
        <result property="cargoType" column="cargo_type"/>
        <result property="addressType" column="address_type"/>
        <result property="businessModel" column="business_model"/>
        <result property="cargoQuantity" column="cargo_quantity"/>
        <result property="totalWeight" column="total_weight"/>
        <result property="totalVolume" column="total_volume"/>
        <result property="totalFreight" column="total_freight"/>
        <result property="shipperLatLng" column="shipper_lat_lng"/>
        <result property="receiverLatLng" column="receiver_lat_lng"/>
        <result property="operator" column="operator"/>
        <result property="carrierId" column="carrier_id"/>
        <result property="revision" column="revision"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>
</mapper>