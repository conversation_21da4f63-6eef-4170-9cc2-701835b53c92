<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsSortingRuleMapper">

  <resultMap id="tmsSortingRuleMap" type="com.jygjexp.jynx.tms.entity.TmsSortingRuleEntity">
        <id property="id" column="id"/>
        <result property="templateId" column="template_id"/>
        <result property="businessType" column="business_type"/>
        <result property="merchantCode" column="merchant_code"/>
        <result property="merchantName" column="merchant_name"/>
        <result property="routeNumber" column="routeNumber"/>
        <result property="warehouseName" column="warehouse_name"/>
        <result property="conditionSpel" column="condition_spel"/>
        <result property="conditions" column="conditions"/>
        <result property="status" column="status"/>
        <result property="siteId" column="site_id"/>
        <result property="revision" column="revision"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
  </resultMap>
</mapper>