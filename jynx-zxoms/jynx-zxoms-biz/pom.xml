<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
            http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.jygjexp</groupId>
		<artifactId>jynx-zxoms</artifactId>
		<version>5.6.0</version>
	</parent>

	<artifactId>jynx-zxoms-biz</artifactId>

	<dependencies>
		<!--必备: undertow容器-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-undertow</artifactId>
		</dependency>
		<!--必备: spring boot web-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<!--必备: 注册中心客户端-->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
		</dependency>
		<!--必备: 配置中心客户端-->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
		</dependency>
		<!--必备: 操作数据源相关-->
		<dependency>
			<groupId>com.jygjexp</groupId>
			<artifactId>jynx-common-data</artifactId>
		</dependency>
		<!--必备：jynx安全模块-->
		<dependency>
			<groupId>com.jygjexp</groupId>
			<artifactId>jynx-common-security</artifactId>
		</dependency>
		<!--必备：xss 过滤模块-->
		<dependency>
			<groupId>com.jygjexp</groupId>
			<artifactId>jynx-common-xss</artifactId>
		</dependency>
		<!--必备: sentinel 依赖-->
		<dependency>
			<groupId>com.jygjexp</groupId>
			<artifactId>jynx-common-sentinel</artifactId>
		</dependency>
		<!--必备: feign 依赖-->
		<dependency>
			<groupId>com.jygjexp</groupId>
			<artifactId>jynx-common-feign</artifactId>
		</dependency>
		<!--必备: 依赖api模块-->
		<dependency>
			<groupId>com.jygjexp</groupId>
			<artifactId>jynx-zxoms-api</artifactId>
			<version>5.6.0</version>
		</dependency>
		<!--必备: log 依赖-->
		<dependency>
			<groupId>com.jygjexp</groupId>
			<artifactId>jynx-common-log</artifactId>
		</dependency>
		<!--选配: mybatis 依赖 -->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-boot-starter</artifactId>
		</dependency>
		<!--选配： druid 连接池 -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-starter</artifactId>
		</dependency>
		<!--选配: mysql 数据库驱动 -->
		<dependency>
			<groupId>com.mysql</groupId>
			<artifactId>mysql-connector-j</artifactId>
		</dependency>
		<!--选配: swagger文档-->
		<dependency>
			<groupId>com.jygjexp</groupId>
			<artifactId>jynx-common-swagger</artifactId>
		</dependency>
		<!--测试: spring boot test-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
		</dependency>
        <dependency>
            <groupId>com.jygjexp</groupId>
            <artifactId>jynx-zxoms-api</artifactId>
            <version>5.6.0</version>
            <scope>compile</scope>
        </dependency>
		<!--依赖基础数据模块		-->
<!--		<dependency>-->
<!--			<groupId>com.jygjexp</groupId>-->
<!--			<artifactId>jynx-basic-api</artifactId>-->
<!--			<version>5.6.0</version>-->
<!--		</dependency>-->

<!--		<dependency>-->
<!--			<groupId>com.jygjexp</groupId>-->
<!--			<artifactId>jynx-daemon-quartz</artifactId>-->
<!--			<version>5.6.0</version>-->
<!--		</dependency>-->

		<!--	xxl-job 定时作业调度	-->
		<dependency>
			<groupId>com.jygjexp</groupId>
			<artifactId>jynx-common-job</artifactId>
			<version>5.6.0</version>
		</dependency>

		<!--hutool工具包		-->
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>5.8.16</version>
		</dependency>
		<!--获取二维码 https://mvnrepository.com/artifact/com.google.zxing/core -->
		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>core</artifactId>
			<version>3.5.1</version>
		</dependency>
		<!--获取二维码barcode https://mvnrepository.com/artifact/net.sf.barcode4j/barcode4j -->
		<dependency>
			<groupId>net.sf.barcode4j</groupId>
			<artifactId>barcode4j</artifactId>
			<version>2.1</version>
		</dependency>

		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>4.1.2</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/io.github.route4me/route4me-java-sdk -->
		<dependency>
			<groupId>io.github.route4me</groupId>
			<artifactId>route4me-java-sdk</artifactId>
			<version>1.15.0</version>
			<scope>system</scope>
			<systemPath>${pom.basedir}/libs/route4me-java-sdk-1.15.0.jar</systemPath>
		</dependency>

		<!--引入freemarker模版引擎		-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-freemarker</artifactId>
		</dependency>

		<!--阿里云SDK core包		-->
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-core</artifactId>
			<version>4.6.4</version>
		</dependency>

		<!--	WebSocket	-->
		<dependency>
			<groupId>com.jygjexp</groupId>
			<artifactId>jynx-common-websocket</artifactId>
		</dependency>

		<!--	PDF打印依赖相关	-->
		<!-- https://mvnrepository.com/artifact/com.itextpdf/itextpdf -->
		<dependency>
			<groupId>com.itextpdf</groupId>
			<artifactId>itextpdf</artifactId>
			<version>5.5.13.2</version>
		</dependency>
		<!--	PDF打印依赖相关	-->
		<!-- https://mvnrepository.com/artifact/com.itextpdf/itext-asian -->
		<dependency>
			<groupId>com.itextpdf</groupId>
			<artifactId>itext-asian</artifactId>
			<version>5.2.0</version>
		</dependency>

		<!--	导出模版相关依赖	-->
		<dependency>
			<groupId>com.jygjexp</groupId>
			<artifactId>jynx-common-excel</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.apache.poi</groupId>
					<artifactId>poi-ooxml</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!--	上传文件依赖	-->
		<dependency>
			<groupId>com.jygjexp</groupId>
			<artifactId>jynx-common-oss</artifactId>
		</dependency>

		<!-- 阿里云oss依赖 -->
		<dependency>
			<groupId>com.aliyun.oss</groupId>
			<artifactId>aliyun-sdk-oss</artifactId>
			<version>3.17.2</version>
		</dependency>

		<!--		redis-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis</artifactId>
		</dependency>

		<!--		<dependency>-->
		<!--			<groupId>com.jygjexp</groupId>-->
		<!--			<artifactId>jynx-common-gateway</artifactId>-->
		<!--		</dependency>-->

		<!--	图片缩放及水印相关	-->
		<!-- https://mvnrepository.com/artifact/net.coobird/thumbnailator -->
		<dependency>
			<groupId>net.coobird</groupId>
			<artifactId>thumbnailator</artifactId>
			<version>0.4.19</version>
		</dependency>
        <dependency>
            <groupId>org.springframework.security.oauth</groupId>
            <artifactId>spring-security-oauth2</artifactId>
        </dependency>

		<!--minio文件系统-->
		<dependency>
			<groupId>io.minio</groupId>
			<artifactId>minio</artifactId>
			<version>8.0.3</version>
			<scope>compile</scope>
		</dependency>

	</dependencies>
	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<!--让maven编译的时候能将第三方包编入-->
				<configuration>
					<includeSystemScope>true</includeSystemScope>
				</configuration>
			</plugin>
			<plugin>
				<groupId>io.fabric8</groupId>
				<artifactId>docker-maven-plugin</artifactId>
			</plugin>
			<!--			<plugin>-->
			<!--				<groupId>org.apache.maven.plugins</groupId>-->
			<!--				<artifactId>maven-compiler-plugin</artifactId>-->
			<!--				<version>3.8.1</version>-->
			<!--				<configuration>-->
			<!--					<source>1.8</source>-->
			<!--					<target>1.8</target>-->
			<!--					<encoding>UTF-8</encoding>-->
			<!--					<compilerArguments>-->
			<!--						<extdirs>${project.basedir}/libs</extdirs>-->
			<!--					</compilerArguments>-->
			<!--				</configuration>-->
			<!--			</plugin>-->

			<!--			<plugin>-->
			<!--				<groupId>org.apache.maven.plugins</groupId>-->
			<!--				<artifactId>maven-assembly-plugin</artifactId>-->
			<!--				<version>3.4.0</version>-->
			<!--				<executions>-->
			<!--					<execution>-->
			<!--						<id>attach-external-jars</id>-->
			<!--						<phase>package</phase>-->
			<!--						<goals>-->
			<!--							<goal>single</goal>-->
			<!--						</goals>-->
			<!--						<configuration>-->
			<!--							<descriptorRefs>-->
			<!--								<descriptorRef>jar-with-dependencies</descriptorRef>-->
			<!--							</descriptorRefs>-->
			<!--							<appendAssemblyId>false</appendAssemblyId>-->
			<!--							<outputDirectory>${project.build.directory}</outputDirectory>-->
			<!--							<archive>-->
			<!--								<manifest>-->
			<!--									<mainClass>com.example.Application</mainClass>-->
			<!--								</manifest>-->
			<!--							</archive>-->
			<!--							<includeBaseDirectory>false</includeBaseDirectory>-->
			<!--						</configuration>-->
			<!--					</execution>-->
			<!--				</executions>-->
			<!--			</plugin>-->


		</plugins>

		<!--		<resources>-->
		<!--			<resource>-->
		<!--				<directory>src/main/resources</directory>-->
		<!--				<includes>-->
		<!--					<include>**/*.yml</include>-->
		<!--				</includes>-->
		<!--				<filtering>true</filtering>-->
		<!--			</resource>-->
		<!--			<resource>-->
		<!--				<directory>${basedir}/src/main/resources/libs/</directory>-->
		<!--			</resource>-->

		<!--			&lt;!&ndash;外部jar包地址&ndash;&gt;-->
		<!--			<resource>-->
		<!--				<directory>${basedir}/src/main/resources/libs/</directory>-->
		<!--				<targetPath>BOOT-INF/lib/</targetPath>-->
		<!--				<includes>-->
		<!--					<include>**/*.jar</include>-->
		<!--				</includes>-->
		<!--			</resource>-->
		<!--		</resources>-->

	</build>
</project>
