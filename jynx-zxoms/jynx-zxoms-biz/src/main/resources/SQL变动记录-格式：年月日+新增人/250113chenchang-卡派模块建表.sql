    -- 备份仓库表
    DROP TABLE IF EXISTS tms_warehouse;
    CREATE TABLE `tms_warehouse`
    (
        `warehouse_id`   int                                                           NOT NULL AUTO_INCREMENT COMMENT '主键ID',
        `warehouse_code` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '仓库编号',
        `warehouse_name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '仓库名称',
        `address`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '地址',
        `contacts`       varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '联系人',
        `phone`          varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '联系电话',
        `is_valid`       tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
        `position_count` varchar(255)                                                  NOT NULL DEFAULT '0' COMMENT '库位数',
        `tenant_id`      bigint                                                        NOT NULL DEFAULT '1' COMMENT '租户号',
        `revision`       bigint                                                        NOT NULL DEFAULT '1' COMMENT '乐观锁',
        `remark`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
        `create_by`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '创建人',
        `create_time`    datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
        `update_by`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '更新人',
        `update_time`    datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
        `del_flag`       char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci               DEFAULT '0' COMMENT '删除标志',
        `country_name`   varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '国家',
        `province_name`  varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '省',
        `city_name`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '城市',
        `timezone`       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '所在时区',
        `business_hours` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '营业时间',
        `lat` double DEFAULT '0' COMMENT '纬度',
        `lng` double DEFAULT '0' COMMENT '经度',
        `postal_code`    varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '邮编',
        `country_id`     int                                                           NOT NULL COMMENT '国家id',
        `province_id`    int                                                           NOT NULL COMMENT '省id',
        `city_id`        int                                                           NOT NULL COMMENT '市/地区id',
        PRIMARY KEY (`warehouse_id`),
        UNIQUE KEY `warehouse_code_UNIQUE` (`warehouse_code`) USING BTREE
    ) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='卡派仓库';

    -- 备份托盘表
    DROP TABLE IF EXISTS tms_tray;
    CREATE TABLE `tms_tray`
    (
        `id`                  bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
        `warehouse_id`        bigint unsigned NOT NULL DEFAULT '0' COMMENT '仓库id',
        `position_id`         bigint                                                        NOT NULL DEFAULT '0' COMMENT '库位 托盘位置',
        `history_position_id` bigint                                                        NOT NULL DEFAULT '0' COMMENT '库位托盘位置(历史记录)',
        `order_id_list`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单列表',
        `title`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
        `code`                varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '代码',
        `bag_count`           int                                                           NOT NULL DEFAULT '0' COMMENT '箱数',
        `status`              int                                                           NOT NULL DEFAULT '1' COMMENT '状态',
        `tenant_id`           bigint                                                        NOT NULL DEFAULT '1' COMMENT '租户号',
        `revision`            bigint                                                        NOT NULL DEFAULT '1' COMMENT '乐观锁',
        `remark`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
        `create_by`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '创建人',
        `create_time`         datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
        `update_by`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '更新人',
        `update_time`         datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
        `del_flag`            char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci               DEFAULT '0' COMMENT '删除标志',
        PRIMARY KEY (`id`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='卡派 托盘';

    -- 备份库位表
    CREATE TABLE `tms_position`
    (
        `id`              bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
        `warehouse_id`    bigint unsigned NOT NULL DEFAULT '0' COMMENT '仓库ID',
        `tray_id`         bigint                                                        NOT NULL DEFAULT '0' COMMENT '托盘ID',
        `title`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
        `code`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '代码',
        `position_column` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '位置列',
        `position_row`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '位置行',
        `status`          int                                                           NOT NULL DEFAULT '1' COMMENT '状态',
        `tenant_id`       bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户号',
        `revision`        int                                                           NOT NULL DEFAULT '0' COMMENT '乐观锁',
        `remark`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
        `create_by`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建人',
        `create_time`     datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
        `update_by`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '更新人',
        `update_time`     datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
        `del_flag`        char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL DEFAULT '0' COMMENT '删除标记',
        PRIMARY KEY (`id`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='卡派库位';

    -- 备份托盘调整表
    CREATE TABLE `tms_tray_adjust`
    (
        `id`                 bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
        `warehouse_id`       bigint unsigned NOT NULL DEFAULT '0' COMMENT '仓库id',
        `position_id_list`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '库位',
        `tray_id_list`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '托盘',
        `order_item_id_list` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单箱',
        `status`             int                                                           NOT NULL DEFAULT '0' COMMENT '状态',
        `execution_time`     datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '执行时间',
        `tenant_id`          bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户号',
        `revision`           int                                                           NOT NULL DEFAULT '0' COMMENT '乐观锁',
        `remark`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
        `create_by`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建人',
        `create_time`        datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
        `update_by`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '更新人',
        `update_time`        datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
        `del_flag`           char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL DEFAULT '0' COMMENT '删除标记',
        PRIMARY KEY (`id`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='卡派托盘调整';

    -- 备份提货单表
    CREATE TABLE `tms_waybill`
    (
        `id`                        bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
        `customer_id`               bigint unsigned NOT NULL DEFAULT '0' COMMENT '客户id',
        `warehouse_id`              bigint unsigned NOT NULL DEFAULT '0' COMMENT '仓库id',
        `service_provider_id`       bigint unsigned NOT NULL DEFAULT '0' COMMENT '服务商id',
        `business_category`         int                                                           NOT NULL DEFAULT '0' COMMENT '业务类型 1 空运 2 海运',
        `service_category_id_list`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '服务列表',
        `container_id`              bigint unsigned NOT NULL DEFAULT '0' COMMENT '货柜id',
        `business_number`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '业务编号',
        `waybill_number`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '提货单号',
        `transport_title`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '飞机名/船名',
        `transport_number`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '航班号/船次',
        `transport_company`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '航司/船司',
        `start_port`                varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '起始港',
        `end_port`                  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '目的港',
        `estimated_time_of_arrival` datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '预计到港时间',
        `container_size`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '板型/箱型',
        `container_number`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '板号/柜号',
        `item_count`                int                                                           NOT NULL DEFAULT '0' COMMENT '箱数',
        `operator_id`               bigint unsigned NOT NULL DEFAULT '0' COMMENT '操作人员',
        `business_id`               bigint unsigned NOT NULL DEFAULT '0' COMMENT '业务人员',
        `customer_service_id`       bigint unsigned NOT NULL DEFAULT '0' COMMENT '客服人员',
        `contract_id`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '合约号',
        `gross_weight`              decimal(18, 3)                                                NOT NULL DEFAULT '0.000' COMMENT '毛重',
        `gross_volume`              decimal(18, 2)                                                NOT NULL DEFAULT '0.00' COMMENT '体积',
        `tenant_id`                 bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户号',
        `revision`                  int                                                           NOT NULL DEFAULT '0' COMMENT '乐观锁',
        `remark`                    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
        `create_by`                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建人',
        `create_time`               datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
        `update_by`                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '更新人',
        `update_time`               datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
        `del_flag`                  char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL DEFAULT '0' COMMENT '删除标记',
        PRIMARY KEY (`id`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='卡派-提货单';

    -- 备份出仓单表
    CREATE TABLE `tms_outbound_order`
    (
        `id`                     bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
        `prep_outbound_order_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '预出仓单id',
        `out_warehouse_code`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '出仓编码',
        `warehouse_id`           bigint unsigned NOT NULL DEFAULT '0' COMMENT '仓库id',
        `consignee_id`           bigint unsigned NOT NULL DEFAULT '0' COMMENT '收件人id',
        `container_number`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '板号/柜号',
        `shipment_appointment`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收货码',
        `order_id_list`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单id',
        `fba_appointment_no`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'FBA预约编号',
        `tray_id_list`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '托盘列表',
        `position_id_list`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '库位列表',
        `item_id_list`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '箱袋列表',
        `total_weight`           decimal(18, 3)                                                NOT NULL DEFAULT '0.000' COMMENT '总重量',
        `total_volume`           decimal(18, 2)                                                NOT NULL DEFAULT '0.00' COMMENT '总体积',
        `out_time`               datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '预出仓时间',
        `status`                 int                                                           NOT NULL DEFAULT '0' COMMENT '状态',
        `car_number`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '车牌号',
        `truck_company`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '卡车公司',
        `driver`                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '司机',
        `estimated_arrival_time` datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '预计到达时间',
        `tenant_id`              bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户号',
        `revision`               int                                                           NOT NULL DEFAULT '0' COMMENT '乐观锁',
        `remark`                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
        `create_by`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建人',
        `create_time`            datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
        `update_by`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '更新人',
        `update_time`            datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
        `del_flag`               char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL DEFAULT '0' COMMENT '删除标记',
        PRIMARY KEY (`id`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='卡派 出仓单';

-- 创建客户(货主)表
CREATE TABLE `tms_customer`
(
    `id`                bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `customer_name`     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '客户名称',
    `customer_name_cn`  varchar(50) COLLATE utf8mb4_general_ci                                 DEFAULT NULL COMMENT '客户名称 中文',
    `customer_code`     varchar(50) COLLATE utf8mb4_general_ci                        NOT NULL DEFAULT '' COMMENT '客户编码',
    `category`          varchar(50) COLLATE utf8mb4_general_ci                                 DEFAULT NULL COMMENT '客户类型',
    `sector`            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '所属行业',
    `customer_level`    varchar(50) COLLATE utf8mb4_general_ci                                 DEFAULT NULL COMMENT '客户级别',
    `customer_label`    varchar(100) COLLATE utf8mb4_general_ci                                DEFAULT NULL COMMENT '客户标签',
    `region`            varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '区域',
    `company_address`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公司地址',
    `postal_code`       varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '地址邮编',
    `business_license`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '营业执照',
    `contact_person`    varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '联系人姓名',
    `phone`             varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '手机号',
    `department`        varchar(50) COLLATE utf8mb4_general_ci                                 DEFAULT NULL COMMENT '部门',
    `position`          varchar(50) COLLATE utf8mb4_general_ci                                 DEFAULT NULL COMMENT '职位',
    `email`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '邮箱',
    `warehouse_address` varchar(255) COLLATE utf8mb4_general_ci                                DEFAULT NULL COMMENT '仓库地址',
    `is_valid`          tinyint(1) NOT NULL DEFAULT '1' COMMENT '启用状态：0 禁用，1 启用',
    `tenant_id`         bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户号',
    `revision`          int                                                           NOT NULL DEFAULT '0' COMMENT '乐观锁',
    `remark`            varchar(255) COLLATE utf8mb4_general_ci                                DEFAULT NULL COMMENT '备注',
    `create_by`         varchar(100) COLLATE utf8mb4_general_ci                                DEFAULT NULL COMMENT '创建人',
    `create_time`       datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
    `update_by`         varchar(100) COLLATE utf8mb4_general_ci                                DEFAULT NULL COMMENT '更新人',
    `update_time`       datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
    `del_flag`          char(1) COLLATE utf8mb4_general_ci                            NOT NULL DEFAULT '0' COMMENT '删除标记：0 未删除，1 已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_customer_code` (`customer_code`) COMMENT '客户编码唯一索引',
    KEY                 `idx_phone` (`phone`) COMMENT '手机号索引'
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='卡派-客户信息表';

    -- 备份货柜表
    CREATE TABLE `tms_container`
    (
        `id`                        bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
        `customer_id`               bigint unsigned NOT NULL DEFAULT '0' COMMENT '客户id',
        `warehouse_id`              bigint unsigned NOT NULL DEFAULT '0' COMMENT '仓库id',
        `service_provider_id`       bigint unsigned NOT NULL DEFAULT '0' COMMENT '服务商id',
        `business_category`         int                                                           NOT NULL DEFAULT '0' COMMENT '业务类型 1 空运 2 海运',
        `service_category_id_list`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '服务列表',
        `business_number`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '业务编号',
        `waybill_number_list`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '提货单号/主单号',
        `transport_title`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '飞机名/船名',
        `transport_number`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '航班号/船次',
        `transport_company`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '航司/船司',
        `start_port`                varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '起始港',
        `end_port`                  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '目的港',
        `estimated_time_of_arrival` datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '预计到港时间',
        `container_size`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '板型/箱型',
        `container_number`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '板号/柜号',
        `total_item_count`          int                                                           NOT NULL DEFAULT '0' COMMENT '总箱数',
        `operator_id`               bigint unsigned NOT NULL DEFAULT '0' COMMENT '操作人员',
        `business_id`               bigint unsigned NOT NULL DEFAULT '0' COMMENT '业务人员',
        `customer_service_id`       bigint unsigned NOT NULL DEFAULT '0' COMMENT '客服人员',
        `gross_weight`              decimal(18, 3)                                                NOT NULL DEFAULT '0.000' COMMENT '毛重',
        `gross_volume`              decimal(18, 2)                                                NOT NULL DEFAULT '0.00' COMMENT '体积',
        `tenant_id`                 bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户号',
        `revision`                  int                                                           NOT NULL DEFAULT '0' COMMENT '乐观锁',
        `remark`                    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
        `create_by`                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建人',
        `create_time`               datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
        `update_by`                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '更新人',
        `update_time`               datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
        `del_flag`                  char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL DEFAULT '0' COMMENT '删除标记',
        PRIMARY KEY (`id`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='卡派货柜';

    -- 备份出仓计划单（预出库单）表
    CREATE TABLE `tms_prep_outbound_order`
    (
        `id`                     bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
        `warehouse_id`           bigint unsigned NOT NULL DEFAULT '0' COMMENT '仓库id',
        `position_id_list`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '库位列表',
        `tray_id_list`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '托盘列表',
        `history_tray_id_list`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '托盘列表(历史记录)',
        `order_id_list`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单id',
        `fba_appointment_no`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'FBA预约编号',
        `reference_code`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'refrence_code',
        `total_weight`           decimal(18, 3)                                                NOT NULL DEFAULT '0.000' COMMENT '总重量',
        `total_volume`           decimal(18, 2)                                                NOT NULL DEFAULT '0.00' COMMENT '总体积',
        `out_time`               datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '预出仓时间',
        `status`                 int                                                           NOT NULL DEFAULT '0' COMMENT '状态',
        `execution_by`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '执行人',
        `execution_time`         datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '执行时间',
        `car_number`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '车牌号',
        `truck_company`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '卡车公司',
        `driver`                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '司机',
        `appointment_time`       datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '预约时间',
        `estimated_arrival_time` datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '预计到达时间',
        `tenant_id`              bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户号',
        `revision`               int                                                           NOT NULL DEFAULT '0' COMMENT '乐观锁',
        `remark`                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
        `create_by`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建人',
        `create_time`            datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
        `update_by`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '更新人',
        `update_time`            datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
        `del_flag`               char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL DEFAULT '0' COMMENT '删除标记',
        PRIMARY KEY (`id`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='卡派出仓计划单';

-- 创建订单申报信息表
CREATE TABLE `tms_delivery_order_declare`
(
    `id`                    bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `order_id`              bigint unsigned NOT NULL DEFAULT '0' COMMENT '订单id',
    `item_id`               bigint unsigned NOT NULL DEFAULT '0',
    `forecast_title`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
    `forecast_title_cn`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称 中文',
    `forecast_quantity`     int                                                           NOT NULL DEFAULT '0' COMMENT '数量',
    `forecast_unit_price`   decimal(18, 2)                                                NOT NULL DEFAULT '0.00' COMMENT '申报价格(单价),单位 USD,必填',
    `forecast_unit_weight`  decimal(18, 3)                                                NOT NULL DEFAULT '0.000' COMMENT '申报重量(单重)，单位 kg',
    `forecast_customs_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '预报海关编码',
    `actual_title`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '实际名称',
    `actual_title_cn`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '实际名称 中文',
    `actual_quantity`       int                                                           NOT NULL DEFAULT '0' COMMENT '实际数量',
    `actual_unit_price`     decimal(18, 2)                                                NOT NULL DEFAULT '0.00' COMMENT '申报价格(单价),单位 USD,必填',
    `actual_unit_weight`    decimal(18, 3)                                                NOT NULL DEFAULT '0.000' COMMENT '申报重量(单重)，单位 kg',
    `actual_customs_code`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '实际海关编码',
    `unit_code`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'PCE' COMMENT '申报单位',
    `product_url`           text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '产品url',
    `image_url`             text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '图片url',
    `brand`                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '品牌',
    `model`                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '型号',
    `material`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '材质',
    `purpose`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用途',
    `currency_code`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'USD' COMMENT '申报币种，默认:USD',
    `sku`                   text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'SKU',
    `tenant_id`             bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户号',
    `revision`              int                                                           NOT NULL DEFAULT '0' COMMENT '乐观锁',
    `remark`                varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
    `create_by`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建人;',
    `create_time`           datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间;',
    `update_by`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '更新人;',
    `update_time`           datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间;',
    `del_flag`              char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL DEFAULT '0' COMMENT '删除标记;',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `id_unique` (`tenant_id`,`update_time`,`del_flag`,`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='卡派订单申报信息';

-- 创建订单签收照片表
CREATE TABLE `tms_order_sign_image`
(
    `id`           bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `order_id`     int                                                           NOT NULL COMMENT '订单ID',
    `order_no`     varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '订单编号',
    `pkg_no`       varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '包裹编号',
    `pkg_image`    varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '包裹图',
    `put_image`    varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '放置图',
    `order_status` int                                                                    DEFAULT NULL COMMENT '订单状态',
    `driver_name`  varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '司机名称',
    `driver_id`    bigint                                                        NOT NULL DEFAULT '0' COMMENT '司机ID',
    `staff_id`     bigint                                                        NOT NULL DEFAULT '0' COMMENT '员工ID',
    `lat`          decimal(10, 6)                                                NOT NULL DEFAULT '0.000000' COMMENT '纬度',
    `lng`          decimal(10, 6)                                                NOT NULL DEFAULT '0.000000' COMMENT '经度',
    `distance`     decimal(10, 2)                                                NOT NULL DEFAULT '-1.00' COMMENT '离收件距离',
    `tenant_id`    bigint                                                        NOT NULL DEFAULT '1' COMMENT '租户号',
    `revision`     bigint                                                        NOT NULL DEFAULT '1' COMMENT '乐观锁',
    `remark`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
    `create_by`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '创建人',
    `create_time`  datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
    `update_by`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '更新人',
    `update_time`  datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
    `del_flag`     char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci               DEFAULT '0' COMMENT '删除标志',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='卡派 签收照片';

-- 创建卡派车辆信息表
CREATE TABLE `tms_vehicle_info`
(
    `id`                     bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `driver_id`              bigint                                                        NOT NULL COMMENT '车主ID',
    `carrier_id`             bigint                                                        NOT NULL COMMENT '承运商ID',
    `contact_phone`          varchar(15) COLLATE utf8mb4_general_ci                                 DEFAULT NULL COMMENT '联系电话',
    `license_plate`          varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '车牌号',
    `vehicle_type`           varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '车辆类型',
    `vehicle_color`          varchar(20) COLLATE utf8mb4_general_ci                                 DEFAULT NULL COMMENT '车辆颜色',
    `vehicle_image_url`      varchar(255) COLLATE utf8mb4_general_ci                                DEFAULT NULL COMMENT '车辆图片(存URL路径)',
    `load_capacity`          decimal(10, 2)                                                NOT NULL COMMENT '载重(吨)',
    `volume`                 decimal(10, 3)                                                NOT NULL COMMENT '容积(升)',
    `purchase_date`          date                                                          NOT NULL COMMENT '车辆购买日期',
    `registration_date`      date                                                          NOT NULL COMMENT '车辆登记日期',
    `insurance_start_date`   date                                                          NOT NULL COMMENT '车辆保险有限期(开始时间)',
    `insurance_end_date`     date                                                          NOT NULL COMMENT '车辆保险有限期(结束时间)',
    `insurance_document_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '车辆保险单文件(支持图片和文件(word、PDF最大支持60MB))',
    `length`                 decimal(10, 3)                                                NOT NULL COMMENT '长(m)',
    `width`                  decimal(10, 3)                                                NOT NULL COMMENT '宽(m)',
    `height`                 decimal(10, 3)                                                NOT NULL COMMENT '高(m)',
    `cargo_type`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '可配货物类型',
    `is_valid`               tinyint(1) NOT NULL DEFAULT '1' COMMENT '启用状态：0 禁用，1 启用',
    `tenant_id`              bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户号',
    `revision`               int                                                           NOT NULL DEFAULT '0' COMMENT '乐观锁',
    `remark`                 varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '备注',
    `create_by`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '创建人',
    `create_time`            datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
    `update_by`              varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '更新人',
    `update_time`            datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
    `del_flag`               char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL DEFAULT '0' COMMENT '删除标记：0 未删除，1 已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_license_plate` (`license_plate`),
    KEY                      `idx_carrier_id` (`carrier_id`),
    KEY                      `idx_is_valid` (`is_valid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='卡派车辆信息';