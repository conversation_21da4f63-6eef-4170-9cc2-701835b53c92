DROP TABLE IF EXISTS `nb_postal_code_group`;
CREATE TABLE `nb_postal_code_group`
(
    `id`          bigint                                                        NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `name_zh`     varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '中文名称',
    `name_en`     varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '英文名称',
    `is_valid`    tinyint                                                       NOT NULL DEFAULT '1' COMMENT '启用状态',
    `remark`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT '' COMMENT '备注',
    `tenant_id`   bigint                                                        NOT NULL DEFAULT '1' COMMENT '租户号',
    `revision`    bigint                                                        NOT NULL DEFAULT '1' COMMENT '乐观锁',
    `create_by`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '创建人',
    `create_time` datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
    `update_by`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '更新人',
    `update_time` datetime                                                      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
    `del_flag`    char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci               DEFAULT '0' COMMENT '删除标志',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='邮编分区组';

DROP TABLE IF EXISTS `nb_postal_code_detail`;
CREATE TABLE `nb_postal_code_detail`
(
    `id`                bigint                                                        NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `partition_code`    bigint                                                        NOT NULL COMMENT '分区代码-正整数',
    `type`              tinyint                                                       NOT NULL DEFAULT '1' COMMENT '类型：1精准，2范围',
    `start_postal_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '开始邮编',
    `end_postal_code`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '截止邮编',
    `group_id`          bigint NULL DEFAULT NULL COMMENT '邮编分区id',
    `tenant_id`         bigint                                                        NOT NULL DEFAULT 1 COMMENT '租户号',
    `revision`          bigint                                                        NOT NULL DEFAULT 1 COMMENT '乐观锁',
    `create_by`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '创建人',
    `create_time`       datetime                                                      NOT NULL DEFAULT '2025-01-09 00:00:00' COMMENT '创建时间',
    `update_by`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '更新人',
    `update_time`       datetime                                                      NOT NULL DEFAULT '2025-01-09 00:00:00' COMMENT '更新时间',
    `del_flag`          char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标志',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `id_idx`(`partition_code` ASC, `start_postal_code` ASC, `end_postal_code` ASC, `del_flag` ASC) USING BTREE COMMENT '邮编分区明细-唯一索引'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '邮编分区-明细' ROW_FORMAT = Dynamic;

SET
FOREIGN_KEY_CHECKS = 1;


