package com.jygjexp.jynx.zxoms.nbapp.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.common.core.constant.CacheConstants;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.zxoms.send.constants.NBDConstants;
import com.jygjexp.jynx.zxoms.dto.DriverDto;
import com.jygjexp.jynx.zxoms.dto.OrderDto;
import com.jygjexp.jynx.zxoms.entity.*;
import com.jygjexp.jynx.zxoms.nbapp.controller.annotaiton.AtomApi;
import com.jygjexp.jynx.zxoms.nbapp.dto.APPDriverDto;
import com.jygjexp.jynx.zxoms.nbapp.utils.AccessTokenBuilderKit;
import com.jygjexp.jynx.zxoms.nbapp.utils.HttpKit;
import com.jygjexp.jynx.zxoms.nbapp.utils.RedisUtil;
import com.jygjexp.jynx.zxoms.nbapp.vo.APPDriverVo;
import com.jygjexp.jynx.zxoms.response.LocalizedR;
import com.jygjexp.jynx.zxoms.send.service.*;
import com.jygjexp.jynx.zxoms.send.utils.*;
import com.route4me.sdk.services.users.User;
import com.route4me.sdk.services.v5.V5Array;
import com.route4me.sdk.services.v5.VehicleCapacity;
import com.route4me.sdk.services.vehicles.Vehicles;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: chenchang
 * @Description: APP-司机相关
 * @Date: 2024/11/11 11:34
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/app/driver" )
@Tag(description = "appdriver" , name = "APP-司机相关" )
//@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class DriverController extends BaseController{
    private final NbDriverService driverService;
    private final NbVehicleTypeService vehicleTypeService;
    private final NbTransferCenterService transferCenterService;
    private final NbOrderService orderService;
    private final NbTransferBatchService transferBatchService;
    private final NbSortingCenterService sortingCenterService;
    private final CommonDataUtil commonDataUtil;
    private final Route4MeUtil route4MeUtil;
    private final Route4MeDriverUtil route4MeDriverUtil;
    private final RedisTemplate<String, String> redisTemplate;

    @Operation(summary = "校验邮箱" , description = "校验邮箱" )
    @PostMapping("/checkEmail" )
    public void checkEmail(@Valid @RequestBody APPDriverVo vo) {
        NbDriverEntity loginDriver = getLoginDriver();

        if (StrUtil.isNotBlank(loginDriver.getEmail())) {
            renderAppErr("-2", "Email cannot be modified");
        }
        String email = vo.getEmail();

        if (StrUtil.isBlank(email)) {
            renderAppErr("-1", "Email cannot be empty");
        }

        email = email.trim();
        // "select * from nb_driver where email = ? limit 1", email);
        NbDriverEntity driver = driverService.getOne(new LambdaQueryWrapper<NbDriverEntity>().eq(NbDriverEntity::getEmail, email));
        if (driver != null) {
            renderAppErr("-1", "Email already exist");
        }

        renderAppSuc();
    }

    /**
     * 更新个人信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Operation(summary = "更新个人信息" , description = "更新个人信息" )
    @PostMapping("/updateInfo" )
    public void updateInfo() {
        NbDriverEntity driver = getLoginDriver();

        if (driver.getAuditStatus() == DriverDto.AUDIT_STATUS_3_PASS) {
            renderAppErr("-1", "已通过不可修改");
        }

        if (driver.getAuditStatus() == DriverDto.AUDIT_STATUS_2_SUBMIT) {
            renderAppErr("-1", "审核中，不可修改");
        }

        String formData = getPara("formData");
        JSONObject formJo = JSONUtil.parseObj(formData);

        int countryId = jsonGetInt(formJo, "countryId", 0);
        int provinceId = jsonGetInt(formJo,"provinceId", 0);
        int cityId = jsonGetInt(formJo,"cityId", 0);

        String firstName = jsonGetStr(formJo, "firstName", null);
        String lastName = jsonGetStr(formJo, "lastName", null);
        String email = jsonGetStr(formJo, "email", null);
        String address = jsonGetStr(formJo, "address", null);
        String postalCode = jsonGetStr(formJo, "postalCode", null);
        String plateNumber = jsonGetStr(formJo, "plateNumber", null);
        int vehicleTypeId = jsonGetInt(formJo,"vehicleTypeId", 0);
        int workTypeId = jsonGetInt(formJo,"workTypeId", 0);
        int workHour = jsonGetInt(formJo,"workHour", 0);
        Boolean useEndLoc = formJo.getBool("useEndLoc");
        int tcId = jsonGetInt(formJo,"tcId", 0);
        if (tcId == 0) {
            renderAppErr("-1", "transit center must choose");
        }

        Double lat = formJo.getDouble("lat");
        Double lng = formJo.getDouble("lng");

        if (countryId == 0) {
            renderAppErr("-1", "Country can not be empty");
        }
        if (provinceId == 0) {
            renderAppErr("-1", "Province can not be empty");
        }
        if (cityId == 0) {
            renderAppErr("-1", "City can not be empty");
        }
        if (StrUtil.isBlank(address)) {
            renderAppErr("-1", "address can not be empty");
        }
        if (StrUtil.isBlank(postalCode)) {
            renderAppErr("-1", "postal code can not be empty");
        }
        if (vehicleTypeId == 0) {
            renderAppErr("-1", "vehicle can not be empty");
        }
        if (workTypeId == 0) {
            renderAppErr("-1", "work type can not be empty");
        }
        if (workTypeId == DriverDto.WORK_TYPE_2_PART_TIME) {
            if (workHour < 1 || workHour > 24) {
                renderAppErr("-1", "work hour invalid");
            }
        }

        String birthdayStr = jsonGetStr(formJo, "birthday", null);
        String sin = jsonGetStr(formJo, "sin", null);
        String emergencyContact = jsonGetStr(formJo, "emergencyContact", null);
        String emergencyContactTel = jsonGetStr(formJo, "emergencyContactTel", null);
        String insurancePolicy = jsonGetStr(formJo, "insurancePolicyFile", null);
        String vehicleRegDoc = jsonGetStr(formJo, "vehicleRegDocFile", null);
        String drivingLicense = jsonGetStr(formJo, "drivingLicenseFile", null);
        String carInsurance = jsonGetStr(formJo, "carInsuranceFile", null);
        int loadCapacity = jsonGetInt(formJo, "loadCapacity", 0);
        int contractType = jsonGetInt(formJo, "contractType", 0);

        Date birthday = Date.from(LocalDate.parse(birthdayStr, DateTimeFormatter.ofPattern("yyyy-MM-dd")).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        driver.setBirthday(birthday);
        driver.setSin(sin);
        driver.setEmergencyContact(emergencyContact);
        driver.setEmergencyContactTel(emergencyContactTel);
        driver.setInsurancePolicy(insurancePolicy);
        driver.setVehicleRegDoc(vehicleRegDoc);
        driver.setCarInsurance(carInsurance);
        driver.setDrivingLicenseFront(drivingLicense);
        driver.setLoadCapacity(loadCapacity);
        driver.setContractType(contractType);

        if (useEndLoc == null) {
            driver.setUseEndLoc(false);
        } else {
            driver.setUseEndLoc(useEndLoc);
        }

        if (lat != null && lng != null) {
            driver.setEndLocLat(lat);
            driver.setEndLocLng(lng);
        }

        driver.setCountryId(countryId);
        driver.setProvinceId(provinceId);
        driver.setCityId(cityId);
        driver.setFirstName(firstName);
        driver.setLastName(lastName);

        if (driver.getEmail() == null) {
            driver.setEmail(email);
        }

        driver.setAddress(address);
        driver.setPostalCode(postalCode);
        driver.setVehicleTypeId(vehicleTypeId);
        driver.setWorkTypeId(workTypeId);
        driver.setWorkHour(workHour);
        driver.setPlateNumber(plateNumber);
        driver.setAuditStatus(DriverDto.AUDIT_STATUS_2_SUBMIT);
        driver.setTcId(tcId);
        driverService.updateById(driver);

//		Route4MeKit.updateVehicle(driver);

        if (driver.getRoute4meMemberId() == 0) {
//			User user = Route4MeKit.createUser(driver);
//
//			if (user != null) {
//				String memberId = user.getMemberId();
//				driver.setRoute4meMemberId(Integer.valueOf(memberId));
//				driver.updateById(driver);
//			}
            // 后台审核通过时，才同步到route4me
        } else {
            // 更新
            if (driver.getAuditStatus() == DriverDto.AUDIT_STATUS_3_PASS) {
                // 审核成功的才同步更新
                User user = route4MeUtil.updateUser(driver);
            }
        }
        renderAppData(new DriverDto().toAppJson(driver));
    }

    @Operation(summary = "获取经纬度地址" , description = "获取经纬度地址" )
    @PostMapping("/addressToLatLng" )
    public void addressToLatLng(@RequestBody APPDriverVo vo) {
        String province = vo.getProvince();
        String city = vo.getCity();
        String address = vo.getAddress();
        String postalCode = vo.getPostalCode();

        String fullAddress = postalCode + " " + address + " " + city + " " + province;

        String api;
        try {
            api = String.format(NBDConstants.GEOCODE_API, URLEncoder.encode(fullAddress, "UTF-8"));
            log.info("googleApi:" + api);
            String ret = HttpKit.post(api, "");

            JSONObject jo = JSONUtil.parseObj(ret);
            JSONArray results = jo.getJSONArray("results");
            if (results.size() == 0) {
                renderAppErr("-1", "The entered address cannot be accurately located. Please provide the corrected address.");
            }

            JSONObject latlng = results.getJSONObject(0).getJSONObject("geometry").getJSONObject("location");
            double lat = latlng.getDouble("lat");
            double lng = latlng.getDouble("lng");

            JSONObject latlngJo = new JSONObject();
            latlngJo.set("lat", lat);
            latlngJo.set("lng", lng);

            renderAppData(latlngJo);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
    }

    //    @Clear(DriverLoginInterceptor.class)
    @Operation(summary = "regOptions" , description = "regOptions" )
    @PostMapping("/regOptions")
    public void regOptions() {
        // "select * from nb_vehicle_type where is_valid = true order by priority asc");
        List<NbVehicleTypeEntity> vehicleTypes = vehicleTypeService.list(new LambdaQueryWrapper<NbVehicleTypeEntity>()
                .eq(NbVehicleTypeEntity::getIsValid, true).orderByAsc(NbVehicleTypeEntity::getPriority));
        JSONArray vehicleJa = vehicleTypes.stream().map(vehicleType -> {
                    JSONObject jo = new JSONObject();
                    jo.set("id", vehicleType.getVehicleTypeId());
                    jo.set("name", vehicleType.getName());
                    jo.set("enName", vehicleType.getEnName());
                    return jo;
                }
        ).collect(Collectors.toCollection(JSONArray::new));

        JSONObject jo = new JSONObject();
        jo.set("vehicles", vehicleJa);

        JSONObject fullTime = new JSONObject();
        fullTime.set("enName", "Full Time");
        fullTime.set("name", "全职");
        fullTime.set("id", 1);
        JSONObject partTime = new JSONObject();
        partTime.set("enName", "Part Time");
        partTime.set("name", "兼职");
        partTime.set("id", 2);

        JSONArray ja = new JSONArray();
        ja.add(fullTime);
        ja.add(partTime);
        jo.set("workType", ja);

        JSONObject region = commonDataUtil.getCaRegion();
        jo.set("region", region);

        JSONArray contractTypeJa = new JSONArray();

        JSONObject contractTypeNormal = new JSONObject();
        contractTypeNormal.set("id", 1);
        contractTypeNormal.set("name", "common");
        contractTypeJa.add(contractTypeNormal);

        jo.set("contractTypes", contractTypeJa);

        renderAppData(jo);
    }

    /**
     * 验证码注册
     */
//    @Clear(DriverLoginInterceptor.class)
    @Transactional(rollbackFor = Exception.class)
    @AtomApi(params = "mobile")
    @Operation(summary = "验证码注册" , description = "验证码注册" )
    @PostMapping("/loginByCaptcha")
    public void loginByCaptcha(@RequestBody APPDriverVo vo) {
        String country = vo.getCountry();
        String mobile = vo.getMobile();
        String captcha = vo.getCaptcha();

        if (StrUtil.isAllNotBlank(country, mobile, captcha)) {
            String code_key = "captcha_" + country + "_" + mobile;
            String sysCaptcha = redisTemplate.opsForValue().get(code_key);
            // 校验验证码是否正确
            if (StrUtil.isBlank(sysCaptcha)) {
                renderAppErr("-2", "verification code has expired.");
            }

            if (!sysCaptcha.equals(captcha)) {
                renderAppErr("-3", "verification code invalid.");
            }

            // "select * from nb_driver where country = ? and mobile = ? limit 1", country, mobile);
            NbDriverEntity driver = driverService.getOne(new LambdaQueryWrapper<NbDriverEntity>().eq(NbDriverEntity::getCountry, country).eq(NbDriverEntity::getMobile, mobile));
            if (driver == null) {
                driver = new NbDriverEntity();
                driver.setCountry(country);
                driver.setMobile(mobile);
                driver.setRegTime(new Date());
                driver.setLastLoginTime(new Date());
                driver.setSessionId(AccessTokenBuilderKit.getAccessToken(getRequest()));
                driver.setLastLoginIp(IpKit.getRealIpV2(getRequest()));
                driverService.save(driver);
            } else {
                if (!driver.getIsValid()) {
                    renderAppErr("-20", "account invalid");
                }

                // 已经注册，直接登录
                driver.setLastLoginTime(new Date());
                driver.setSessionId(AccessTokenBuilderKit.getAccessToken(getRequest()));
                driver.setLastLoginIp(IpKit.getRealIpV2(getRequest()));
                driverService.updateById(driver);
            }

            driver = driverService.getById(driver.getDriverId());
            JSONObject jo = new DriverDto().toAppJson(driver);
            jo.set("sessionId", driver.getSessionId());

            renderAppData(jo);
        } else {
            renderAppErr("-1", "mobile must be enter");
        }
    }

    /**
     * app短信登录-图形验证码校验
     */
    @Operation(summary = "图形验证码校验" , description = "图形验证码校验" )
    @Inner(value = false)
    @PostMapping("/checkCaptcha")
    public R checkCaptcha(@RequestParam("randomStr") @NotNull(message = "唯一标识不能为空") String randomStr,@RequestParam("captcha") @NotNull(message = "验证码不能为空") String captcha) {
        String key = redisTemplate.opsForValue().get(CacheConstants.DEFAULT_CODE_KEY + randomStr);
        // 校验验证码是否正确
        if (key == null) {
            return LocalizedR.failed("verification.code.has.expired.please.obtain.the.verification.code.again", Optional.ofNullable(null));
        }

        if (!captcha.equalsIgnoreCase(key)) {
            return LocalizedR.failed("verification.code.is.incorrect", Optional.ofNullable(null));
        }

        return R.ok();
    }

    /**
     * 密码登录
     */
//    @Clear(DriverLoginInterceptor.class)
//    @Transactional(rollbackFor = Exception.class)
    @AtomApi(params = "mobile")
    @Operation(summary = "密码登录" , description = "密码登录" )
    @PostMapping("/login")
    public void login(@RequestBody APPDriverVo vo) {
        String country = vo.getCountry();
        String mobile = vo.getMobile();
        String password = vo.getPassword();

        // "select * from nb_driver where country = ? and mobile = ? limit 1", country, mobile);
        NbDriverEntity driver = driverService.getOne(new LambdaQueryWrapper<NbDriverEntity>().eq(NbDriverEntity::getCountry, country).eq(NbDriverEntity::getMobile, mobile));
        if (driver == null) {
            renderAppErr("-1", "Driver inexistence");
            return;
        }

        if (driver.getPassword() == null || !driver.getPassword().equals(password)) {
            renderAppErr("-1", "Incorrect password");
            return;
        }

        driver.setLastLoginTime(new Date());
        driver.setSessionId(AccessTokenBuilderKit.getAccessToken(getRequest()));
        driver.setLastLoginIp(IpKit.getRealIpV2(getRequest()));
        driverService.updateById(driver);

        JSONObject jo = new DriverDto().toAppJson(driver);
        jo.set("sessionId", driver.getSessionId());

        renderAppData(jo);
    }

    //    @Clear(DriverLoginInterceptor.class)
    @Operation(summary = "发送验证码" , description = "发送验证码" )
    @PostMapping("/sendCaptcha")
    public void sendCaptcha(@RequestBody APPDriverVo vo) {
        String country = vo.getCountry();
        String mobile = vo.getMobile();

        if (StrUtil.isAllNotBlank(country, mobile)) {
            String code_key = "captcha_" + country + "_" + mobile;
            String delay_key = "captcha_resend_" + country + "_" + mobile;

            Object obj = redisTemplate.opsForValue().get(delay_key);
            if (obj != null) {
                renderAppErr("-2", "You can only send once in a minute");
                return;
            }

            ALiYunSms aliYunSms = new ALiYunSms();
            String code = RandomStringUtils.randomNumeric(4);
            if (country.equals("+86")) {
//				Ret ret = sms.send(mobile, "1011874", new String[] {code, "5"});
                R ret = R.ok();
                if (ret.isOk()) {
//                    Redis.use().setex(code_key, RedisUtil.MINUTE_1 * 5, code);
//                    Redis.use().setex(delay_key, RedisUtil.MINUTE_1, System.currentTimeMillis());
                    redisTemplate.opsForValue().set(code_key, code, RedisUtil.MINUTE_1 * 5);
                    redisTemplate.opsForValue().set(delay_key, String.valueOf(System.currentTimeMillis()), RedisUtil.MINUTE_1);
                    System.out.println(code);
                    renderAppSuc();
                } else {
                    renderAppErr("-2", "send failure, try agein later.");
                }
            } else {
                // Ret ret = sms.twilioSend(mobile, "your verification code is: " + code);
                R ret = aliYunSms.sentMes(mobile, "your verification code is: " + code);
                if (ret.isOk()) {
                    redisTemplate.opsForValue().set(code_key, code, RedisUtil.MINUTE_1 * 5);
                    redisTemplate.opsForValue().set(delay_key, String.valueOf(System.currentTimeMillis()), RedisUtil.MINUTE_1);
                    renderAppSuc();
                } else {
                    renderAppErr("-2", "send failure, try agein later.");
                }
            }
        } else {
            renderAppErr("-1", "mobile must be enter");
        }
    }

    /**
     * 获取信息，用户展示
     */
    @Operation(summary = "获取信息" , description = "获取信息")
    @PostMapping("/info")
    public R info() {
        //获取正向单条司机登录信息
        NbDriverEntity driver = getLoginDriver();
        //获取正逆向多条司机登录信息
        List<NbDriverEntity> driverList = getLoginDriverList();

        JSONObject jo = new DriverDto().toAppJson(driver);
        jo.set("emergencyContact", driver.getEmergencyContact());
        jo.set("emergencyContactTel", driver.getEmergencyContactTel());
        jo.set("insurancePolicy", driver.getInsurancePolicy());
        jo.set("vehicleRegDoc", driver.getVehicleRegDoc());
        jo.set("drivingLicenseFront", driver.getDrivingLicenseFront());
        jo.set("drivingLicenseBack", driver.getDrivingLicenseBack());
        jo.set("carInsurance", driver.getCarInsurance());
        jo.set("sinPic", driver.getSinPic());
        jo.set("vcOrDdf", driver.getVcOrDdf());
        jo.set("licenseNumber", driver.getLicenseNumber());
        jo.set("licenseNumberExpire", driver.getLicenseNumberExpire() == null ? null : DateFormatUtils.format(driver.getLicenseNumberExpire(), "yyyy-MM-dd"));
        jo.set("socialNumber", driver.getSocialNumber());
        jo.set("refuseReason", driver.getRefuseReason());
        jo.set("maxVolume", driver.getR4mMaxVolume());
        jo.set("maxItems", driver.getR4mMaxItems());
        jo.set("maxWeight", driver.getR4mMaxWeight());
        jo.set("make", driver.getR4mVehicleMake());
        jo.set("fuel", driver.getR4mFuelType());
        jo.set("vehicleCapacityId", driver.getR4mVehicleCapacityProfileId());
        jo.set("email", driver.getEmail());
        jo.set("lastName", driver.getLastName());
        jo.set("firstName", driver.getFirstName());
        jo.set("middleName", driver.getMiddleName());

        jo.set("driverList",driverList);

        return R.ok(jo);
    }

    /**
     * 返回司机的转运中心信息
     */
    @Operation(summary = "返回司机的转运中心信息" , description = "返回司机的转运中心信息")
    @PostMapping("/transferCenterInfo")
    public void transferCenterInfo() {
        NbDriverEntity driver = getLoginDriver();
        int transferId = driver.getTcId();

        NbTransferCenterEntity tc = transferCenterService.getById(transferId);
        if (tc == null) {
            renderAppErr("-1", "Transit center not yet assigned.");
            return;
        }

        JSONObject jo = new JSONObject();
        jo.set("tcId", tc.getTcId());
        jo.set("code", tc.getTransferCenterCode());
        jo.set("provinceId", tc.getProvinceId());
        jo.set("province", commonDataUtil.getProvinceById(tc.getProvinceId()));
        jo.set("cityId", tc.getCityId());
        jo.set("city", commonDataUtil.getCityById(tc.getCityId()));
        jo.set("address", tc.getAddress());
        jo.set("postalCode", tc.getPostalCode());
        jo.set("centerName", tc.getCenterName());

        // "select count(1) cont from nb_order where driver_id = ? and order_status in (?) and delivery_status = ?",
        // driver.getDriverId(), Order.ORDER_STATUS_200_PARCEL_SCANNED, Order.DELIVERY_STATUS_1_UN_PICKUP).getInt("cont");
        Long count = orderService.count(new LambdaQueryWrapper<NbOrderEntity>().eq(NbOrderEntity::getDriverId,driver.getDriverId())
                .in(NbOrderEntity::getOrderStatus, OrderDto.ORDER_STATUS_200_PARCEL_SCANNED).eq(NbOrderEntity::getDeliveryStatus,OrderDto.DELIVERY_STATUS_1_UN_PICKUP));
        int unPickupTotal = Math.toIntExact(count);
        jo.set("unPickupTotal", unPickupTotal);

        //List<Record> records = Db.find("select count(order_id) orderTotal, batch_id from (select o.order_id, max(tbo.batch_id) batch_id from nb_order o, nb_transfer_batch_order tbo " +
        //    "where o.order_id = tbo.order_id and o.driver_id = ? and order_status in (?) and delivery_status = ? group by o.order_id " +
        //    ") t group by batch_id", driver.getDriverId(), OrderDto.ORDER_STATUS_200_PARCEL_SCANNED, OrderDto.DELIVERY_STATUS_1_UN_PICKUP);

        List<APPDriverDto> driverDtoList = new MPJLambdaWrapper<NbOrderEntity>()
                .select("COUNT(o.order_id) AS orderTotal", "tbo.batch_id AS batchId")
                .leftJoin(NbTransferBatchOrderEntity.class, NbTransferBatchOrderEntity::getOrderId, NbOrderEntity::getOrderId)
                .eq(NbOrderEntity::getDriverId, driver.getDriverId())
                .in(NbOrderEntity::getOrderStatus, OrderDto.ORDER_STATUS_200_PARCEL_SCANNED)
                .eq(NbOrderEntity::getDeliveryStatus, OrderDto.DELIVERY_STATUS_1_UN_PICKUP)
                .groupBy(NbTransferBatchOrderEntity::getBatchId)
                .list(APPDriverDto.class);

        JSONArray ja = new JSONArray();
        for (APPDriverDto dto : driverDtoList) {
            JSONObject innerJo = new JSONObject();
            innerJo.set("orderTotal", dto.getOrderTotal());

            NbTransferBatchEntity tb = transferBatchService.getById(dto.getBatchId());
            innerJo.set("batchNo", tb.getBatchNo());

            ja.add(innerJo);
        }

        jo.set("transferBatchs", ja);
        renderAppData(jo);
    }

    @Operation(summary = "根据市查询转运中心" , description = "根据市查询转运中心")
    @PostMapping("/listTransferCenterByCityId")
    public void listTransferCenterByCityId(@RequestBody APPDriverVo vo) {
        Integer cityId = vo.getCityId();

        // "select * from nb_transfer_center where is_valid = true and city_id = ?", cityId);
        List<NbTransferCenterEntity> centers = transferCenterService.list(new LambdaQueryWrapper<NbTransferCenterEntity>()
                .eq(NbTransferCenterEntity::getCityId,cityId).eq(NbTransferCenterEntity::getIsValid,true));
        JSONArray ja = centers.stream().map(this::toAppJson).collect(Collectors.toCollection(JSONArray::new));
        renderAppData(ja);
    }

    /**
     * 设置密码
     */
    @Transactional(rollbackFor = Exception.class)
    @Operation(summary = "设置密码" , description = "设置密码")
    @PostMapping("/setPassword")
    public void setPassword(@RequestBody APPDriverVo vo) {
        String password = vo.getPassword();

        NbDriverEntity loginDriver = getLoginDriver();
        String regStep = loginDriver.getRegStep();
        regStep = setChatAt(regStep, 0, '1');

        loginDriver.setRegStep(regStep);
        loginDriver.setPassword(password);
        driverService.updateById(loginDriver);

        log.info("司机driverId=" + loginDriver.getDriverId() + "设置密码");

        String target = new DriverDto().judgeNextUrl(loginDriver);

        if (loginDriver.getAuditStatus() == DriverDto.AUDIT_STATUS_3_PASS) {
            // 如果审核通过的，修改密码时，需要同步到route4me
            route4MeUtil.updateUser(loginDriver);
        }

        JSONObject ret = new JSONObject();
        ret.set("nextPage", target);

        renderAppData(ret);
    }

    /**
     * 设置查账密码
     */
    @Transactional(rollbackFor = Exception.class)
    @Operation(summary = "设置查账密码" , description = "设置查账密码")
    @PostMapping("/setSettlePassword")
    public void setSettlePassword(@RequestBody APPDriverVo vo) {
        String password = vo.getPassword();

        NbDriverEntity loginDriver = getLoginDriver();

        loginDriver.setSettlePassword(password);
        driverService.updateById(loginDriver);

        log.info("司机driverId=" + loginDriver.getDriverId() + "设置查账密码");

        String target = new DriverDto().judgeNextUrl(loginDriver);

        JSONObject ret = new JSONObject();
        ret.set("nextPage", target);

        renderAppData(ret);
    }


    /**
     * 根据状态查看包裹信息
     */
    @Operation(summary = "根据状态查看包裹信息" , description = "根据状态查看包裹信息" )
    @PostMapping("/statusinfo")
    public R statusinfo(@RequestParam("driverId") Integer driverId,@RequestParam("orderStatus") String orderStatus) {
        if (ObjectUtil.equals(driverId, "") || ObjectUtil.equals(driverId, 0)){
            return R.failed("-1", "driverId no is empty");
        }

        LambdaQueryWrapper<NbOrderEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(NbOrderEntity::getDriverId, driverId);
        if ("inDelivery".equals(orderStatus)) {
            qw.in(NbOrderEntity::getOrderStatus, OrderDto.ORDER_STATUS_203_LOAD_SCANNED, OrderDto.ORDER_STATUS_204_IN_TRANSIT).orderByAsc(NbOrderEntity::getPickNo);
                    //.eq(NbOrderEntity::getDeliveryStatus, OrderDto.DELIVERY_STATUS_3_START);
        } else if ("deliveryFail".equals(orderStatus)) {
            qw.in(NbOrderEntity::getOrderStatus, OrderDto.ORDER_STATUS_210_FAILED_DELIVERY,OrderDto.ORDER_STATUS_211_FAILED_DELIVERY_RETRY_1,OrderDto.ORDER_STATUS_212_FAILED_DELIVERY_RETRY_2
                    ,OrderDto.ORDER_STATUS_280_FAILURE, OrderDto.ORDER_STATUS_286_RETURN_OFFICE_FROM_TRANSIT).orderByAsc(NbOrderEntity::getPickNo);;
                   // .eq(NbOrderEntity::getDeliveryStatus, OrderDto.DELIVERY_STATUS_5_FAILURED);
        } else if ("unScan".equals(orderStatus)) {
            qw.in(NbOrderEntity::getOrderStatus, OrderDto.ORDER_STATUS_200_PARCEL_SCANNED, OrderDto.ORDER_STATUS_201_TO_TRANSIT_CENTER, OrderDto.ORDER_STATUS_202_ARRIVED_TRANSIT_CENTER);
        } else if ("scaned".equals(orderStatus)) {
            qw.in(NbOrderEntity::getOrderStatus, OrderDto.ORDER_STATUS_203_LOAD_SCANNED, OrderDto.ORDER_STATUS_204_IN_TRANSIT);
        } else if ("delivered".equals(orderStatus)) {
            qw.eq(NbOrderEntity::getOrderStatus, OrderDto.ORDER_STATUS_205_DELIVERED)
                    .eq(NbOrderEntity::getDeliveryStatus, OrderDto.DELIVERY_STATUS_4_FINISHED);
        }

        List<NbOrderEntity> orders = orderService.list(qw);
        //if (CollUtil.isEmpty(orders)) {
            //return R.failed("-1", "driverId not exist or There is no package in the current state." + driverId + ":" + orderStatus);
        //}
        JSONArray ja = orders.stream().map(o -> {
            JSONObject jo = new OrderDto().toDriverListJson(o);
            NbTransferCenterEntity tc = transferCenterService.getById(o.getTcId());
            jo.put("centerName",tc.getCenterName());
            return jo;
        }).collect(Collectors.toCollection(JSONArray::new));
        return R.ok(ja);
    }

    /**
     * 设置司机档案
     */
    @Transactional(rollbackFor = Exception.class)
    @Operation(summary = "设置司机档案" , description = "设置司机档案")
    @PostMapping("/setArchive")
    public void setArchive() {
        NbDriverEntity loginDriver = getLoginDriver();

        if (loginDriver.getAuditStatus() == DriverDto.AUDIT_STATUS_2_SUBMIT) {
            renderAppErr("-1", "审核中，不可修改");
            return;
        }

        String formData = getPara("formData");
        JSONObject formJo = JSONUtil.parseObj(formData);

        int countryId = jsonGetInt(formJo, "countryId", 0);
        int provinceId = jsonGetInt(formJo,"provinceId", 0);
        int cityId = jsonGetInt(formJo,"cityId", 0);

        String firstName = jsonGetStr(formJo, "firstName", null);
        String middleName = jsonGetStr(formJo, "middleName", null);
        String lastName = jsonGetStr(formJo, "lastName", null);
        String email = jsonGetStr(formJo, "email", null);
        String address = jsonGetStr(formJo, "address", null);
        String postalCode = jsonGetStr(formJo, "postalCode", null);
        int tcId = jsonGetInt(formJo,"tcId", 0);
        if (tcId == 0) {
            renderAppErr("-1", "transit center must choose");
        }
        if (countryId == 0) {
            renderAppErr("-1", "Country can not be empty");
            return;
        }
        if (provinceId == 0) {
            renderAppErr("-1", "Province can not be empty");
            return;
        }
        if (cityId == 0) {
            renderAppErr("-1", "City can not be empty");
            return;
        }
        if (StrUtil.isBlank(address)) {
            renderAppErr("-1", "address can not be empty");
            return;
        }
        if (StrUtil.isBlank(postalCode)) {
            renderAppErr("-1", "postal code can not be empty");
            return;
        }

        if (StrUtil.isBlank(loginDriver.getEmail())) {
            // 为空才能进行修改
            if (StrUtil.isBlank(email)) {
                renderAppErr("-1", "Email cannot be empty");
                return;
            }

            email = email.trim();
            // "select * from nb_driver where email = ? limit 1", email);
            NbDriverEntity driver = driverService.getOne(new LambdaQueryWrapper<NbDriverEntity>().eq(NbDriverEntity::getEmail, email));
            if (driver != null) {
                renderAppErr("-1", "Email already exist");
                return;
            }
        }

        if (email.contains("qq.com")) {
            renderAppErr("-1", "qq邮箱不能使用，请更换");
            return;
        }

        loginDriver.setFirstName(firstName);
        loginDriver.setMiddleName(middleName);
        loginDriver.setLastName(lastName);
        loginDriver.setEmail(email);
        loginDriver.setCountryId(countryId);
        loginDriver.setProvinceId(provinceId);
        loginDriver.setCityId(cityId);
        loginDriver.setAddress(address);
        loginDriver.setPostalCode(postalCode);
        loginDriver.setTcId(tcId);

        NbTransferCenterEntity tc = transferCenterService.getById(tcId);
        loginDriver.setScId(tc.getScId());

        String regStep = loginDriver.getRegStep();

        regStep = setChatAt(regStep, 1, '1');
        loginDriver.setRegStep(regStep);
        driverService.updateById(loginDriver);

        String nextUrl = new DriverDto().judgeNextUrl(loginDriver);
        JSONObject ret = new JSONObject();
        ret.set("nextPage", nextUrl);

        renderAppData(ret);
    }

    /**
     * 录入司机信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Operation(summary = "录入司机信息" , description = "录入司机信息")
    @PostMapping("/setProfile")
    public void setProfile() {
        NbDriverEntity loginDriver = getLoginDriver();
        if (loginDriver.getAuditStatus() == DriverDto.AUDIT_STATUS_2_SUBMIT) {
            renderAppErr("-1", "审核中，不可修改");
            return;
        }

        String formData = getPara("formData");
        JSONObject formJo = JSONUtil.parseObj(formData);

        String licenseNumber = jsonGetStr(formJo, "licenseNumber", null);
        String licenseNumberExpire = jsonGetStr(formJo, "licenseNumberExpire", null);
        String plateNumber = jsonGetStr(formJo, "plateNumber", null);
        String emergencyContact = jsonGetStr(formJo, "emergencyContact", null);
        String emergencyContactTel = jsonGetStr(formJo, "emergencyContactTel", null);
        String socialNumber = jsonGetStr(formJo, "socialNumber", null);
        int vehicleTypeId = jsonGetInt(formJo,"vehicleTypeId", 0);

        if (vehicleTypeId == 0) {
            renderAppErr("-1", "vehicle can not be empty");
            return;
        }

        if (StrUtil.isBlank(licenseNumber)) {
            renderAppErr("-1", "licenseNumber can not be empty");
            return;
        }

        if (StrUtil.isBlank(licenseNumberExpire)) {
            renderAppErr("-1", "licenseNumberExpire can not be empty");
            return;
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date licenseNumberExpireDate = null;
        try {
            licenseNumberExpireDate = sdf.parse(licenseNumberExpire);
        } catch (ParseException e) {
            e.printStackTrace();
            renderAppErr("-1", "licenseNumberExpire format invalid");
            return;
        }
        if (StrUtil.isBlank(plateNumber)) {
            renderAppErr("-1", "plateNumber can not be empty");
            return;
        }
        if (StrUtil.isBlank(emergencyContact)) {
            renderAppErr("-1", "emergencyContact can not be empty");
            return;
        }
        if (StrUtil.isBlank(emergencyContactTel)) {
            renderAppErr("-1", "emergencyContactTel can not be empty");
            return;
        }
        if (StrUtil.isBlank(socialNumber)) {
            renderAppErr("-1", "socialNumber can not be empty");
            return;
        }

        loginDriver.setLicenseNumber(licenseNumber);
        loginDriver.setLicenseNumberExpire(licenseNumberExpireDate);
        loginDriver.setPlateNumber(plateNumber);
        loginDriver.setEmergencyContact(emergencyContact);
        loginDriver.setEmergencyContactTel(emergencyContactTel);
        loginDriver.setVehicleTypeId(vehicleTypeId);
        loginDriver.setSocialNumber(socialNumber);

        String regStep = loginDriver.getRegStep();

        regStep = setChatAt(regStep, 2, '1');
        loginDriver.setRegStep(regStep);

        driverService.updateById(loginDriver);

        String nextUrl = new DriverDto().judgeNextUrl(loginDriver);
        JSONObject ret = new JSONObject();
        ret.set("nextPage", nextUrl);

        renderAppData(ret);
    }

    /**
     * 设置车辆
     */
    @Transactional(rollbackFor = Exception.class)
    @Operation(summary = "设置车辆" , description = "设置车辆")
    @PostMapping("/setVehicle")
    public void setVehicle(@RequestBody APPDriverVo vo) {
        String formData = vo.getFormData();
        JSONObject jo = JSONUtil.parseObj(formData);

        Integer vehicleCapacityId = jsonGetInt(jo, "vehicleCapacityId", null);
        if (vehicleCapacityId == null) {
            renderAppErr("-1", "Vehicle Capacity 为必填项");
            return;
        }
        String makeId = jsonGetStr(jo, "makeId", null);
        String fuelId = jsonGetStr(jo, "fuelId", null);

        NbDriverEntity loginDriver = getLoginDriver();

        loginDriver.setR4mVehicleCapacityProfileId(vehicleCapacityId.toString());
        loginDriver.setR4mVehicleMake(makeId);
        loginDriver.setR4mFuelType(fuelId);

        if (loginDriver.getAuditStatus() == DriverDto.AUDIT_STATUS_3_PASS) {
            // 更新车辆载货能力
            VehicleCapacity vc = new VehicleCapacity();
            vc.setName("Driver" + loginDriver.getDriverId());
            vc.setMaxVolume(loginDriver.getR4mMaxVolume());
            vc.setMaxItemsNumber(loginDriver.getR4mMaxItems());
            vc.setMaxWeight(loginDriver.getR4mMaxWeight());

            NbSortingCenterEntity sc = sortingCenterService.getById(loginDriver.getScId());
            NbTransferCenterEntity tc = transferCenterService.getById(loginDriver.getTcId());
            // 更新车辆信息
            Vehicles vehicle = new Vehicles();
            vehicle.setMemberId(loginDriver.getRoute4meMemberId().toString());
            vehicle.setVehicleAlias(sc.getScCode() + "-" + tc.getTransferCenterCode() + "-" + loginDriver.getDriverId() + "-" + loginDriver.getFirstName() + " " + loginDriver.getLastName());
//			vehicle.setVehicleVin(driver.getv);
            if (loginDriver.getLicenseNumber() != null) {
                vehicle.setVehicleLicensePlate(loginDriver.getLicenseNumber().toUpperCase());
            }

            if (loginDriver.getVehicleTypeId() != null) {
                NbVehicleTypeEntity vt = vehicleTypeService.getById(loginDriver.getVehicleTypeId());
                vehicle.setVehicleTypeId(vt.getRoute4meKey());
            }
            vehicle.setVehicleMake(loginDriver.getR4mVehicleMake());
            if (loginDriver.getR4mFuelType() != null) {
                vehicle.setFuelType(loginDriver.getR4mFuelType().toLowerCase());
            }
            vehicle.setVehicleCapacityProfileId(Integer.valueOf(loginDriver.getR4mVehicleCapacityProfileId()));

            if (StrUtil.isBlank(loginDriver.getRoute4meVehicleId())) {
                R ret = route4MeDriverUtil.createVehicle(vehicle);
                if (ret.isOk()) {
                    vehicle = (Vehicles) ret.getData();
                    loginDriver.setRoute4meVehicleId(vehicle.getVehicleId());
                } else {
                    renderAppErr("-10", ret.failed().getMsg());
                    return;
                }
            } else {
                vehicle.setVehicleId(loginDriver.getRoute4meVehicleId());
                R ret = route4MeDriverUtil.updateVehicle(vehicle);
                if (ret.isOk()) {
                    vehicle = (Vehicles) ret.getData();
                } else {
                    renderAppErr("-10", ret.failed().getMsg());
                    return;
                }
            }
        }

        String regStep = loginDriver.getRegStep();
        regStep = setChatAt(regStep, 3, '1');
        loginDriver.setRegStep(regStep);
        driverService.updateById(loginDriver);

        String nextUrl = new DriverDto().judgeNextUrl(loginDriver);
        JSONObject ret = new JSONObject();
        ret.set("nextPage", nextUrl);

        renderAppData(ret);
    }

    /**
     * 上传文件
     */
    @Transactional(rollbackFor = Exception.class)
    @Operation(summary = "上传文件" , description = "上传文件")
    @PostMapping("/setFile")
    public void setFile() {
        NbDriverEntity loginDriver = getLoginDriver();
        if (loginDriver.getAuditStatus() == DriverDto.AUDIT_STATUS_2_SUBMIT) {
            renderAppErr("-1", "审核中，不可修改");
            return;
        }

        String formData = getPara("formData");
        JSONObject formJo = JSONUtil.parseObj(formData);

        String drivingLicenseFrontFile = jsonGetStr(formJo, "drivingLicenseFront", null);
        String drivingLicenseBackFile = jsonGetStr(formJo, "drivingLicenseBack", null);
        String insurancePolicyFile = jsonGetStr(formJo, "insurancePolicy", null);
        String sinPic = jsonGetStr(formJo, "sinPic", null);
        String vcOrDdf = jsonGetStr(formJo, "vcOrDdf", null);

        if (StrUtil.isBlank(drivingLicenseFrontFile)) {
            renderAppErr("-1", "驾照正面没有上传");
            return;
        }
        if (StrUtil.isBlank(drivingLicenseBackFile)) {
            renderAppErr("-1", "驾照背面没有上传");
            return;
        }
        if (StrUtil.isBlank(insurancePolicyFile)) {
            renderAppErr("-1", "保险单没有上传");
            return;
        }
        if (StrUtil.isBlank(sinPic)) {
            renderAppErr("-1", "社保号图片没有上传");
            return;
        }
        if (StrUtil.isBlank(vcOrDdf)) {
            renderAppErr("-1", "void cheque or direct deposit form required.");
            return;
        }

        loginDriver.setInsurancePolicy(insurancePolicyFile);
        loginDriver.setDrivingLicenseFront(drivingLicenseFrontFile);
        loginDriver.setDrivingLicenseBack(drivingLicenseBackFile);
        loginDriver.setSinPic(sinPic);
        loginDriver.setVcOrDdf(vcOrDdf);

        String regStep = loginDriver.getRegStep();
        regStep = setChatAt(regStep, 4, '1');
        loginDriver.setRegStep(regStep);
        driverService.updateById(loginDriver);

        String nextUrl = new DriverDto().judgeNextUrl(loginDriver);
        JSONObject ret = new JSONObject();
        ret.set("nextPage", nextUrl);

        renderAppData(ret);
    }

    /**
     * 同意条款
     */
    @Transactional(rollbackFor = Exception.class)
    @Operation(summary = "同意条款" , description = "同意条款")
    @PostMapping("/setAgreement")
    public void setAgreement() {
        NbDriverEntity loginDriver = getLoginDriver();
        if (loginDriver.getAuditStatus() == DriverDto.AUDIT_STATUS_3_PASS) {
            renderAppErr("-1", "已通过不可修改");
            return;
        }

        if (loginDriver.getAuditStatus() == DriverDto.AUDIT_STATUS_2_SUBMIT) {
            renderAppErr("-1", "审核中，不可修改");
            return;
        }

        String regStep = loginDriver.getRegStep();
        regStep = setChatAt(regStep, 5, '1');
        loginDriver.setRegStep(regStep);
        loginDriver.setAuditStatus(DriverDto.AUDIT_STATUS_2_SUBMIT);
        driverService.updateById(loginDriver);

        String nextUrl = new DriverDto().judgeNextUrl(loginDriver);
        JSONObject ret = new JSONObject();
        ret.set("nextPage", nextUrl);

        renderAppData(ret);
    }

    /**
     * 拒绝同意条款
     */
    @Transactional(rollbackFor = Exception.class)
    @Operation(summary = "拒绝同意条款" , description = "拒绝同意条款")
    @PostMapping("/setRefuseUnderstand")
    public void setRefuseUnderstand() {
        NbDriverEntity loginDriver = getLoginDriver();
        if (loginDriver.getAuditStatus() == DriverDto.AUDIT_STATUS_3_PASS) {
            renderAppErr("-1", "已通过不可修改");
            return;
        }
        if (loginDriver.getAuditStatus() == DriverDto.AUDIT_STATUS_2_SUBMIT) {
            renderAppErr("-1", "审核中，不可修改");
            return;
        }
        if (loginDriver.getAuditStatus() != DriverDto.AUDIT_STATUS_4_REFUSE) {
            renderAppErr("-1", "无法进行该操作");
            return;
        }
        loginDriver.setAuditStatus(DriverDto.AUDIT_STATUS_1_CREATE);
        driverService.updateById(loginDriver);

        String nextUrl = new DriverDto().judgeNextUrl(loginDriver);
        JSONObject ret = new JSONObject();
        ret.set("nextPage", nextUrl);

        renderAppData(ret);
    }

    /**
     * 车辆选项
     */
    @Operation(summary = "车辆选项" , description = "车辆选项")
    @PostMapping("/vehicleOptions")
    public void vehicleOptions() {
        JSONArray makeJa = new JSONArray();
        makeJa.add("Acura");
        makeJa.add("Alfa-Romeo");
        makeJa.add("American Coleman");
        makeJa.add("Aston-Martin");
        makeJa.add("Audi");
        makeJa.add("Bentley");
        makeJa.add("Bugatti");
        makeJa.add("Buick");
        makeJa.add("BMW");
        makeJa.add("Cadillac");
        makeJa.add("Chevrolet");
        makeJa.add("Chrysler");
        makeJa.add("Citroen");
        makeJa.add("Dodge");
        makeJa.add("Eicher");
        makeJa.add("Ferrari");
        makeJa.add("Fiat");
        makeJa.add("Ford");
        makeJa.add("Freightliner");
        makeJa.add("Geely");
        makeJa.add("Genesis");
        makeJa.add("GMC");
        makeJa.add("Hino");
        makeJa.add("Honda");
        makeJa.add("Hyundai");
        makeJa.add("Infiniti");
        makeJa.add("International trucks");
        makeJa.add("Isuzu");
        makeJa.add("Jaguar");
        makeJa.add("Jeep");
        makeJa.add("Kenworth");
        makeJa.add("Kia");
        makeJa.add("Koenigsegg");
        makeJa.add("Lamborghini");
        makeJa.add("Lancia");
        makeJa.add("Land Rover");
        makeJa.add("Lexus");
        makeJa.add("Lincoln");
        makeJa.add("Lotus");
        makeJa.add("Mack");
        makeJa.add("Mahindra");
        makeJa.add("Maserati");
        makeJa.add("Maybach");
        makeJa.add("Mazda");
        makeJa.add("McLaren");
        makeJa.add("Mercedes-Benz");
        makeJa.add("Mini");
        makeJa.add("Mitsubishi");
        makeJa.add("Navistar");
        makeJa.add("Nissan");
        makeJa.add("Opel");
        makeJa.add("Pagani");
        makeJa.add("Peugeot");
        makeJa.add("Peterbilt");
        makeJa.add("Pontiac");
        makeJa.add("Porsche");
        makeJa.add("Ram");
        makeJa.add("Renault");
        makeJa.add("Rolls-Royce");
        makeJa.add("Scania");
        makeJa.add("Skoda");
        makeJa.add("Smart");
        makeJa.add("Sterling");
        makeJa.add("Subaru");
        makeJa.add("Suzuki");
        makeJa.add("Tata");
        makeJa.add("Taurus");
        makeJa.add("Tesla");
        makeJa.add("Toyota");
        makeJa.add("Volkswagen");
        makeJa.add("Volvo");
        makeJa.add("Western Star");

        JSONArray vehicleTypeJa = new JSONArray();
        vehicleTypeJa.add("18 wheeler");
        vehicleTypeJa.add("Big Rig");
        vehicleTypeJa.add("Cabin");
        vehicleTypeJa.add("Cement Mixer");
        vehicleTypeJa.add("Coupe");
        vehicleTypeJa.add("Dairy");
        vehicleTypeJa.add("Hatchback");
        vehicleTypeJa.add("Livestock Carrier");
        vehicleTypeJa.add("Motorcycle");
        vehicleTypeJa.add("Pickup Truck");
        vehicleTypeJa.add("Sedan");
        vehicleTypeJa.add("Suv");
        vehicleTypeJa.add("Tractor Trailer");
        vehicleTypeJa.add("Tree Cutting");
        vehicleTypeJa.add("Van");
        vehicleTypeJa.add("Waste Disposal");

        JSONArray fuelTypeJa = new JSONArray();
        fuelTypeJa.add("Unleaded 87");
        fuelTypeJa.add("Unleaded 89");
        fuelTypeJa.add("Unleaded 91");
        fuelTypeJa.add("Unleaded 93");
        fuelTypeJa.add("Diesel");
        fuelTypeJa.add("Electric");
        fuelTypeJa.add("Hybrid");

        JSONObject ret = new JSONObject();
        ret.set("makes", makeJa);
        ret.set("vehicleTypes", vehicleTypeJa);
        ret.set("fuelTypes", fuelTypeJa);

        JSONArray vcJa = new JSONArray();
        V5Array<VehicleCapacity> vcs = route4MeUtil.listVehicleCapacity();
        if (vcs != null) {
            List<VehicleCapacity> vcList = vcs.getData();
            for (VehicleCapacity vc : vcList) {
                String name = vc.getName();
                Integer id = vc.getVehicleCapacityProfileId();

                JSONObject vcObject = new JSONObject();
                vcObject.set("id", id);
                vcObject.set("name", name);

                vcJa.add(vcObject);
            }
        }
        ret.set("vehicleCapacitys", vcJa);

        renderAppData(ret);
    }

    private static String setChatAt(String str, int index, char tarStr) {
        if (str.length() < index + 1) {
            throw new RuntimeException("长度溢出");
        }

        StringBuffer sb = new StringBuffer(str.length());
        for (int i=0; i<str.length(); i++) {
            if (i == index) {
                sb.append(tarStr);
            } else {
                sb.append(str.charAt(i));
            }
        }
        return sb.toString();
    }

    private Integer jsonGetInt(JSONObject json, String key, Integer defaultValue) {
        return Optional.ofNullable(json.getInt(key)).orElse(defaultValue);
    }

    private String jsonGetStr(JSONObject json, String key, String defaultValue) {
        return Optional.ofNullable(json.getStr(key)).orElse(defaultValue);
    }

    private JSONObject toAppJson(NbTransferCenterEntity nbTransferCenterEntity) {
        JSONObject jo = new JSONObject();
        jo.set("tcId", nbTransferCenterEntity.getTcId());
        jo.set("code", nbTransferCenterEntity.getTransferCenterCode());
        jo.set("provinceId", nbTransferCenterEntity.getProvinceId());
        jo.set("province", commonDataUtil.getProvinceById(nbTransferCenterEntity.getProvinceId()));
        jo.set("cityId", nbTransferCenterEntity.getCityId());
        jo.set("city", commonDataUtil.getCityById(nbTransferCenterEntity.getCityId()));
        jo.set("address", nbTransferCenterEntity.getAddress());
        jo.set("postalCode", nbTransferCenterEntity.getPostalCode());
        jo.set("centerName", nbTransferCenterEntity.getCenterName());
        return jo;
    }

}
