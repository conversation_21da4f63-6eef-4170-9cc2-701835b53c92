{
  /*
   * ENVIRONMENTS
   * =================
   */

  // Define globals exposed by modern browsers.
  "browser": true,

  // Define globals exposed by jQuery.
  "jquery": true,

  // Define globals exposed by Node.js.
  "node": true,

  // Allow ES6.
  "esversion": 6,

  /*
   * ENFORCING OPTIONS
   * =================
   */

  // Force all variable names to use either camelCase style or UPPER_CASE
  // with underscores.
  "camelcase": true,

  // Prohibit use of == and != in favor of === and !==.
  "eqeqeq": true,

  // Enforce tab width of 2 spaces.
  "indent": 2,

  // Prohibit use of a variable before it is defined.
  "latedef": true,

  // Enforce line length to 100 characters
  "maxlen": 100,

  // Require capitalized names for constructor functions.
  "newcap": true,

  // Enforce use of single quotation marks for strings.
  "quotmark": "single",

  // Enforce placing 'use strict' at the top function scope
  // 前端项目中外层使用 strict 即可，覆盖此条规则
  "strict": false,

  // Prohibit use of explicitly undeclared variables.
  "undef": true,

  // Warn when variables are defined but never used.
  "unused": true,

  /*
   * RELAXING OPTIONS
   * =================
   */

  // Suppress warnings about == null comparisons.
  "eqnull": true,
  "globals": {
    "$": false,
    "angular": false
  }
}