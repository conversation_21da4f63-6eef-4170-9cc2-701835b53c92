Type,Package,License
npm,angular,MIT License
npm,angular-animate,MIT License
npm,angular-bootstrap,MIT License
npm,angular-clipboard,MIT License
npm,angular-cookies,MIT License
npm,angular-date-time-input,MIT License
npm,angular-loading-bar,MIT License
npm,angular-mocks,MIT License
npm,angular-resource,MIT License
npm,angular-route,MIT License
npm,angular-selectize2,MIT License
npm,angular-table-resize,MIT License
npm,angular-touch,MIT License
npm,angular-ui-notification,MIT License
npm,angular-ui-router,MIT License
npm,angular-utils-pagination,MIT License
npm,angularjs-bootstrap-datetimepicker,MIT License
npm,bootstrap-switch,Apache License 2.0
npm,bootstrap-tagsinput,MIT License
npm,moment,MIT License
npm,ng-dialog,MIT License
npm,ng-tags-input,MIT License
npm,oclazyload,MIT License
npm,selectize,Apache License 2.0
lib,jsTreeTable,MIT License