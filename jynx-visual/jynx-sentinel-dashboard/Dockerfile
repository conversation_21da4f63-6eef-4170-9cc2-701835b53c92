FROM registry.cn-hangzhou.aliyuncs.com/dockerhub_mirror/java:1.8-full

MAINTAINER <EMAIL>

ENV TZ=Asia/Shanghai
ENV JAVA_OPTS="-Xms128m -Xmx256m -Djava.security.egd=file:/dev/./urandom"

RUN ln -sf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

RUN mkdir -p /jynx-sentinel-dashboard

WORKDIR /jynx-sentinel-dashboard

EXPOSE 5020

ADD ./target/jynx-sentinel-dashboard.jar ./

CMD sleep 120;java $JAVA_OPTS -jar jynx-sentinel-dashboard.jar
